{"version": 3, "sources": ["configuration/languageDescription.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDhG,gDAEC;AAED,oEAEC;AAED,kFAEC;AAED,kFAEC;AApED,+BAAgC;AAEhC,2DAA6C;AAOhC,QAAA,sBAAsB,GAAG,8EAA8D,CAAC;AAaxF,QAAA,4BAA4B,GAA0B;IAClE;QACC,EAAE,EAAE,YAAY;QAChB,eAAe,EAAE,YAAY;QAC7B,gBAAgB,EAAE,IAAI;QACtB,kBAAkB,uCAA+B;QACjD,WAAW,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,eAAe,CAAC;QAClE,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE;YACvB,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;SACL;KACD,EAAE;QACF,EAAE,EAAE,YAAY;QAChB,eAAe,EAAE,YAAY;QAC7B,gBAAgB,EAAE,IAAI;QACtB,kBAAkB,uCAA+B;QACjD,WAAW,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,eAAe,CAAC;QAClE,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE;YACvB,IAAI;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;SACL;KACD;CACD,CAAC;AAEF,SAAgB,kBAAkB,CAAC,QAAgB;IAClD,OAAO,0BAA0B,CAAC,IAAI,CAAC,IAAA,eAAQ,EAAC,QAAQ,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED,SAAgB,4BAA4B,CAAC,QAAgB;IAC5D,OAAO,6BAA6B,CAAC,IAAI,CAAC,IAAA,eAAQ,EAAC,QAAQ,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAgB,mCAAmC,CAAC,QAAoB;IACvE,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC;AAED,SAAgB,mCAAmC,CAAC,QAAoB;IACvE,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnD,CAAC", "file": "languageDescription.js", "sourceRoot": "../../src/"}