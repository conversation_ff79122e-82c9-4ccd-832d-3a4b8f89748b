{"version": 3, "sources": ["languageFeatures/fixAll.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiPhG,4BAYC;AA3PD,+CAAiC;AAEjC,4EAA8D;AAC9D,wEAA0D;AAE1D,kEAAoD;AACpD,4DAAkF;AAGlF,wEAA8F;AAQ9F,KAAK,UAAU,oBAAoB,CAClC,KAAyB,EACzB,IAA0B,EAC1B,MAAgC,EAChC,IAAY,EACZ,WAAyC,EACzC,KAA+B;IAE/B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACtC,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,KAAK,EAAE,CAAC;YACxC,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBACnC,OAAO;YACR,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAc,CAAC,EAAE,CAAC;gBAC3C,SAAS;YACV,CAAC;YAED,MAAM,IAAI,GAA6B;gBACtC,GAAG,cAAc,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC;gBACtE,UAAU,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAK,CAAC,CAAC;aACjC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAClC,SAAS;YACV,CAAC;YAED,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;YAChE,IAAI,GAAG,EAAE,CAAC;gBACT,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC1E,MAAM;YACP,CAAC;QACF,CAAC;IACF,CAAC;AACF,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC9B,KAAyB,EACzB,IAA0B,EAC1B,MAAgC,EAChC,IAAY,EACZ,WAAyC,EACzC,KAA+B;IAE/B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACtC,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,KAAK,EAAE,CAAC;YACxC,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBACnC,OAAO;YACR,CAAC;YAED,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAc,CAAC,EAAE,CAAC;gBAC3C,SAAS;YACV,CAAC;YAED,MAAM,IAAI,GAA6B;gBACtC,GAAG,cAAc,CAAC,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC;gBACtE,UAAU,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,IAAK,CAAC,CAAC;aACjC,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACnE,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAC5D,SAAS;YACV,CAAC;YAED,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;YAChE,IAAI,CAAC,GAAG,EAAE,CAAC;gBACV,SAAS;YACV,CAAC;YAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBAChB,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC1E,OAAO;YACR,CAAC;YAED,MAAM,YAAY,GAAwC;gBACzD,KAAK,EAAE;oBACN,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,EAAE,IAAI,EAAE;iBACd;gBACD,KAAK,EAAE,GAAG,CAAC,KAAK;aAChB,CAAC;YAEF,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;YACzF,IAAI,gBAAgB,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC;gBACpE,OAAO;YACR,CAAC;YAED,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5F,OAAO;QACR,CAAC;IACF,CAAC;AACF,CAAC;AAED,yBAAyB;AAEzB,MAAe,YAAa,SAAQ,MAAM,CAAC,UAAU;CAOpD;AAED,MAAM,YAAa,SAAQ,YAAY;IAItC;QACC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,8BAA8B,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,MAAgC,EAAE,IAAY,EAAE,WAAyC,EAAE,KAA+B;QACrI,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QAEvC,MAAM,oBAAoB,CAAC;YAC1B,EAAE,KAAK,EAAE,UAAU,CAAC,8BAA8B,EAAE,OAAO,EAAE,QAAQ,CAAC,mCAAmC,EAAE;YAC3G,EAAE,KAAK,EAAE,UAAU,CAAC,gCAAgC,EAAE,OAAO,EAAE,QAAQ,CAAC,mBAAmB,EAAE;SAC7F,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;QAEhD,MAAM,gBAAgB,CAAC;YACtB,EAAE,KAAK,EAAE,UAAU,CAAC,eAAe,EAAE,OAAO,EAAE,QAAQ,CAAC,eAAe,EAAE;SACxE,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;;AAjBe,iBAAI,GAAG,MAAM,CAAC,cAAc,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAoBxE,MAAM,kBAAmB,SAAQ,YAAY;IAI5C;QACC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,wBAAwB,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,MAAgC,EAAE,IAAY,EAAE,WAAyC,EAAE,KAA+B;QACrI,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QACvC,MAAM,gBAAgB,CAAC;YACtB,EAAE,KAAK,EAAE,UAAU,CAAC,4BAA4B,EAAE,OAAO,EAAE,QAAQ,CAAC,gBAAgB,EAAE;SACtF,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IACjD,CAAC;;AAXe,uBAAI,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAczF,MAAM,uBAAwB,SAAQ,YAAY;IAIjD;QACC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB,CAAC,EAAE,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAC/E,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,MAAgC,EAAE,IAAY,EAAE,WAAyC,EAAE,KAA+B;QACrI,IAAI,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QACvC,MAAM,gBAAgB,CAAC;YACtB,EAAE,KAAK,EAAE,UAAU,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,SAAS,EAAE;SACjE,EACA,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAC/C,CAAC;;AAZe,4BAAI,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAe9F,YAAY;AAEZ,MAAM,yBAAyB;IAQ9B,YACkB,MAAgC,EAChC,wBAAkD,EAClD,kBAAsC;QAFtC,WAAM,GAAN,MAAM,CAA0B;QAChC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,uBAAkB,GAAlB,kBAAkB,CAAoB;IACpD,CAAC;IAEL,IAAW,QAAQ;QAClB,OAAO;YACN,uBAAuB,EAAE,yBAAyB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SACjF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC9B,QAA6B,EAC7B,MAAoB,EACpB,OAAiC,EACjC,KAA+B;QAE/B,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC7E,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACzE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;YACzB,oEAAoE;YACpE,OAAO,OAAO,CAAC;QAChB,CAAC;QAED,MAAM,IAAI,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAEpF,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACnC,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAE9F,OAAO,OAAO,CAAC;IAChB,CAAC;IAEO,gBAAgB,CAAC,IAA2B;QACnD,OAAO,yBAAyB,CAAC,aAAa;aAC5C,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;aAClD,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC;IACnC,CAAC;;AAvDuB,uCAAa,GAAG;IACvC,YAAY;IACZ,kBAAkB;IAClB,uBAAuB;CACvB,CAAC;AAsDH,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,MAAgC,EAChC,wBAAkD,EAClD,kBAAsC;IAEtC,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACxD,EAAE,GAAG,EAAE;QACP,MAAM,QAAQ,GAAG,IAAI,yBAAyB,CAAC,MAAM,EAAE,wBAAwB,EAAE,kBAAkB,CAAC,CAAC;QACrG,OAAO,MAAM,CAAC,SAAS,CAAC,2BAA2B,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACrG,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "fixAll.js", "sourceRoot": "../../src/"}