{"version": 3, "sources": ["languageFeatures/util/snippetForFunctionCall.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhG,wDAkBC;AAtBD,+CAAiC;AAEjC,+EAAiE;AAEjE,SAAgB,sBAAsB,CACrC,IAAmE,EACnE,YAAoD;IAEpD,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QAC5D,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;IACxD,CAAC;IAED,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;IAC/D,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;IAC3C,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;IACxD,wBAAwB,CAAC,OAAO,EAAE,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAClE,IAAI,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;QAC9C,OAAO,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IACD,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACxB,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IACzB,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,kBAAkB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1H,CAAC;AAED,SAAS,wBAAwB,CAChC,OAA6B,EAC7B,KAA6C,EAC7C,MAAc;IAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACvC,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;IACF,CAAC;AACF,CAAC;AAOD,SAAS,qBAAqB,CAC7B,YAAoD;IAEpD,MAAM,KAAK,GAA8B,EAAE,CAAC;IAC5C,IAAI,cAAc,GAA8B,EAAE,CAAC;IACnD,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAClC,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,KAAK,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;QACrD,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7B,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;YACvC,KAAK,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC;YACzC,KAAK,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC;YACjC,KAAK,MAAM,CAAC,eAAe,CAAC,YAAY;gBACvC,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;oBAC1C,UAAU,GAAG,IAAI,CAAC;gBACnB,CAAC;gBACD,MAAM;YAEP,KAAK,MAAM,CAAC,eAAe,CAAC,aAAa;gBACxC,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,UAAU,EAAE,CAAC;oBACxD,kCAAkC;oBAClC,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACjC,2BAA2B;oBAC3B,MAAM,iCAAiC,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC;oBACpE,sBAAsB;oBACtB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;oBAExC;;mCAEe;oBACf,IAAI,iCAAiC,EAAE,CAAC;wBACvC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3B,CAAC;yBAAM,CAAC;wBACP,KAAK,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;wBAC9B,cAAc,GAAG,EAAE,CAAC;oBACrB,CAAC;oBAED,IAAI,CAAC,iCAAiC,IAAI,CAAC,UAAU,EAAE,CAAC;wBACvD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAClB,CAAC;oBACD,qBAAqB,GAAG,qBAAqB,IAAI,iCAAiC,CAAC;gBACpF,CAAC;gBACD,MAAM;YAEP,KAAK,MAAM,CAAC,eAAe,CAAC,WAAW;gBACtC,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBACvB,EAAE,UAAU,CAAC;gBACd,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBAC9B,EAAE,UAAU,CAAC;oBACb,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,EAAE,CAAC;wBACnC,MAAM,KAAK,CAAC;oBACb,CAAC;gBACF,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;oBACpD,4DAA4D;oBAC5D,qBAAqB,GAAG,IAAI,CAAC;oBAC7B,MAAM,KAAK,CAAC;gBACb,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBAC9B,EAAE,UAAU,CAAC;gBACd,CAAC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;oBAC9B,EAAE,UAAU,CAAC;gBACd,CAAC;gBACD,MAAM;QACR,CAAC;IACF,CAAC;IAED,OAAO,EAAE,qBAAqB,EAAE,KAAK,EAAE,CAAC;AACzC,CAAC", "file": "snippetForFunctionCall.js", "sourceRoot": "../../../src/"}