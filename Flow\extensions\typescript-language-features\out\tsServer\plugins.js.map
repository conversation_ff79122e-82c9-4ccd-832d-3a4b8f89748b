{"version": 3, "sources": ["tsServer/plugins.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,wDAA0C;AAC1C,8CAA8C;AAW9C,IAAU,sBAAsB,CAO/B;AAPD,WAAU,sBAAsB;IAC/B,SAAgB,MAAM,CAAC,CAAyB,EAAE,CAAyB;QAC1E,OAAO,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE;eACxC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI;eACjB,CAAC,CAAC,oCAAoC,KAAK,CAAC,CAAC,oCAAoC;eACjF,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IALe,6BAAM,SAKrB,CAAA;AACF,CAAC,EAPS,sBAAsB,KAAtB,sBAAsB,QAO/B;AAED,MAAa,aAAc,SAAQ,oBAAU;IAK5C;QACC,KAAK,EAAE,CAAC;QALQ,0BAAqB,GAAG,IAAI,GAAG,EAAc,CAAC;QAyB9C,wBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC,CAAC;QACvE,uBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC;QAEnD,uBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAoC,CAAC,CAAC;QAClG,sBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;QAtBjE,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,EAAE;YAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACpB,OAAO;YACR,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,sBAAsB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtI,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;gBAC3B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;QACF,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;IAED,IAAW,OAAO;QACjB,IAAI,CAAC,QAAQ,KAAb,IAAI,CAAC,QAAQ,GAAK,IAAI,CAAC,WAAW,EAAE,EAAC;QACrC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAQM,gBAAgB,CAAC,QAAgB,EAAE,MAAU;QACnD,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAEM,cAAc;QACpB,OAAO,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;IAC7C,CAAC;IAEO,WAAW;QAClB,MAAM,SAAS,GAAG,IAAI,GAAG,EAAiD,CAAC;QAC3E,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAC/C,MAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC;YACnC,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACjF,MAAM,OAAO,GAA6B,EAAE,CAAC;gBAC7C,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,uBAAuB,EAAE,CAAC;oBAC/D,OAAO,CAAC,IAAI,CAAC;wBACZ,SAAS;wBACT,IAAI,EAAE,MAAM,CAAC,IAAI;wBACjB,oCAAoC,EAAE,CAAC,CAAC,MAAM,CAAC,oCAAoC;wBACnF,GAAG,EAAE,SAAS,CAAC,YAAY;wBAC3B,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;wBAClE,eAAe,EAAE,MAAM,CAAC,eAAe;qBACvC,CAAC,CAAC;gBACJ,CAAC;gBACD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACpB,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBACtC,CAAC;YACF,CAAC;QACF,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;CACD;AAhED,sCAgEC", "file": "plugins.js", "sourceRoot": "../../src/"}