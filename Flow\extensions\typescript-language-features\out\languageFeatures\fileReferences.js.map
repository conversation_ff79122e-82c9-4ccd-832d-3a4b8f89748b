{"version": 3, "sources": ["languageFeatures/fileReferences.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EhG,4BAWC;AAnFD,+CAAiC;AAEjC,8DAAuE;AACvE,yCAAsC;AACtC,kEAAoD;AAIpD,MAAM,qBAAqB;IAO1B,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;QAHlC,OAAE,GAAG,kCAAkC,CAAC;IAIpD,CAAC;IAEE,KAAK,CAAC,OAAO,CAAC,QAAqB;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,qBAAqB,CAAC,UAAU,CAAC,EAAE,CAAC;YACjE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,wDAAwD,CAAC,CAAC,CAAC;YACxG,OAAO;QACR,CAAC;QAED,QAAQ,KAAR,QAAQ,GAAK,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC,GAAG,EAAC;QAC1D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,oDAAoD,CAAC,CAAC,CAAC;YACpG,OAAO;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,IAAA,qCAAuB,EAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,qDAAqD,CAAC,CAAC,CAAC;YACrG,OAAO;QACR,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE,CAAC;YACtB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,iDAAiD,CAAC,CAAC,CAAC;YACjG,OAAO;QACR,CAAC;QAED,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAChC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;YACxC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB,CAAC;SAC/C,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE;YAE7B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE;gBAC5D,IAAI,EAAE,eAAe;aACrB,EAAE,KAAK,CAAC,CAAC;YACV,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACpD,OAAO;YACR,CAAC;YAED,MAAM,SAAS,GAAsB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CACvE,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;YAE1F,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;YAC/D,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAS,mBAAmB,CAAC,CAAC;YAEpE,MAAM,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC;gBACJ,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,EAAE,QAAQ,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;YACtH,CAAC;oBAAS,CAAC;gBACV,MAAM,MAAM,CAAC,MAAM,CAAC,mBAAmB,EAAE,eAAe,EAAE,oBAAoB,IAAI,eAAe,EAAE,cAAc,CAAC,CAAC;YACpH,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;;AA1DsB,6BAAO,GAAG,0BAAH,AAA6B,CAAC;AACrC,gCAAU,GAAG,SAAG,CAAC,IAAP,AAAW,CAAC;AA6D9C,SAAgB,QAAQ,CACvB,MAAgC,EAChC,cAA8B;IAE9B,SAAS,aAAa;QACrB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,qBAAqB,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC;IACtI,CAAC;IACD,aAAa,EAAE,CAAC;IAEhB,cAAc,CAAC,QAAQ,CAAC,IAAI,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC;IAC3D,OAAO,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC;AACxD,CAAC", "file": "fileReferences.js", "sourceRoot": "../../src/"}