{"version": 3, "sources": ["languageFeatures/util/codeAction.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhG,oDAOC;AAED,0CAYC;AAED,0DAWC;AAvCD,+CAAiC;AAEjC,qEAAuD;AAGvD,SAAgB,oBAAoB,CACnC,MAAgC,EAChC,MAAwB;IAExB,OAAO,MAAM,CAAC,OAAO,EAAE,MAAM;QAC5B,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;QACxE,CAAC,CAAC,SAAS,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,eAAe,CACpC,MAAgC,EAChC,MAAwB,EACxB,KAA+B;IAE/B,MAAM,aAAa,GAAG,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC3D,IAAI,aAAa,EAAE,CAAC;QACnB,IAAI,CAAC,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACd,CAAC;IACF,CAAC;IACD,OAAO,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChE,CAAC;AAEM,KAAK,UAAU,uBAAuB,CAC5C,MAAgC,EAChC,QAAuC,EACvC,KAA+B;IAE/B,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;QACtB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAChC,MAAM,MAAM,CAAC,OAAO,CAAC,wBAAwB,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QACpE,CAAC;IACF,CAAC;IACD,OAAO,IAAI,CAAC;AACb,CAAC", "file": "codeAction.js", "sourceRoot": "../../../src/"}