{"version": 3, "sources": ["languageFeatures/directiveCommentCompletions.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEhG,4BAOC;AA1ED,+CAAiC;AAEjC,yCAAsC;AAStC,MAAM,YAAY,GAAgB;IACjC;QACC,KAAK,EAAE,WAAW;QAClB,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,+EAA+E,CAAC;KAC3G,EAAE;QACF,KAAK,EAAE,aAAa;QACpB,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gFAAgF,CAAC;KAC5G,EAAE;QACF,KAAK,EAAE,YAAY;QACnB,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,yDAAyD,CAAC;KACrF;CACD,CAAC;AAEF,MAAM,eAAe,GAAgB;IACpC,GAAG,YAAY;IACf;QACC,KAAK,EAAE,kBAAkB;QACzB,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,0FAA0F,CAAC;KACtH;CACD,CAAC;AAEF,MAAM,kCAAkC;IAEvC,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;IAC9C,CAAC;IAEE,sBAAsB,CAC5B,QAA6B,EAC7B,QAAyB,EACzB,MAAgC;QAEhC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACjD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE,CAAC;YACX,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC;gBACtD,CAAC,CAAC,eAAe;gBACjB,CAAC,CAAC,YAAY,CAAC;YAEhB,OAAO,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;gBACjC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBAC3F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,WAAW,CAAC;gBACpC,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACpJ,OAAO,IAAI,CAAC;YACb,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,EAAE,CAAC;IACX,CAAC;CACD;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,MAAgC;IAEhC,OAAO,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,QAAQ,CAAC,MAAM,EACrE,IAAI,kCAAkC,CAAC,MAAM,CAAC,EAC9C,GAAG,CAAC,CAAC;AACP,CAAC", "file": "directiveCommentCompletions.js", "sourceRoot": "../../src/"}