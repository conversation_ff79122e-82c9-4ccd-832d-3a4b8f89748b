{"version": 3, "sources": ["fig/figInterface.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BhG,8CA+EC;AAqDD,kEA+KC;AAMD,kEAKC;AAED,sDAWC;AAnWD,+CAAiC;AACjC,yEAA4F;AAE5F,gEAAuE;AACvE,sDAAgF;AAChF,0CAAgD;AAChD,oDAAkE;AAClE,8DAAiE;AAGjE,sCAA4C;AAC5C,0CAAyD;AAEzD,gEAAiE;AAW1D,KAAK,UAAU,iBAAiB,CACtC,KAAiB,EACjB,eAAgE,EAChE,iBAAwC,EACxC,MAAc,EACd,SAAoB,EACpB,mBAA2C,EAC3C,GAA2B,EAC3B,IAAY,EACZ,aAAqB,EACrB,gBAAsC,EACtC,KAAgC;IAEhC,MAAM,MAAM,GAA8B;QACzC,cAAc,EAAE,KAAK;QACrB,gBAAgB,EAAE,KAAK;QACvB,aAAa,EAAE,KAAK;QACpB,KAAK,EAAE,EAAE;KACT,CAAC;IACF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QAC1B,MAAM,UAAU,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,UAAU,EAAE,CAAC;YACjB,SAAS;QACV,CAAC;QACD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACpC,MAAM,gBAAgB,GAAG,CAAC,IAAA,gBAAW,GAAE;gBACtC,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,GAAG,SAAS,cAAc,CAAC,CAAC,CAAC;gBAC5J,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACjI,IAAI,CAAC,gBAAgB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,uBAAuB,CAAC,EAAE,CAAC;gBACnE,SAAS;YACV,CAAC;YAED,kCAAkC;YAClC,IAAI,SAAS,8BAAsB,EAAE,CAAC;gBACrC,IAAI,gBAAgB,CAAC,IAAI,KAAK,MAAM,CAAC,0BAA0B,CAAC,KAAK,EAAE,CAAC;oBACvE,MAAM,WAAW,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;oBACtD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAA,qCAAoB,EACrC,eAAe,CAAC,cAAc,EAC9B,MAAM,EACN;wBACC,KAAK,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE;wBACxC,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM;qBAC9C,EACD,WAAW,EACX,gBAAgB,CAAC,MAAM,CACvB,CAAC,CAAC;gBACJ,CAAC;gBACD,SAAS;YACV,CAAC;YAED,MAAM,iBAAiB,GAAG,CAAC,IAAA,gBAAW,GAAE;gBACvC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,KAAK,IAAA,6BAAsB,EAAC,OAAO,CAAC,iBAAiB,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBACnL,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,SAAS,KAAK,CAAC,OAAO,CAAC,iBAAiB,IAAI,CAAC,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAChK,IACC,CAAC,CAAC,IAAA,gBAAW,GAAE;gBACd,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,IAAA,6BAAsB,EAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;gBAC9I,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EACrH,CAAC;gBACF,SAAS;YACV,CAAC;YAED,MAAM,UAAU,GAAG,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC,oCAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACvI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACjB,SAAS;YACV,CAAC;YACD,MAAM,oBAAoB,GAAG,MAAM,qBAAqB,CAAC,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACvJ,MAAM,CAAC,aAAa,KAApB,MAAM,CAAC,aAAa,GAAK,CAAC,CAAC,oBAAoB,EAAE,aAAa,EAAC;YAC/D,IAAI,oBAAoB,EAAE,CAAC;gBAC1B,MAAM,CAAC,cAAc,KAArB,MAAM,CAAC,cAAc,GAAK,oBAAoB,CAAC,cAAc,EAAC;gBAC9D,MAAM,CAAC,gBAAgB,KAAvB,MAAM,CAAC,gBAAgB,GAAK,oBAAoB,CAAC,gBAAgB,EAAC;gBAClE,MAAM,CAAC,cAAc,KAArB,MAAM,CAAC,cAAc,GAAK,oBAAoB,CAAC,cAAc,EAAC;gBAC9D,IAAI,oBAAoB,CAAC,KAAK,EAAE,CAAC;oBAChC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,KAAK,CAAC,CAAC;gBAClD,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC;AAED,KAAK,UAAU,qBAAqB,CACnC,IAAc,EACd,eAAgE,EAChE,MAAc,EACd,mBAA2C,EAC3C,GAA2B,EAC3B,IAAY,EACZ,gBAAsC,EACtC,KAAgC;IAEhC,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI,cAAoC,CAAC;IAEzC,MAAM,OAAO,GAAG,IAAA,oBAAU,EAAC,eAAe,CAAC,WAAW,EAAE,EAAE,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;IAC5F,IAAI,CAAC,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACtC,OAAO;IACR,CAAC;IACD,MAAM,YAAY,GAAqB;QACtC,oBAAoB,EAAE,GAAG;QACzB,uBAAuB,EAAE,mBAAmB,CAAC,MAAM;QACnD,SAAS,EAAE,EAAE;QACb,cAAc,EAAE,IAAI;QACpB,wBAAwB;KACxB,CAAC;IACF,MAAM,eAAe,GAAyB,MAAM,IAAA,+BAAc,EAAC,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;IAElH,MAAM,KAAK,GAAoC,EAAE,CAAC;IAClD,+CAA+C;IAC/C,MAAM,oBAAoB,GAAG,MAAM,2BAA2B,CAAC,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,mBAAmB,EAAE,GAAG,EAAE,KAAK,EAAE,gBAAgB,CAAC,CAAC;IACrK,IAAI,KAAK,EAAE,uBAAuB,EAAE,CAAC;QACpC,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,IAAI,oBAAoB,EAAE,CAAC;QAC1B,cAAc,GAAG,oBAAoB,CAAC,cAAc,CAAC;QACrD,gBAAgB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC;QACzD,cAAc,GAAG,oBAAoB,CAAC,cAAc,CAAC;IACtD,CAAC;IAED,OAAO;QACN,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,aAAa,EAAE,CAAC,CAAC,eAAe,CAAC,UAAU;QAC3C,KAAK;KACL,CAAC;AACH,CAAC;AAIM,KAAK,UAAU,2BAA2B,CAChD,OAAgB,EAChB,eAAqC,EACrC,MAAc,EACd,eAAgE,EAChE,mBAA2C,EAC3C,GAA2B,EAC3B,KAAsC,EACtC,gBAAsC;IAEtC,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI,cAAoC,CAAC;IAEzC,MAAM,cAAc,GAAG,KAAK,EAAE,QAAyD,EAAE,IAAuC,EAAE,eAAsC,EAAE,EAAE;QAC3K,IAAI,IAAI,KAAK,MAAM,CAAC,0BAA0B,CAAC,QAAQ,IAAI,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;YACpG,MAAM,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC,UAAU,CAAC;YACzD,MAAM,eAAe,GAAa;gBACjC,MAAM,EAAE,eAAe,CAAC,WAAW;gBACnC,cAAc,EAAE,eAAe,CAAC,cAAc;gBAC9C,GAAG,EAAE,mBAAmB,EAAE,MAAM,IAAI,IAAI;gBACxC,eAAe,EAAE,IAAI;gBACrB,gBAAgB,EAAE,IAAI;gBACtB,OAAO,EAAE,EAAE;gBACX,oBAAoB,EAAE,GAAG;gBACzB,YAAY,EAAE;oBACb,uBAAuB,EAAE,mBAAmB,EAAE,MAAM;oBACpD,oBAAoB,EAAE,uBAAuB,CAAC,GAAG,CAAC;iBAClD;aACD,CAAC;YACF,MAAM,KAAK,GAAsB;gBAChC,QAAQ,EAAE,eAAe;gBACzB,YAAY,EAAE,eAAe;gBAC7B,eAAe,EAAE,EAAE;gBACnB,OAAO;gBAEP,YAAY,EAAE,kBAAU,CAAC,qBAAqB;gBAC9C,sBAAsB,EAAE,IAAI;gBAC5B,YAAY,EAAE,KAAK;gBAEnB,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,EAAE;gBACf,eAAe,EAAE,KAAK;gBAEtB,kBAAkB,EAAE,KAAK;gBACzB,kBAAkB,EAAE,KAAK;gBACzB,sBAAsB,EAAE,KAAK;aAC7B,CAAC;YACF,MAAM,CAAC,GAAG,IAAA,iCAAoB,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YACxD,MAAM,gBAAgB,GAAG,CAAC,CAAC,iBAAiB,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;YAChF,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE,CAAC;gBAChD,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,eAAe,EAAE,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;oBAC3D,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;wBAC1B,cAAc,GAAG,IAAI,CAAC;wBACtB,gBAAgB,GAAG,IAAI,CAAC;wBACxB,cAAc,GAAG,IAAI,CAAC,SAAS,EAAE,cAAsC,CAAC;oBACzE,CAAC;oBACD,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;wBAC5B,gBAAgB,GAAG,IAAI,CAAC;oBACzB,CAAC;oBAED,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;wBAChB,SAAS;oBACV,CAAC;oBACD,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;oBACrD,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBACvB,SAAS;oBACV,CAAC;oBACD,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE,CAAC;wBACtC,KAAK,CAAC,IAAI,CAAC,IAAA,qCAAoB,EAC9B,eAAe,CAAC,cAAc,EAC9B,MAAM,EACN,EAAE,KAAK,EAAE,EACT,SAAS,EACT,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAClD,IAAI,CACJ,CAAC,CAAC;oBACJ,CAAC;gBACF,CAAC;YACF,CAAC;YACD,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;gBACpC,gFAAgF;gBAChF,oCAAoC;gBACpC,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;oBACxB,MAAM,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;oBAChG,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;wBAClC,IAAI,QAAQ,KAAK,WAAW,EAAE,CAAC;4BAC9B,cAAc,GAAG,IAAI,CAAC;wBACvB,CAAC;6BAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;4BACnC,gBAAgB,GAAG,IAAI,CAAC;wBACzB,CAAC;oBACF,CAAC;gBACF,CAAC;YACF,CAAC;QACF,CAAC;QACD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,CAAC;QAC7C,CAAC;QACD,MAAM,cAAc,GAAG,IAAI,KAAK,MAAM,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,EAAE,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAEtJ,SAAS,OAAO,CAAC,KAAa,EAAE,IAAa;YAC5C,IAAI,cAAc,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrC,OAAO;YACR,CAAC;YAED,IAAI,QAAQ,GAAG,IAAI,CAAC;YACpB,MAAM,WAAW,GAAuB,eAAe,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;YAClF,IAAI,WAAW,KAAK,gBAAgB,EAAE,CAAC;gBACtC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,IAAA,6BAAO,EAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzF,QAAQ,GAAG,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;gBACrD,CAAC;YACF,CAAC;iBACI,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;gBACvC,QAAQ,GAAG,MAAM,CAAC,0BAA0B,CAAC,WAAW,CAAC;YAC1D,CAAC;YAED,mCAAmC;YACnC,IAAI,MAA0B,CAAC;YAC/B,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBAChD,MAAM,IAAI,GAAG,IAAA,6BAAO,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;oBAChC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACrB,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;4BAC3B,IAAI,MAAM,GAAG,IAAI,CAAE,CAAC,IAAI,GAAG,CAAC;4BAC5B,IAAI,CAAC,EAAE,UAAU,EAAE,CAAC;gCACnB,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC;4BACxB,CAAC;4BACD,OAAO,MAAM,CAAC;wBACf,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACd,CAAC;gBACF,CAAC;YACF,CAAC;YAED,KAAK,CAAC,IAAI,CACT,IAAA,qCAAoB,EACnB,eAAe,CAAC,cAAc,EAC9B,MAAM,EACN;gBACC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK;aACzC,EACD,SAAS,EACT,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAClD,QAAQ,CACR,CACD,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC7B,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;gBACrD,IAAI,CAAC,gBAAgB,EAAE,MAAM,EAAE,CAAC;oBAC/B,SAAS;gBACV,CAAC;gBACD,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE,CAAC;oBACtC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;gBACtB,CAAC;YACF,CAAC;QACF,CAAC;aAAM,CAAC;YACP,KAAK,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtD,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACtB,CAAC;QACF,CAAC;IACF,CAAC,CAAC;IAEF,IAAI,eAAe,CAAC,eAAe,GAAG,sBAAc,CAAC,IAAI,EAAE,CAAC;QAC3D,MAAM,cAAc,CAAC,eAAe,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,0BAA0B,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;IAC5H,CAAC;IACD,IAAI,eAAe,CAAC,eAAe,GAAG,sBAAc,CAAC,WAAW,EAAE,CAAC;QAClE,MAAM,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,WAAW,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;IAC3G,CAAC;IACD,IAAI,eAAe,CAAC,eAAe,GAAG,sBAAc,CAAC,OAAO,EAAE,CAAC;QAC9D,MAAM,cAAc,CAAC,eAAe,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,0BAA0B,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;IACtH,CAAC;IAED,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,CAAC;AAC7D,CAAC;AAED,SAAS,uBAAuB,CAAC,GAA2B;IAC3D,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,SAAgB,2BAA2B,CAAC,IAAc;IACzD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC,WAAW,IAAI,EAAE,CAAC;IAC/B,CAAC;IACD,OAAO,EAAE,CAAC;AACX,CAAC;AAED,SAAgB,qBAAqB,CAAC,IAAkD;IACvF,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;IACD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACnC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzD,OAAO;IACR,CAAC;IACD,OAAO,IAAI,CAAC,IAAI,CAAC;AAClB,CAAC", "file": "figInterface.js", "sourceRoot": "../../src/"}