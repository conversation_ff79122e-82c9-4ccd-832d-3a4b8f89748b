{"version": 3, "sources": ["languageFeatures/copyPaste.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmPhG,4BAYC;AA7PD,+CAAiC;AAGjC,yCAAsC;AAEtC,kEAAoD;AACpD,4DAAkG;AAClG,0CAA6C;AAE7C,wEAA6I;AAE7I,MAAM,YAAY;IAEjB,MAAM,CAAC,KAAK,CAAC,IAAY;QACxB,IAAI,CAAC;YAEJ,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/F,MAAM,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACvG,OAAO,IAAI,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,SAAS,CAAC;QAClB,CAAC;IACF,CAAC;IAED,YACiB,QAAoB,EACpB,MAA+B,EAC/B,aAA+F;QAF/F,aAAQ,GAAR,QAAQ,CAAY;QACpB,WAAM,GAAN,MAAM,CAAyB;QAC/B,kBAAa,GAAb,aAAa,CAAkF;IAC5G,CAAC;CACL;AAED,MAAM,WAAY,SAAQ,MAAM,CAAC,iBAAiB;IAEjD,MAAM,CAAC,qBAAqB,CAC3B,MAAgC,EAChC,QAAiE;QAEjE,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;YAClE,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,WAAW,EAAE,CAAC;QAEpC,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QAClD,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACxC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;QAClH,CAAC;QACD,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;QAE1C,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;QACC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,OAAO,GAAG;YACd,MAAM,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;SACvD,CAAC;IACH,CAAC;CACD;AAED,MAAM,kBAAmB,SAAQ,WAAW;IAC3C,YACC,IAAY,EACI,SAA2E;QAE3F,KAAK,EAAE,CAAC;QAFQ,cAAS,GAAT,SAAS,CAAkE;QAG3F,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACxB,CAAC;CACD;AAED,MAAM,gBAAgB,GAAG,8BAA8B,CAAC;AAExD,MAAM,qBAAqB;IAK1B,YACkB,OAAe,EACf,OAAiC,EACjC,wBAAkD;QAFlD,YAAO,GAAP,OAAO,CAAQ;QACf,YAAO,GAAP,OAAO,CAA0B;QACjC,6BAAwB,GAAxB,wBAAwB,CAA0B;IAChE,CAAC;IAEL,KAAK,CAAC,oBAAoB,CAAC,QAA6B,EAAE,MAA+B,EAAE,YAAiC,EAAE,KAA+B;QAC5J,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,OAAO;QACR,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO;QACR,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,mBAAmB,EAAE;YAChG,IAAI;YACJ,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC;SAC3D,EAAE,KAAK,CAAC,CAAC,CAAC;QAEX,MAAM,WAAW,GAAG,GAAG,CAAC;QACxB,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAW,EAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC7D,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACnC,OAAO;QACR,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACd,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACpD,8DAA8D;gBAC9D,yDAAyD;gBACzD,OAAO;YACR,CAAC;YAED,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,gBAAgB,EACtD,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;QAClF,CAAC;aAAM,CAAC;YACP,0GAA0G;YAC1G,iCAAiC;YACjC,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,gBAAgB,EACtD,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QACpF,CAAC;IACF,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC9B,QAA6B,EAC7B,MAA+B,EAC/B,YAAiC,EACjC,QAAyC,EACzC,KAA+B;QAE/B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/B,OAAO;QACR,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO;QACR,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE,CAAC;QAC9D,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAC5C,OAAO;QACR,CAAC;QAED,wBAAwB;QACxB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACjE,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACnC,OAAO;QACR,CAAC;QAED,IAAI,UAGS,CAAC;QACd,IAAI,QAAQ,EAAE,CAAC;YACd,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC9D,IAAI,QAAQ,EAAE,CAAC;gBACd,UAAU,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YACxC,CAAC;QACF,CAAC;QAED,IAAI,UAAU,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC;YAC/B,yEAAyE;YACzE,OAAO;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACtD,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAEvD,wGAAwG;QACxG,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE;YAC5C,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBAC5D,QAAQ,CAAC,MAAM,EAAE,CAAC;YACnB,CAAC;QACF,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE;YACX,qCAAqC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC;YACJ,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE;gBACxD,IAAI,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAE9E,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE;oBAC5C,IAAI;oBACJ,6CAA6C;oBAC7C,UAAU,EAAE,CAAC,IAAI,CAAC;oBAClB,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC;oBAC3D,UAAU;iBACV,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,GAAG,CAAC;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAA,mBAAW,EAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YACjE,IAAI,QAAQ,EAAE,CAAC;gBACd,uCAAuC;gBACvC,MAAM,IAAI,GAAG,WAAW,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACvE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACP,0FAA0F;gBAC1F,0BAA0B;gBAC1B,OAAO,CAAC,IAAI,kBAAkB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;YACvD,CAAC;QACF,CAAC;gBAAS,CAAC;YACV,QAAQ,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC;IACF,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,MAAmB,EAAE,MAAgC;QACnF,IAAI,CAAC,CAAC,MAAM,YAAY,kBAAkB,CAAC,EAAE,CAAC;YAC7C,OAAO;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC;QACxC,MAAM,SAAS,GAAG,kBAAkB,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QACnF,OAAO,SAAS,IAAI,MAAM,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,YAAiC,EAAE,KAA+B;QAC/F,MAAM,QAAQ,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EAAE,KAAK,CAAC;QACvF,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YACnC,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,QAAQ,YAAY,YAAY,EAAE,CAAC;YACtC,OAAO,QAAQ,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAClC,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,SAAS,CAAC,QAA6B;QAC9C,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC7E,OAAO,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;;AAlKe,0BAAI,GAAG,MAAM,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC3E,sCAAgB,GAAG,oCAAoC,CAAC;AAoKzE,SAAgB,QAAQ,CAAC,QAA0B,EAAE,QAA6B,EAAE,MAAgC,EAAE,wBAAkD;IACvK,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,QAAQ,CAAC;QACxD,IAAA,yCAAiB,EAAC,MAAM,EAAE,SAAG,CAAC,IAAI,CAAC;QACnC,IAAA,kDAA0B,EAAC,QAAQ,CAAC,EAAE,EAAE,gBAAgB,CAAC;KACzD,EAAE,GAAG,EAAE;QACP,OAAO,MAAM,CAAC,SAAS,CAAC,iCAAiC,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,qBAAqB,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,wBAAwB,CAAC,EAAE;YACtJ,sBAAsB,EAAE,CAAC,qBAAqB,CAAC,IAAI,CAAC;YACpD,aAAa,EAAE,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;YACvD,cAAc,EAAE,CAAC,qBAAqB,CAAC,gBAAgB,CAAC;SACxD,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "copyPaste.js", "sourceRoot": "../../src/"}