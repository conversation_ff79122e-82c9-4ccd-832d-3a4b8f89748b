{"version": 3, "sources": ["tsServer/versionProvider.electron.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,uCAAyB;AACzB,2CAA6B;AAC7B,+CAAiC;AAEjC,wEAA8E;AAC9E,+BAA4B;AAC5B,uDAA2G;AAG3G,MAAa,6BAA6B;IAEzC,YACS,aAA8C;QAA9C,kBAAa,GAAb,aAAa,CAAiC;IACnD,CAAC;IAEE,mBAAmB,CAAC,aAA6C;QACvE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACpC,CAAC;IAED,IAAW,cAAc;QACxB,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC;IAClD,CAAC;IAED,IAAW,aAAa;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;YACpC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,2DAAsC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YACjH,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACtC,CAAC;IAED,IAAW,YAAY;QACtB,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC;QAC5C,IAAI,YAAY,EAAE,MAAM,EAAE,CAAC;YAC1B,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACnD,IAAI,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAGD,IAAW,aAAa;QACvB,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACjF,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAC;QAChC,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAC7B,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACd,CAAC;YACD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO,IAAI,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC;IAED,IAAW,cAAc;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,kDAAkC,qCAAqC,EAAE,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;QAC3I,IAAI,OAAO,EAAE,CAAC;YACb,OAAO,OAAO,CAAC;QAChB,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,8HAA8H,CAAC,CAAC,CAAC;QAC9K,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACvD,CAAC;IAED,IAAY,wBAAwB;QACnC,OAAO,IAAI,CAAC,qBAAqB,0EAA6C,kCAAkC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC;IACrI,CAAC;IAEO,qBAAqB,CAAC,MAA+B,EAAE,WAAmB,EAAE,QAA2B;QAC9G,IAAI,CAAC;YACJ,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAC9D,IAAI,SAAS,EAAE,CAAC;gBACf,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,GAAG,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC;gBACvG,MAAM,cAAc,GAAG,IAAI,mCAAiB,CAAC,MAAM,EAAE,UAAU,EAAE,6BAA6B,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC9H,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;oBAC5B,OAAO,cAAc,CAAC;gBACvB,CAAC;YACF,CAAC;QACF,CAAC;QAAC,MAAM,CAAC;YACR,OAAO;QACR,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,IAAY,iBAAiB;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,SAAS,CAAC;QAChD,OAAO,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,qEAA2C,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3G,CAAC;IAEO,uBAAuB,CAAC,MAA+B,EAAE,eAAuB;QACvF,IAAI,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;YAC7D,OAAO;gBACN,IAAI,mCAAiB,CAAC,MAAM,EAC3B,UAAU,EACV,6BAA6B,CAAC,aAAa,CAAC,UAAU,CAAC,EACvD,eAAe,CAAC;aACjB,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAG,oDAA6B,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;QAC7F,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;YAC3D,OAAO;gBACN,IAAI,mCAAiB,CAAC,MAAM,EAC3B,UAAU,EACV,6BAA6B,CAAC,aAAa,CAAC,UAAU,CAAC,EACvD,eAAe,CAAC;aACjB,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,8BAA8B,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;IACrE,CAAC;IAED,IAAY,wBAAwB;QACnC,OAAO,IAAI,CAAC,8BAA8B,2DAAsC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;aAC7H,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IAC1B,CAAC;IAEO,8BAA8B,CAAC,MAA+B,EAAE,YAAoB;QAC3F,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACxC,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,QAAQ,GAAwB,EAAE,CAAC;QACzC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACtD,IAAI,KAAK,GAAW,YAAY,CAAC;YACjC,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC5C,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;YAC3E,QAAQ,CAAC,IAAI,CAAC,IAAI,mCAAiB,CAAC,MAAM,EAAE,UAAU,EAAE,6BAA6B,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QAC1H,CAAC;QACD,OAAO,QAAQ,CAAC;IACjB,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,UAAkB;QAC9C,MAAM,OAAO,GAAG,6BAA6B,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QAC/E,IAAI,OAAO,EAAE,CAAC;YACb,OAAO,OAAO,CAAC;QAChB,CAAC;QAED,gDAAgD;QAChD,MAAM,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAqB,yBAAyB,EAAE,SAAS,CAAC,CAAC;QACtH,IAAI,WAAW,EAAE,CAAC;YACjB,OAAO,SAAG,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,UAAkB;QACrD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAChC,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACnB,OAAO,SAAS,CAAC;QAClB,CAAC;QACD,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,mCAAmC;YACnC,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,OAAO,EAAE,CAAC;gBAC3C,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YACxD,CAAC;QACF,CAAC;QACD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtD,IAAI,IAAI,GAAQ,IAAI,CAAC;QACrB,IAAI,CAAC;YACJ,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;YACpB,OAAO,SAAS,CAAC;QAClB,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,SAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACvE,CAAC;CACD;AAtLD,sEAsLC", "file": "versionProvider.electron.js", "sourceRoot": "../../src/"}