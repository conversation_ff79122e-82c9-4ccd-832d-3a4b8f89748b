{"version": 3, "sources": ["languageFeatures/completions.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA65BhG,4BAiBC;AA56BD,+CAAiC;AAKjC,yCAAsC;AACtC,8DAAmE;AAEnE,4EAA8D;AAC9D,kEAAoD;AACpD,4DAAkG;AAElG,wDAAiD;AAEjD,kDAAoD;AACpD,wEAA8F;AAC9F,0EAAuE;AACvE,gEAAkD;AA2BlD,MAAM,gBAAiB,SAAQ,MAAM,CAAC,cAAc;IAInD,YACiB,QAAyB,EACzB,QAA6B,EAC7B,OAA8B,EAC7B,iBAAoC,EACrC,QAAyB,EACzC,MAAgC,EAChC,uBAAsD;QAEtD,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;QACzD,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QATzC,aAAQ,GAAR,QAAQ,CAAiB;QACzB,aAAQ,GAAR,QAAQ,CAAqB;QAC7B,YAAO,GAAP,OAAO,CAAuB;QAC7B,sBAAiB,GAAjB,iBAAiB,CAAmB;QACrC,aAAQ,GAAR,QAAQ,CAAiB;QAOzC,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,SAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3E,4BAA4B;YAC5B,mDAAmD;YACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAC7C,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACzC,iDAAiD;YACjD,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC3E,IAAI,kBAAkB,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC3C,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC;YACzD,CAAC;QAEF,CAAC;QAED,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;QAC7C,IAAI,aAAa,EAAE,CAAC;YACnB,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,CAAC,oBAAoB,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,CAAC;QAC5F,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;QACjD,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,aAAa,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAEtK,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,2BAA2B,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QAC1E,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,OAAO,EAAE,uBAAuB,CAAC,CAAC;QAClH,IAAI,CAAC,UAAU,GAAG,SAAS,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;QACtH,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;QAEvG,IAAI,iBAAiB,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,kBAAkB,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YACxI,IAAI,CAAC,UAAU,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YAClG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACjB,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;gBAC1D,IAAI,gBAAgB,EAAE,CAAC;oBACtB,IAAI,CAAC,KAAK,GAAG;wBACZ,SAAS,EAAE,iBAAiB,CAAC,kBAAkB,CAAC,KAAK;wBACrD,SAAS,EAAE,iBAAiB,CAAC,kBAAkB,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC;qBAC7E,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACP,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,KAAK,CAAC;gBACzD,CAAC;gBACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACnC,CAAC;QACF,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC3B,MAAM,aAAa,GAAG,IAAA,6BAAiB,EAAC,OAAO,CAAC,aAAa,CAAC,CAAC;YAC/D,IAAI,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACtD,IAAI,CAAC,UAAU,KAAf,IAAI,CAAC,UAAU,GAAK,IAAI,CAAC,SAAS,EAAC;gBACnC,IAAI,CAAC,UAAU,KAAf,IAAI,CAAC,UAAU,GAAK,IAAI,CAAC,SAAS,EAAC;gBAEnC,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;oBACpC,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACP,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC;gBACzB,CAAC;YACF,CAAC;YACD,IAAI,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC;gBACxD,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACnD,CAAC;YAED,IAAI,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC7C,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;IACrB,CAAC;IAED,IAAY,SAAS;QACpB,OAAO,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IACvE,CAAC;IAQM,KAAK,CAAC,qBAAqB,CACjC,MAAgC,EAChC,KAA+B;QAE/B,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE;YAClC,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;gBACnE,yDAAyD;gBACzD,UAAU,CAAC,GAAG,EAAE;oBACf,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,CAAC,EAAE,CAAC;wBACjE,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;oBAC7C,CAAC;gBACF,CAAC,EAAE,GAAG,CAAC,CAAC;YACT,CAAC;QACF,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,EAAE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;YAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;QACtC,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QAE1D,MAAM,OAAO,GAAG,CAAC,KAAK,IAAiD,EAAE;YACxE,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACf,OAAO,SAAS,CAAC;YAClB,CAAC;YAED,MAAM,IAAI,GAAuC;gBAChD,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;gBAC7E,UAAU,EAAE;oBACX,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC1C,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;wBACvB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;wBAC3B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;qBACvB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI;iBACrB;aACD,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,wBAAwB,EAAE,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;YACxH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBAC5D,OAAO,SAAS,CAAC;YAClB,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAEhC,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACvD,IAAI,cAAc,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;YAC9B,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAE9E,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzD,MAAM,QAAQ,GAAqB,CAAC;oBACnC,OAAO,EAAE,yBAAyB,CAAC,EAAE;oBACrC,KAAK,EAAE,EAAE;oBACT,SAAS,EAAE,CAAC,IAAI,CAAC;iBACjB,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACnC,CAAC;YACD,MAAM,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;YAE3D,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzB,MAAM,sBAAsB,GAAG,MAAM,IAAI,CAAC,gCAAgC,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAClI,IAAI,sBAAsB,EAAE,CAAC;oBAC5B,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,IAAA,+CAAsB,EAAC,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;oBACpH,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC;oBAC1B,IAAI,cAAc,GAAG,CAAC,EAAE,CAAC;wBACxB,2DAA2D;wBAC3D,sEAAsE;wBACtE,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;4BAC/E,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC,CAAC;wBACnG,CAAC;oBACF,CAAC;gBACF,CAAC;YACF,CAAC;YAED,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC;QACjD,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,gBAAgB,GAAG;YACvB,OAAO;YACP,YAAY;YACZ,OAAO,EAAE,CAAC;SACV,CAAC;QAEF,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;IACtC,CAAC;IAEO,UAAU,CACjB,MAAgC,EAChC,MAAoC;QAEpC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACxC,6BAA6B;YAC7B,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,WAAW,IAAI,EAAE,EAAE,CAAC;YAC/C,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;QAChC,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;QACxE,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAEO,gBAAgB,CACvB,MAAgC,EAChC,MAAoC,EACpC,OAAmB;QAEnB,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAClD,SAAS,CAAC,6BAA6B,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAClG,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;QAChC,OAAO,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/D,CAAC;IAEO,KAAK,CAAC,gCAAgC,CAC7C,MAAgC,EAChC,QAAgB,EAChB,QAAyB,EACzB,QAA6B,EAC7B,KAA+B;QAE/B,sEAAsE;QACtE,6EAA6E;QAC7E,IAAI,CAAC;YACJ,MAAM,IAAI,GAAkC,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAClH,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACnD,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC5B,KAAK,KAAK,CAAC;oBACX,KAAK,KAAK,CAAC;oBACX,KAAK,OAAO,CAAC;oBACb,KAAK,OAAO;wBACX,OAAO,KAAK,CAAC;gBACf,CAAC;YACF,CAAC;QACF,CAAC;QAAC,MAAM,CAAC;YACR,OAAO;QACR,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5C,6FAA6F;QAC7F,mDAAmD;QAEnD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACd,CAAC;QAED,2DAA2D;QAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACtD,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,cAAc,CACrB,MAAoC,EACpC,QAAgB;QAEhB,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC;YACjC,OAAO,EAAE,CAAC;QACX,CAAC;QAED,mEAAmE;QACnE,0EAA0E;QAC1E,yBAAyB;QACzB,MAAM,mBAAmB,GAAsB,EAAE,CAAC;QAClD,IAAI,2BAA2B,GAAG,KAAK,CAAC;QACxC,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;YAC3C,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACvB,2BAA2B,GAAG,IAAI,CAAC;YACpC,CAAC;YAED,kEAAkE;YAClE,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACtB,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACvC,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;wBAClC,mBAAmB,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;oBAC3F,CAAC;yBAAM,CAAC;wBACP,2BAA2B,GAAG,IAAI,CAAC;oBACpC,CAAC;gBACF,CAAC;YACF,CAAC;QACF,CAAC;QAED,IAAI,OAAO,GAA+B,SAAS,CAAC;QACpD,IAAI,2BAA2B,EAAE,CAAC;YACjC,iEAAiE;YACjE,OAAO,GAAG;gBACT,KAAK,EAAE,EAAE;gBACT,OAAO,EAAE,gCAAgC,CAAC,EAAE;gBAC5C,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAoB,EAAE,CAAC,CAAC;wBACtE,QAAQ,EAAE,CAAC,CAAC,QAAQ;wBACpB,WAAW,EAAE,CAAC,CAAC,WAAW;wBAC1B,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC;qBACvD,CAAC,CAAC,CAAC;aACJ,CAAC;QACH,CAAC;QAED,OAAO;YACN,OAAO;YACP,mBAAmB,EAAE,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,SAAS;SACjF,CAAC;IACH,CAAC;IAEO,2BAA2B,CAAC,OAA8B,EAAE,iBAAoC;QACvG,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC9B,IAAI,iBAAiB,CAAC,wBAAwB,EAAE,CAAC;gBAChD,OAAO;oBACN,SAAS,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;oBAC5F,SAAS,EAAE,iBAAiB,CAAC,wBAAwB;iBACrD,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,0GAA0G;QAE1G,kDAAkD;QAClD,IAAI,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QAC9E,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;YAChC,YAAY,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAChJ,CAAC;QACD,OAAO;YACN,SAAS,EAAE,YAAY;YACvB,SAAS,EAAE,YAAY;SACvB,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,IAAY,EAAE,UAA8B;QACjE,mCAAmC;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;YACnD,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACjF,IAAI,UAAU,EAAE,CAAC;gBAChB,IAAI,UAAU,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACrC,OAAO,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBAC5E,CAAC;qBAAM,CAAC;oBACP,OAAO,UAAU,CAAC;gBACnB,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,OAAO,SAAS,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC5E,CAAC;QACF,CAAC;QAED,yHAAyH;QACzH,IAAI,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,mBAAmB;QACnB,MAAM;QACN,6BAA6B;QAC7B,UAAU;QACV,MAAM;QACN,yGAAyG;QACzG,6BAA6B;aACxB,IAAI,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACtC,OAAO,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;QAED,uDAAuD;QACvD,OAAO,UAAU,CAAC;IACnB,CAAC;IAEO,YAAY;QACnB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAChB,OAAO;QACR,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC;QACtD,IAAI,YAAY,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,GAAG;gBACZ,SAAS,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;gBAC9D,SAAS,EAAE,YAAY;aACvB,CAAC;QACH,CAAC;IACF,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,IAAY;QACtC,QAAQ,IAAI,EAAE,CAAC;YACd,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;YAC/B,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO;gBACvB,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAE1C,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACvB,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YACrB,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC1B,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;YAC/B,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACvB,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS;gBACzB,OAAO,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAE3C,KAAK,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC;YAChC,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACnC,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB;gBACjC,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAExC,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC1B,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa;gBAC7B,OAAO,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAE3C,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,KAAK,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACpC,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;YAC/B,KAAK,MAAM,CAAC,IAAI,CAAC,cAAc;gBAC9B,OAAO,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAEzC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;gBACpB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAEvC,KAAK,MAAM,CAAC,IAAI,CAAC,UAAU;gBAC1B,OAAO,MAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAE7C,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,KAAK,MAAM,CAAC,IAAI,CAAC,kBAAkB;gBAClC,OAAO,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAEzC,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACvB,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;gBACpB,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAExC,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS;gBACzB,OAAO,MAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC;YAE5C,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO;gBACvB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAEvC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM;gBACtB,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAEvC,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS;gBACzB,OAAO,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAEzC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM;gBACtB,OAAO,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAE3C;gBACC,OAAO,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QAC5C,CAAC;IACF,CAAC;IAEO,MAAM,CAAC,mBAAmB,CACjC,OAA0B,EAC1B,KAA4B,EAC5B,uBAAsD;QAEtD,IAAI,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC7H,IAAI,gBAAgB,EAAE,CAAC;YACtB,IAAI,OAAO,CAAC,qBAAqB;mBAC7B,CAAC,OAAO,CAAC,uBAAuB;mBAChC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO;mBAClC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACvC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;YACD,OAAO,gBAAgB,CAAC;QACzB,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,4CAA4C;YAC1H,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACrC,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACnC,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACnC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,gBAAgB,CAAC;IACzB,CAAC;CACD;AAED,SAAS,oBAAoB,CAAC,OAA8B;IAC3D,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QACnE,OAAO;IACR,CAAC;IAED,MAAM,aAAa,GAAG,IAAA,6BAAiB,EAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAC/D,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,aAAa,CAAC,0BAA0B,EAAE,CAAC;QAC3E,IAAI,aAAa,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACpC,IAAI,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACtD,OAAO,OAAO,CAAC,IAAI,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACP,OAAO,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;YACnC,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC;AAGD,MAAM,yBAAyB;IAI9B,YACkB,oBAA2D,EAC3D,iBAAoC;QADpC,yBAAoB,GAApB,oBAAoB,CAAuC;QAC3D,sBAAiB,GAAjB,iBAAiB,CAAmB;QAJtC,OAAE,GAAG,yBAAyB,CAAC,EAAE,CAAC;IAK9C,CAAC;IAEE,OAAO,CAAC,IAA2B;QACzC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,IAAI,YAAY,gBAAgB,EAAE,CAAC;YACtC;;;;;;;;;cASE;YACF,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,oBAAoB,EAAE;gBACzD,mBAAmB,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAC1E,2BAA2B,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;aAC1F,CAAC,CAAC;QACJ,CAAC;IACF,CAAC;;AA1BsB,4BAAE,GAAG,kCAAH,AAAqC,CAAC;AA6BhE;;GAEG;AACH,MAAM,sBAAsB;IAI3B,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;QAHlC,OAAE,GAAG,sBAAsB,CAAC,EAAE,CAAC;IAI3C,CAAC;IAEE,KAAK,CAAC,OAAO,CAAC,IAAsB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,uBAAQ,CAAC,CAAC;QACzE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO;QACR,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC;QAErC,IAAI,KAAK,EAAE,CAAC;YACX,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACjD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC1B,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACpE,CAAC;YACD,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QACjD,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAChC,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;QACrF,CAAC;IACF,CAAC;;AA1BsB,yBAAE,GAAG,oCAAH,AAAuC,CAAC;AA6BlE,MAAM,gCAAgC;IAIrC,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;QAHlC,OAAE,GAAG,gCAAgC,CAAC,EAAE,CAAC;IAIrD,CAAC;IAEE,KAAK,CAAC,OAAO,CAAC,KAAa,EAAE,WAA+B;QAClE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,CAAC;QACb,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAA,4BAAe,EAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,uBAAQ,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAClD,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC1B,KAAK,EAAE,MAAM,CAAC,WAAW;YACzB,WAAW,EAAE,EAAE;YACf,MAAM;SACN,CAAC,CAAC,EAAE;YACL,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC;SACzD,CAAC,CAAC;QAEH,IAAI,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,4BAAe,EAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE,uBAAQ,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;;AA7BsB,mCAAE,GAAG,uCAAH,AAA0C,CAAC;AAwCrE,IAAU,uBAAuB,CAoBhC;AApBD,WAAU,uBAAuB;IACnB,6CAAqB,GAAG,+BAA+B,CAAC;IACxD,uCAAe,GAAG,eAAe,CAAC;IAClC,uCAAe,GAAG,eAAe,CAAC;IAClC,6CAAqB,GAAG,qBAAqB,CAAC;IAC9C,kDAA0B,GAAG,0BAA0B,CAAC;IAErE,SAAgB,2BAA2B,CAC1C,MAAc,EACd,QAAoB;QAEpB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACnE,OAAO;YACN,qBAAqB,EAAE,MAAM,CAAC,GAAG,CAAU,uBAAuB,CAAC,qBAAqB,EAAE,KAAK,CAAC;YAChG,eAAe,EAAE,MAAM,CAAC,GAAG,CAAU,uBAAuB,CAAC,eAAe,EAAE,IAAI,CAAC;YACnF,qBAAqB,EAAE,MAAM,CAAC,GAAG,CAAU,uBAAuB,CAAC,qBAAqB,EAAE,IAAI,CAAC;YAC/F,eAAe,EAAE,MAAM,CAAC,GAAG,CAAU,uBAAuB,CAAC,eAAe,EAAE,IAAI,CAAC;YACnF,0BAA0B,EAAE,MAAM,CAAC,GAAG,CAAU,uBAAuB,CAAC,0BAA0B,EAAE,IAAI,CAAC;SACzG,CAAC;IACH,CAAC;IAZe,mDAA2B,8BAY1C,CAAA;AACF,CAAC,EApBS,uBAAuB,KAAvB,uBAAuB,QAoBhC;AAED,MAAM,gCAAgC;IAIrC,YACkB,MAAgC,EAChC,QAA6B,EAC7B,aAA4B,EAC5B,wBAAkD,EACnE,cAA8B,EACb,iBAAoC,EACrD,oBAA2D;QAN1C,WAAM,GAAN,MAAM,CAA0B;QAChC,aAAQ,GAAR,QAAQ,CAAqB;QAC7B,kBAAa,GAAb,aAAa,CAAe;QAC5B,6BAAwB,GAAxB,wBAAwB,CAA0B;QAElD,sBAAiB,GAAjB,iBAAiB,CAAmB;QAGrD,cAAc,CAAC,QAAQ,CAAC,IAAI,gCAAgC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3E,cAAc,CAAC,QAAQ,CAAC,IAAI,yBAAyB,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QACrG,cAAc,CAAC,QAAQ,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAClE,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAClC,QAA6B,EAC7B,QAAyB,EACzB,KAA+B,EAC/B,OAAiC;QAEjC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC3F,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;YAC3C,OAAO,OAAO,CAAC,MAAM,CAA0C;gBAC9D,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpB,OAAO,EAAE,sBAAsB;oBAC/B,OAAO,EAAE,CAAC,oGAAoG,CAAC;iBAC/G,CAAC;gBACF,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBACrB,OAAO,EAAE,iDAAiD;oBAC1D,OAAO,EAAE,CAAC,oGAAoG,CAAC;iBAC/G,CAAC;aACF,CAAC,CAAC;QACJ,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,uBAAuB,GAAG,uBAAuB,CAAC,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEpH,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,uBAAuB,CAAC,EAAE,CAAC;YAC3E,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,SAAS,GAAG,QAAQ,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAC1D,IAAI,SAAS,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACrC,MAAM,kBAAkB,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3D,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC,CAAC;YAC1F,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;gBACvB,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YAChD,CAAC;QACF,CAAC;QAED,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;QAEvH,MAAM,IAAI,GAAiC;YAC1C,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,EAAE,QAAQ,CAAC;YACpE,4BAA4B,EAAE,uBAAuB,CAAC,qBAAqB;YAC3E,4BAA4B,EAAE,IAAI;YAClC,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC;YACrD,WAAW,EAAE,cAAc,CAAC,qBAAqB,CAAC,+BAA+B,CAAC,OAAO,CAAC,WAAW,CAAC;SACtG,CAAC;QAEF,IAAI,kBAAkD,CAAC;QACvD,IAAI,QAA2E,CAAC;QAChF,IAAI,QAA4B,CAAC;QACjC,IAAI,wBAAkD,CAAC;QAEvD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC;YACJ,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACxG,CAAC;gBAAS,CAAC;YACV,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACnC,CAAC;QAED,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACjD,OAAO,SAAS,CAAC;QAClB,CAAC;QACD,MAAM,uBAAuB,GAAG,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC;QACtE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;QAC5D,IAAI,kBAAkB,EAAE,CAAC;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,SAAS,CAAC;YACxF,IAAI,QAAQ,EAAE,CAAC;gBACd,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,cAAc,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;gBACtG,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACrC,kBAAkB,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;YACtC,CAAC;QACF,CAAC;QACD,MAAM,YAAY,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,IAAK,QAAQ,CAAC,QAAgB,EAAE,YAAY,CAAC;QAC9F,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;QACtC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACnC,MAAM,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAErF,IAAI,QAAQ,CAAC,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3C,wBAAwB,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrG,CAAC;QAED,MAAM,iBAAiB,GAAsB;YAC5C,uBAAuB;YACvB,kBAAkB;YAClB,kBAAkB;YAClB,qBAAqB,EAAE,CAAC,uBAAuB,CAAC,qBAAqB;YACrE,SAAS;YACT,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,qBAAqB,EAAE,uBAAuB,CAAC,qBAAqB;YACpE,wBAAwB;SACxB,CAAC;QAEF,IAAI,yBAAyB,GAAG,KAAK,CAAC;QACtC,IAAI,iCAAiC,GAAG,KAAK,CAAC;QAC9C,MAAM,KAAK,GAAuB,EAAE,CAAC;QACrC,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,uBAAuB,CAAC,EAAE,CAAC;gBACnE,MAAM,IAAI,GAAG,IAAI,gBAAgB,CAChC,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,iBAAiB,EACjB,QAAQ,EACR,IAAI,CAAC,MAAM,EACX,uBAAuB,CAAC,CAAC;gBAC1B,IAAI,CAAC,OAAO,GAAG;oBACd,OAAO,EAAE,sBAAsB,CAAC,EAAE;oBAClC,KAAK,EAAE,EAAE;oBACT,SAAS,EAAE,CAAC,IAAI,CAAC;iBACjB,CAAC;gBACF,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjB,yBAAyB,GAAG,yBAAyB,IAAI,CAAC,CAAC,KAAK,CAAC,mBAAmB,CAAC;gBACrF,iCAAiC,GAAG,iCAAiC,IAAI,CAAC,CAAC,KAAK,CAAC,2BAA2B,CAAC;YAC9G,CAAC;QACF,CAAC;QACD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,EAAE,yBAAyB,EAAE,iCAAiC,CAAC,CAAC;QAChH,CAAC;QACD,OAAO,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACvD,CAAC;IAEO,uBAAuB,CAC9B,QAAgB,EAChB,QAA2E,EAC3E,yBAAmC,EACnC,iCAA2C;QAE3C;;;;;;;;;;;;;;;UAeE;QACF,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,qBAAqB,EAAE;YAC1D,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;YAC1B,IAAI,EAAE,QAAQ,EAAE,IAAI,IAAI,SAAS;YACjC,KAAK,EAAE,QAAQ,EAAE,IAAI,KAAK,UAAU,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YAC1H,KAAK,EAAE,MAAM,CAAC,QAAQ,EAAE,IAAI,KAAK,UAAU,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAChG,qBAAqB,EAAE,QAAQ,EAAE,IAAI,KAAK,UAAU,IAAI,OAAO,QAAQ,CAAC,eAAe,EAAE,qBAAqB,KAAK,QAAQ;gBAC1H,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,qBAAqB,CAAC;gBACxD,CAAC,CAAC,SAAS;YACZ,yCAAyC,EAAE,QAAQ,EAAE,IAAI,KAAK,UAAU,IAAI,OAAO,QAAQ,CAAC,eAAe,EAAE,yCAAyC,KAAK,QAAQ;gBAClK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yCAAyC,CAAC;gBAC5E,CAAC,CAAC,SAAS;YACZ,yBAAyB,EAAE,yBAAyB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACzE,iCAAiC,EAAE,iCAAiC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;SACzF,CAAC,CAAC;IACJ,CAAC;IAEO,qBAAqB,CAAC,OAAiC;QAC9D,QAAQ,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAClC,KAAK,GAAG,CAAC,CAAC,CAAC;gBACV,OAAO,GAAG,CAAC;YACZ,CAAC;YACD,KAAK,GAAG,CAAC,CAAC,CAAC;gBACV,OAAO,GAAG,CAAC;YACZ,CAAC;YACD,KAAK,GAAG,CAAC,CAAC,CAAC;gBACV,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC;YAC/D,CAAC;YACD,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,IAAI,CAAC;YACV,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC;YACT,KAAK,GAAG,CAAC,CAAC,CAAC;gBACV,OAAO,OAAO,CAAC,gBAAgB,CAAC;YACjC,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACT,OAAO,SAAS,CAAC;YAClB,CAAC;QACF,CAAC;IACF,CAAC;IAEM,KAAK,CAAC,qBAAqB,CACjC,IAAsB,EACtB,KAA+B;QAE/B,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,aAAa,CACpB,OAAiC,EACjC,IAAqB,EACrB,QAAyB,EACzB,aAAsC;QAEtC,IAAI,OAAO,CAAC,gBAAgB,KAAK,GAAG,EAAE,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,0BAA0B,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,SAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtF,OAAO,KAAK,CAAC;YACd,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;YACnD,OAAO,GAAG,KAAK,QAAQ,CAAC;QACzB,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;;AAtOsB,kDAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAyO3F,SAAS,4BAA4B,CACpC,OAA8B,EAC9B,uBAAgD;IAEhD,OAAO,CACN,CAAC,CAAC,uBAAuB,CAAC,eAAe,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;WAC/E,CAAC,CAAC,uBAAuB,CAAC,eAAe;YAC3C,CAAC,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;WACjI,CAAC,CAAC,uBAAuB,CAAC,qBAAqB,IAAI,OAAO,CAAC,SAAS,CAAC,CACxE,CAAC;AACH,CAAC;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,QAA6B,EAC7B,MAAgC,EAChC,aAA4B,EAC5B,wBAAkD,EAClD,cAA8B,EAC9B,iBAAoC,EACpC,oBAA2D;IAE3D,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,cAAc,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACzF,EAAE,GAAG,EAAE;QACP,OAAO,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,QAAQ,CAAC,MAAM,EACrE,IAAI,gCAAgC,CAAC,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,wBAAwB,EAAE,cAAc,EAAE,iBAAiB,EAAE,oBAAoB,CAAC,EACxJ,GAAG,gCAAgC,CAAC,iBAAiB,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "completions.js", "sourceRoot": "../../src/"}