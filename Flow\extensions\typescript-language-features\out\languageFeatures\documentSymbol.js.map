{"version": 3, "sources": ["languageFeatures/documentSymbol.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HhG,4BAOC;AAhID,+CAAiC;AAGjC,8DAAmE;AAEnE,4EAA8D;AAC9D,kEAAoD;AAGpD,MAAM,aAAa,GAAG,CAAC,IAAY,EAAqB,EAAE;IACzD,QAAQ,IAAI,EAAE,CAAC;QACd,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;QACzD,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;QACvD,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC;QACrD,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC;QAC/D,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;QACzD,KAAK,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QACnE,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QACtE,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QACtE,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7D,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC1D,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAClE,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC7D,KAAK,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAClE,KAAK,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;QAC1E,KAAK,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,OAAO,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;IAClF,CAAC;IACD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;AACnC,CAAC,CAAC;AAEF,MAAM,gCAAgC;IAErC,YACkB,MAAgC,EAChC,cAAqD;QADrD,WAAM,GAAN,MAAM,CAA0B;QAChC,mBAAc,GAAd,cAAc,CAAuC;IACnE,CAAC;IAEE,KAAK,CAAC,sBAAsB,CAAC,QAA6B,EAAE,KAA+B;QACjG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAA0B,EAAE,IAAI,EAAE,CAAC;QAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAChH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC;YAChE,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,mEAAmE;QACnE,MAAM,MAAM,GAA4B,EAAE,CAAC;QAC3C,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC7C,gCAAgC,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7E,CAAC;QACD,OAAO,MAAM,CAAC;IACf,CAAC;IAEO,MAAM,CAAC,cAAc,CAC5B,QAAoB,EACpB,MAA+B,EAC/B,IAA0B;QAE1B,IAAI,aAAa,GAAG,gCAAgC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7E,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;YAChD,OAAO,KAAK,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;QAChD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,UAAU,GAAG,gCAAgC,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAE/E,KAAK,MAAM,KAAK,IAAI,QAAQ,EAAE,CAAC;gBAC9B,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC7F,MAAM,aAAa,GAAG,gCAAgC,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;oBAC5G,aAAa,GAAG,aAAa,IAAI,aAAa,CAAC;oBAC/C,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACxB,CAAC;YACF,CAAC;YAED,IAAI,aAAa,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACzB,CAAC;QACF,CAAC;QAED,OAAO,aAAa,CAAC;IACtB,CAAC;IAEO,MAAM,CAAC,aAAa,CAAC,IAA0B,EAAE,KAAmB;QAC3E,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAChG,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QAEtB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB;gBAAE,KAAK,GAAG,SAAS,KAAK,EAAE,CAAC;gBAAC,MAAM;YACpE,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB;gBAAE,KAAK,GAAG,SAAS,KAAK,EAAE,CAAC;gBAAC,MAAM;QACrE,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,cAAc,CAC3C,KAAK,EACL,EAAE,EACF,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACxB,KAAK,EACL,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAG1D,MAAM,aAAa,GAAG,IAAA,6BAAiB,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC5D,IAAI,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC;YACxD,UAAU,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,UAAU,CAAC;IACnB,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,IAAoD;QACpF,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACrC,OAAO,KAAK,CAAC;QACd,CAAC;QACD,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;IAC/E,CAAC;CACD;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,MAAgC,EAChC,cAAqD;IAErD,OAAO,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,QAAQ,CAAC,MAAM,EACrE,IAAI,gCAAgC,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;AACzF,CAAC", "file": "documentSymbol.js", "sourceRoot": "../../src/"}