{"version": 3, "sources": ["test/words.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;gGAGgG;AAChG,oDAA4B;AAC5B,wDAA0C;AAC1C,uCAAyB;AACzB,2CAA6B;AAE7B,KAAK,CAAC,6BAA6B,EAAE,GAAG,EAAE;IACzC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,8CAA8C,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAE9H,SAAS,WAAW,CAAC,GAAgD;QACpE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC7B,OAAO,IAAI,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IAElD,SAAS,UAAU,CAAC,KAAa,EAAE,QAAgB;QAClD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAClC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAE9D,MAAM,WAAW,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;QAClE,gBAAM,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,IAAI,MAAM,CAAC,CAAC;QACvC,gBAAM,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC;QAC5D,gBAAM,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;IACnF,CAAC;IAED,IAAI,CAAC,aAAa,EAAE;QACnB,UAAU,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAC/C,UAAU,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAC/C,UAAU,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAC/C,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;QAC9C,UAAU,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;QAC9C,UAAU,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;QAC7C,UAAU,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;QAC7C,UAAU,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;QAC7C,UAAU,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC;QAC7C,UAAU,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC;QAC5C,UAAU,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAAC;QAC7C,UAAU,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC;IACjD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iBAAiB,EAAE;QACvB,UAAU,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;QACtE,UAAU,CAAC,oDAAoD,EAAE,EAAE,CAAC,CAAC;QACrE,UAAU,CAAC,qDAAqD,EAAE,KAAK,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAa,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;IAEpG,SAAS,gBAAgB,CAAC,IAAY,EAAE,aAAsB;QAC7D,KAAK,MAAM,GAAG,IAAI,kBAAkB,EAAE,CAAC;YACtC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YACnC,gBAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC,CAAC;YAClD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC;YAC/C,gBAAM,CAAC,EAAE,CAAC,SAAS,GAAG,GAAG,EAAE,iBAAiB,GAAG,CAAC,MAAM,OAAO,IAAI,SAAS,SAAS,KAAK,CAAC,CAAC;QAC3F,CAAC;IACF,CAAC;IAED,IAAI,CAAC,gBAAgB,EAAE;QACtB,gBAAgB,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACzD,gBAAgB,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC;QACzD,gBAAgB,CAAC,sCAAsC,EAAE,IAAI,CAAC,CAAC;QAC/D,gBAAgB,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC,CAAC,CAAC;AAEJ,CAAC,CAAC,CAAC", "file": "words.test.js", "sourceRoot": "../../src/"}