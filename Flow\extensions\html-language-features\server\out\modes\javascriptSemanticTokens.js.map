{"version": 3, "sources": ["modes/javascriptSemanticTokens.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAKhG,wDAQC;AAED,8CAsBC;AAhCD,SAAgB,sBAAsB;IACrC,IAAI,UAAU,CAAC,MAAM,yBAAgB,EAAE,CAAC;QACvC,OAAO,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IACD,IAAI,cAAc,CAAC,MAAM,4BAAoB,EAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC;AACzD,CAAC;AAED,QAAe,CAAC,CAAC,iBAAiB,CAAC,iBAAqC,EAAE,QAAsB,EAAE,QAAgB;IACjH,MAAM,EAAE,KAAK,EAAE,GAAG,iBAAiB,CAAC,iCAAiC,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,MAAyC,CAAC,CAAC;IAE5K,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;QACnC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAM,gBAAgB,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAEpC,MAAM,SAAS,GAAG,8BAA8B,CAAC,gBAAgB,CAAC,CAAC;QACnE,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC7B,SAAS;QACV,CAAC;QAED,MAAM,cAAc,GAAG,kCAAkC,CAAC,gBAAgB,CAAC,CAAC;QAC5E,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC7C,MAAM;YACL,KAAK,EAAE,QAAQ;YACf,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,SAAS;YAClB,WAAW,EAAE,cAAc;SAC3B,CAAC;IACH,CAAC;AACF,CAAC;AAqCD,SAAS,8BAA8B,CAAC,gBAAwB;IAC/D,IAAI,gBAAgB,6CAAmC,EAAE,CAAC;QACzD,OAAO,CAAC,gBAAgB,0CAAkC,CAAC,GAAG,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC;AAED,SAAS,kCAAkC,CAAC,gBAAwB;IACnE,OAAO,gBAAgB,6CAAmC,CAAC;AAC5D,CAAC;AAED,MAAM,UAAU,GAAa,EAAE,CAAC;AAChC,UAAU,yBAAiB,GAAG,OAAO,CAAC;AACtC,UAAU,wBAAgB,GAAG,MAAM,CAAC;AACpC,UAAU,6BAAqB,GAAG,WAAW,CAAC;AAC9C,UAAU,6BAAqB,GAAG,WAAW,CAAC;AAC9C,UAAU,iCAAyB,GAAG,eAAe,CAAC;AACtD,UAAU,wBAAgB,GAAG,MAAM,CAAC;AACpC,UAAU,6BAAqB,GAAG,WAAW,CAAC;AAC9C,UAAU,4BAAoB,GAAG,UAAU,CAAC;AAC5C,UAAU,8BAAsB,GAAG,YAAY,CAAC;AAChD,UAAU,4BAAoB,GAAG,UAAU,CAAC;AAC5C,UAAU,6BAAoB,GAAG,UAAU,CAAC;AAC5C,UAAU,2BAAkB,GAAG,QAAQ,CAAC;AAExC,MAAM,cAAc,GAAa,EAAE,CAAC;AACpC,cAAc,6BAAqB,GAAG,OAAO,CAAC;AAC9C,cAAc,mCAA2B,GAAG,aAAa,CAAC;AAC1D,cAAc,gCAAwB,GAAG,UAAU,CAAC;AACpD,cAAc,8BAAsB,GAAG,QAAQ,CAAC;AAChD,cAAc,6BAAqB,GAAG,OAAO,CAAC;AAC9C,cAAc,sCAA8B,GAAG,gBAAgB,CAAC", "file": "javascriptSemanticTokens.js", "sourceRoot": "../../src/"}