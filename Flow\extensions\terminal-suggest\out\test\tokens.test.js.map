{"version": 3, "sources": ["test/tokens.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAEhG,iBAAe;AACf,6CAA0C;AAC1C,sCAAoD;AAGpD,KAAK,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAC9B,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC3B,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,4BAAoB,CAAC;IACjH,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC5B,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,6BAAqB,CAAC;IAC9H,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC5C,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,4BAAoB,CAAC;IACvH,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,kCAAkC,EAAE,GAAG,EAAE;QAC7C,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,6BAAqB,CAAC;IAC5H,CAAC,CAAC,CAAC;IACH,KAAK,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAC9B,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE;YACd,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,4BAAoB,CAAC;QACnI,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE;YACd,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,4BAAoB,CAAC;QACjI,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;YACf,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,4BAAoB,CAAC;QACrI,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;YACf,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,4BAAoB,CAAC;QACrI,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IACH,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE;QAClB,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE;YAC3B,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,CAAC,MAAM,EAAE,4CAA+B,4BAAoB,CAAC;QAChJ,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,EAAE,GAAG,EAAE;YAC5B,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,kBAAkB,EAAE,cAAc,EAAE,kBAAkB,CAAC,MAAM,EAAE,4CAA+B,6BAAqB,CAAC;QAC7J,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE;YACvB,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE,cAAc,EAAE,wBAAwB,CAAC,MAAM,EAAE,4CAA+B,4BAAoB,CAAC;QACxK,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE;YACvC,IAAA,yBAAW,EAAC,IAAA,qBAAY,EAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,cAAc,EAAE,8BAA8B,CAAC,MAAM,EAAE,4CAA+B,6BAAqB,CAAC;QACrL,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "tokens.test.js", "sourceRoot": "../../src/"}