{"version": 3, "sources": ["languageFeatures/rename.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoMhG,4BAYC;AA9MD,2CAA6B;AAC7B,+CAAiC;AAEjC,0EAA4D;AAC5D,yCAAsC;AAEtC,kEAAoD;AACpD,4DAAkF;AAElF,wEAA8F;AAW9F,MAAM,wBAAwB;IAE7B,YACkB,QAA6B,EAC7B,MAAgC,EAChC,wBAAkD;QAFlD,aAAQ,GAAR,QAAQ,CAAqB;QAC7B,WAAM,GAAN,MAAM,CAA0B;QAChC,6BAAwB,GAAxB,wBAAwB,CAA0B;IAChE,CAAC;IAEE,KAAK,CAAC,aAAa,CACzB,QAA6B,EAC7B,QAAyB,EACzB,KAA+B;QAE/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACf,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;gBACtC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;oBAC3B,OAAO,OAAO,CAAC,MAAM,CAAe,UAAU,CAAC,qBAAqB,CAAC,CAAC;gBACvE,CAAC;gBACD,OAAO,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAClE,CAAC;YACD,KAAK,kBAAkB,CAAC,CAAC,CAAC;gBACzB,OAAO,QAAQ,CAAC,KAAK;qBACnB,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC;qBACtC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3C,CAAC;QACF,CAAC;IACF,CAAC;IAEM,KAAK,CAAC,kBAAkB,CAC9B,QAA6B,EAC7B,QAAyB,EACzB,OAAe,EACf,KAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAClE,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAChD,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACf,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;gBACtC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;oBAC3B,OAAO,OAAO,CAAC,MAAM,CAAuB,UAAU,CAAC,qBAAqB,CAAC,CAAC;gBAC/E,CAAC;gBAED,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;oBAC7B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,YAAY,EAAE,UAAU,CAAC,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;oBACzG,IAAI,KAAK,EAAE,CAAC;wBACX,OAAO,KAAK,CAAC;oBACd,CAAC;yBAAM,CAAC;wBACP,OAAO,OAAO,CAAC,MAAM,CAAuB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uCAAuC,CAAC,CAAC,CAAC;oBACrG,CAAC;gBACF,CAAC;gBAED,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACrD,CAAC;YACD,KAAK,kBAAkB,CAAC,CAAC,CAAC;gBACzB,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC;wBACvB,IAAI;wBACJ,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAwB,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;qBACvE,CAAC,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;QACF,CAAC;IACF,CAAC;IAEM,KAAK,CAAC,UAAU,CACtB,QAA6B,EAC7B,QAAyB,EACzB,KAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,kDAAkD;QAClD,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC;YACvC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAAC;YAClG,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,QAAQ,CAAC,EACvD,CAAC;YACF,MAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAC9E,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACpD,OAAO,SAAS,CAAC;YAClB,CAAC;YAED,OAAO,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QAClE,CAAC;QAED,MAAM,IAAI,GAA4B;YACrC,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,EAAE,QAAQ,CAAC;YACpE,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,KAAK;SACrB,CAAC;QAEF,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;YAC7C,IAAI,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAClE,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACpD,OAAO,SAAS,CAAC;YAClB,CAAC;YACD,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC;IACJ,CAAC;IAEO,+BAA+B,CAAC,QAA6B,EAAE,QAAyB;QAC/F,IAAI,CAAC,CAAC,WAAW,CAAC,eAAe,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACvH,OAAO,KAAK,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QACvG,OAAO,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAEO,UAAU,CACjB,SAAyC,EACzC,OAAe;QAEf,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QACxC,KAAK,MAAM,SAAS,IAAI,SAAS,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACxD,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBACvC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,EACjE,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC,GAAG,OAAO,GAAG,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC;YACvE,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,UAAU,CACvB,YAAoB,EACpB,eAAuB,EACvB,OAAe,EACf,KAA+B;QAE/B,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACvC,CAAC;aACI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;YAClE,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACxG,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEhD,MAAM,IAAI,GAA8D;YACvE,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,YAAY;YACzB,WAAW,EAAE,WAAW;SACxB,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACjF,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,KAAK,GAAG,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QACzF,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QAC9E,OAAO,KAAK,CAAC;IACd,CAAC;CACD;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,QAA6B,EAC7B,MAAgC,EAChC,wBAAkD;IAElD,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACxD,EAAE,GAAG,EAAE;QACP,OAAO,MAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,EAC/D,IAAI,wBAAwB,CAAC,QAAQ,EAAE,MAAM,EAAE,wBAAwB,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "rename.js", "sourceRoot": "../../src/"}