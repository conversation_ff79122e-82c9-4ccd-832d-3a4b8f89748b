{"version": 3, "sources": ["fig/shell-parser/test/command.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAEhG,6CAA8C;AAC9C,wCAAiD;AAEjD,KAAK,CAAC,8BAA8B,EAAE,GAAG,EAAE;IAC1C,MAAM,OAAO,GAAG;QACf,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,CAAC,EAAE,KAAK;KACR,CAAC;IACF,MAAM,YAAY,GAAG,CAAC,OAAuB,EAAE,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAEnG,IAAI,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC3C,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,SAAS,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QAC5E,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;QACjF,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACvE,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,EAAE;YACrE,SAAS;YACT,QAAQ;YACR,EAAE;SACF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,4BAA4B,EAAE,GAAG,EAAE;QACvC,+BAA+B;QAC/B,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QACvE,oCAAoC;QACpC,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;QAC1E,6BAA6B;QAC7B,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACvF,gBAAgB;QAChB,IAAA,6BAAe,EAAC,YAAY,CAAC,IAAA,oBAAU,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "command.test.js", "sourceRoot": "../../../../src/"}