{"version": 3, "sources": ["test/completions/code.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;AA2DhG,kDA0CC;AAnGD,iBAAe;AACf,kEAAwD;AACxD,wCAAwE;AACxE,oFAAyE;AAE5D,QAAA,6BAA6B,GAAG;IAC5C,aAAa;IACb,kBAAkB;IAClB,4BAA4B;IAC5B,IAAI;IACJ,oCAAoC;IACpC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;IACH,gBAAgB;IAChB,uBAAuB;IACvB,sBAAsB;IACtB,oCAAoC;IACpC,sBAAsB;IACtB,eAAe;IACf,uBAAuB;IACvB,wBAAwB;IACxB,gCAAgC;IAChC,QAAQ;IACR,iCAAiC;IACjC,6BAA6B;IAC7B,6DAA6D;IAC7D,mBAAmB;IACnB,mBAAmB;IACnB,yCAAyC;IACzC,eAAe;IACf,uBAAuB;IACvB,yCAAyC;IACzC,cAAc;IACd,eAAe;IACf,gBAAgB;IAChB,iCAAiC;IACjC,gBAAgB;IAChB,iBAAiB;IACjB,UAAU;IACV,eAAe;IACf,aAAa;IACb,sCAAsC;IACtC,uBAAuB;IACvB,WAAW;IACX,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,MAAM;IACN,QAAQ;IACR,SAAS;CACT,CAAC;AAEF,SAAgB,mBAAmB,CAAC,UAAkB;IACrD,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpH,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,EAAE,uBAAuB,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;IAC3R,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAClF,MAAM,WAAW,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAElC,MAAM,WAAW,GAAgB,EAAE,CAAC;IACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5C,MAAM,mBAAmB,GAAG,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,KAAK,cAAkB,CAAC,IAAI,CAAC,CAAC,CAAE,cAA0B,CAAC,WAAW,CAAC,CAAC,CAAE,uBAAkC,CAAC,WAAW,EAAE,CAAC,CAAC;QACrM,MAAM,KAAK,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;QAC3C,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACpJ,CAAC;IAED,OAAO;QACN,qBAAqB;QACrB,GAAG,WAAW;QAEd,kBAAkB;QAClB,EAAE,KAAK,EAAE,GAAG,UAAU,IAAI,EAAE,mBAAmB,EAAE,qCAA6B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAChJ,EAAE,KAAK,EAAE,GAAG,UAAU,aAAa,EAAE,mBAAmB,EAAE,aAAa,EAAE;QACzE,EAAE,KAAK,EAAE,GAAG,UAAU,WAAW,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACpG,EAAE,KAAK,EAAE,GAAG,UAAU,mBAAmB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAC5G,EAAE,KAAK,EAAE,GAAG,UAAU,YAAY,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACrG,EAAE,KAAK,EAAE,GAAG,UAAU,4BAA4B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACrH,EAAE,KAAK,EAAE,GAAG,UAAU,mCAAmC,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAC5H,EAAE,KAAK,EAAE,GAAG,UAAU,WAAW,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACpG,EAAE,KAAK,EAAE,GAAG,UAAU,oBAAoB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAC/G,EAAE,KAAK,EAAE,GAAG,UAAU,cAAc,EAAE;QACtC,EAAE,KAAK,EAAE,GAAG,UAAU,wBAAwB,EAAE,mBAAmB,EAAE,CAAC,UAAU,CAAC,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACnJ,EAAE,KAAK,EAAE,GAAG,UAAU,0BAA0B,EAAE,mBAAmB,EAAE,CAAC,UAAU,CAAC,EAAE;QACrF,EAAE,KAAK,EAAE,GAAG,UAAU,wBAAwB,EAAE,mBAAmB,EAAE,CAAC,UAAU,CAAC,EAAE;QACnF,EAAE,KAAK,EAAE,GAAG,UAAU,UAAU,EAAE,mBAAmB,EAAE,UAAU,EAAE;QACnE,EAAE,KAAK,EAAE,GAAG,UAAU,WAAW,EAAE,mBAAmB,EAAE,WAAW,EAAE;QACrE,EAAE,KAAK,EAAE,GAAG,UAAU,qBAAqB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAChH,EAAE,KAAK,EAAE,GAAG,UAAU,sBAAsB,EAAE,mBAAmB,EAAE,qCAA6B,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,mBAAmB,CAAC,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACzM,EAAE,KAAK,EAAE,GAAG,UAAU,oBAAoB,EAAE,mBAAmB,EAAE,qCAA6B,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,iBAAiB,CAAC,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACrM,EAAE,KAAK,EAAE,GAAG,UAAU,eAAe,EAAE,mBAAmB,EAAE,eAAe,EAAE;QAC7E,EAAE,KAAK,EAAE,GAAG,UAAU,gBAAgB,EAAE,mBAAmB,EAAE,eAAe,EAAE;QAE9E,oBAAoB;QACpB,EAAE,KAAK,EAAE,GAAG,UAAU,aAAa,EAAE,mBAAmB,EAAE,qCAA6B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;KACzJ,CAAC;AACH,CAAC;AAEY,QAAA,aAAa,GAAe;IACxC,IAAI,EAAE,MAAM;IACZ,eAAe,EAAE,cAAkB;IACnC,iBAAiB,EAAE,MAAM;IACzB,SAAS,EAAE,mBAAmB,CAAC,MAAM,CAAC;CACtC,CAAC", "file": "code.test.js", "sourceRoot": "../../../src/"}