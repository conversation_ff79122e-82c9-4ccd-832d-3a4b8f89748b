{"version": 3, "sources": ["completions/upstream/killall.ts"], "names": [], "mappings": ";AAAA,qBAAqB;;AAErB,MAAM,OAAO,GAAG;IACf,KAAK;IACL,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,KAAK;IACL,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,KAAK;IACL,MAAM;IACN,MAAM;IACN,6BAA6B;IAC7B,UAAU;IACV,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,IAAI;IACJ,MAAM;IACN,MAAM;IACN,QAAQ;IACR,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;CACN,CAAC;AAEF,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,SAAS;IACf,WAAW,EAAE,wBAAwB;IACrC,IAAI,EAAE;QACL,IAAI,EAAE,cAAc;QACpB,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE;YACX,uCAAuC;YACvC,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,yBAAyB,CAAC;YACjD,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CACpB,GAAG;iBACD,IAAI,EAAE;iBACN,KAAK,CAAC,IAAI,CAAC;iBACX,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACb,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC1C,MAAM,KAAK,GAAG,WAAW,KAAK,CAAC,CAAC,CAAC;gBACjC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACnD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChC,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBACjC,OAAO;oBACN,IAAI;oBACJ,WAAW,EAAE,IAAI;oBACjB,QAAQ,EACP,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK;wBACrD,CAAC,CAAC,EAAE;wBACJ,CAAC,CAAC,EAAE;oBACN,IAAI,EAAE,KAAK;wBACV,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,GAAG,CAAC,CAAC;wBAC3C,CAAC,CAAC,sBAAsB;iBACzB,CAAC;YACH,CAAC,CAAC;SACJ;KACD;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,2DAA2D;SACxE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,sFAAsF;SACvF;QACD;YACC,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,uBAAuB;SACpC;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,kDAAkD;SAC/D;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,kDAAkD;SAC/D;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,gDAAgD;SAC7D;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,YAAY;SACzB;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,sBAAsB;SACnC;QACD,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAC3B,IAAI,EAAE,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE;YACnC,WAAW,EAAE,QAAQ,MAAM,CAAC,WAAW,EAAE,kBAAkB;SAC3D,CAAC,CAAC;QACH;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,qEAAqE;YACtE,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE;oBACX,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,oCAAoC,CAAC;oBAC5D,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CACpB,GAAG;yBACD,IAAI,EAAE;yBACN,KAAK,CAAC,IAAI,CAAC;yBACX,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;wBACnB,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,yBAAyB;qBAC/B,CAAC,CAAC;iBACL;aACD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,gEAAgE;YACjE,IAAI,EAAE;gBACL,IAAI,EAAE,KAAK;aACX;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,2DAA2D;YACxE,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;aACZ;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,oDAAoD;SACjE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,qBAAqB;SAClC;KACD;CACD,CAAC;AACF,kBAAe,cAAc,CAAC", "file": "killall.js", "sourceRoot": "../../../src/"}