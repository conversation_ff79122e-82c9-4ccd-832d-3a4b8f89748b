{"version": 3, "sources": ["tsServer/pluginPathsProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;gGAGgG;AAChG,2CAA6B;AAC7B,+CAAiC;AACjC,wEAA8E;AAI9E,MAAa,6BAA6B;IAEzC,YACS,aAA6C;QAA7C,kBAAa,GAAb,aAAa,CAAgC;IAClD,CAAC;IAEE,mBAAmB,CAAC,aAA6C;QACvE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACpC,CAAC;IAEM,cAAc;QACpB,MAAM,WAAW,GAAG,EAAE,CAAC;QACvB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC;YACjE,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,WAAW,CAAC;IACpB,CAAC;IAEO,iBAAiB,CAAC,UAAkB;QAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrB,CAAC;QAED,MAAM,aAAa,GAAG,oDAA6B,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;QACxF,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,CAAC,aAAa,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,EAAE,CAAC;aAC9C,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC;IAC7E,CAAC;CACD;AA/BD,sEA+BC", "file": "pluginPathsProvider.js", "sourceRoot": "../../src/"}