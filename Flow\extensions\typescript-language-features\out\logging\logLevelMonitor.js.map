{"version": 3, "sources": ["logging/logLevelMonitor.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,kEAAkE;AAClE,8CAA8C;AAG9C,MAAa,eAAgB,SAAQ,oBAAU;IAM9C,YAA6B,OAAgC;QAC5D,KAAK,EAAE,CAAC;QADoB,YAAO,GAAP,OAAO,CAAyB;QAG5D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAE/G,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE,CAAC;YACxC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC9B,CAAC;IACF,CAAC;IAEO,qBAAqB,CAAC,KAAsC;QACnE,MAAM,eAAe,GAAG,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;QACtF,IAAI,CAAC,eAAe,EAAE,CAAC;YACtB,OAAO;QACR,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,yBAAyB,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;IACxF,CAAC;IAED,IAAY,QAAQ;QACnB,OAAO,gCAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,GAAG,CAAS,eAAe,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC;IAC/H,CAAC;IAED;;;OAGG;IACH,IAAY,kBAAkB;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAqB,eAAe,CAAC,yBAAyB,CAAC,CAAC;QAE/G,IAAI,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;gBACpD,OAAO,IAAI,CAAC;YACb,CAAC;QACF,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,IAAY,WAAW;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAsB,eAAe,CAAC,6BAA6B,CAAC,IAAI,KAAK,CAAC;IAClH,CAAC;IAEO,2BAA2B;QAClC,MAAM,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACzG,MAAM,qBAAqB,GAAG,IAAI,IAAI,CAAC,sBAAsB,GAAG,4BAA4B,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QAE3G,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,KAAK,gCAAgB,CAAC,GAAG,IAAI,qBAAqB,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;YACjH,OAAO,IAAI,CAAC;QACb,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEO,qBAAqB;QAS5B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACnC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,sEAAsE,CAAC,EACrF;YACC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC;YACvC,MAAM,+BAAuB;SAC7B,EACD;YACC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC;YACxC,MAAM,+BAAuB;SAC7B,CAAC;aACD,IAAI,CAAC,SAAS,CAAC,EAAE;YACjB,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChB,OAAO;YACR,CAAC;YACD,IAAI,SAAS,CAAC,MAAM,kCAA0B,EAAE,CAAC;gBAChD,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACnG,CAAC;iBAAM,IAAI,SAAS,CAAC,MAAM,kCAA0B,EAAE,CAAC;gBACvD,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,6BAA6B,EAAE,IAAI,CAAC,CAAC;YAC7F,CAAC;YACD,OAAO;QACR,CAAC,CAAC,CAAC;IACL,CAAC;;AAxFF,0CAyFC;AAvFwB,iCAAiB,GAAG,yBAAyB,CAAC;AAC9C,yCAAyB,GAAG,qCAAqC,CAAC;AAClE,6CAA6B,GAAG,yCAAyC,CAAC", "file": "logLevelMonitor.js", "sourceRoot": "../../src/"}