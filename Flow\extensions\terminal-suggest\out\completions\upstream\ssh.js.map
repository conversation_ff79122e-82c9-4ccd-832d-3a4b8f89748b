{"version": 3, "sources": ["completions/upstream/ssh.ts"], "names": [], "mappings": ";;;AAAA,MAAM,cAAc,GAAG,kCAAkC,CAAC,CAAC,yDAAyD;AAEpH,MAAM,mBAAmB,GAAG,CAC3B,IAAY,EACZ,QAAgB,EAChB,IAAY,EACH,EAAE;IACX,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;QACnE,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;IACD,IACC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;QACxB,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC;QACzB,QAAQ,KAAK,GAAG,EACf,CAAC;QACF,OAAO,CACN,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC;YAC3B,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YACtD,IAAI,CACJ,CAAC;IACH,CAAC;IACD,OAAO,QAAQ,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC9D,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,KAAK,EAC3B,IAAY,EACZ,mBAA+C,EAC/C,IAAY,EACZ,QAAgB,EACf,EAAE;IACH,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;IAE/D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,mBAAmB,CAAC;QAC5C,OAAO,EAAE,KAAK;QACd,iEAAiE;QACjE,IAAI,EAAE,CAAC,YAAY,CAAC;KACpB,CAAC,CAAC;IACH,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IAElE,0CAA0C;IAC1C,MAAM,QAAQ,GAAG,WAAW;SAC1B,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;SAC3D,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEpC,sCAAsC;IACtC,MAAM,YAAY,GAAe,MAAM,OAAO,CAAC,GAAG,CACjD,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CACrB,cAAc,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,QAAQ,CAAC,CACzD,CACD,CAAC;IAEF,kDAAkD;IAClD,OAAO,CAAC,GAAG,WAAW,EAAE,GAAG,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;AACjD,CAAC,CAAC;AAEW,QAAA,UAAU,GAAkB;IACxC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,EAAE;QACjD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,cAAc,CAAC;YACvC,OAAO,EAAE,KAAK;YACd,iEAAiE;YACjE,IAAI,EAAE,CAAC,GAAG,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC;SAClE,CAAC,CAAC;QAEH,OAAO,MAAM;aACX,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACb,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,IAAI,KAAK,EAAE,CAAC;gBACX,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;QACF,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;aACtE,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YACpB,IAAI,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,sCAAsC;YACpG,WAAW,EAAE,UAAU;SACvB,CAAC,CAAC,CAAC;IACN,CAAC;IACD,OAAO,EAAE,GAAG;CACZ,CAAC;AAEW,QAAA,WAAW,GAAkB;IACzC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE,OAAO,EAAE,EAAE;QACtD,MAAM,WAAW,GAAG,MAAM,cAAc,CACvC,QAAQ,EACR,mBAAmB,EACnB,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,EACpC,QAAQ,CACR,CAAC;QAEF,OAAO,WAAW;aAChB,MAAM,CACN,CAAC,IAAI,EAAE,EAAE,CACR,IAAI,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CACrE;aACA,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACf,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACxB,WAAW,EAAE,UAAU;YACvB,QAAQ,EAAE,EAAE;SACZ,CAAC,CAAC,CAAC;IACN,CAAC;CACD,CAAC;AAEF,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,KAAK;IACX,WAAW,EAAE,2BAA2B;IACxC,IAAI,EAAE;QACL,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,uCAAuC;QACpD,UAAU,EAAE,CAAC,kBAAU,EAAE,mBAAW,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;KAC9D;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,2CAA2C;SACxD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,2CAA2C;SACxD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,uCAAuC;SACpD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,uCAAuC;SACpD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,2DAA2D;SACxE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,4DAA4D;SACzE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,+EAA+E;YAChF,IAAI,EAAE;gBACL,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,kCAAkC;aAC/C;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,oHAAoH;SACrH;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,6DAA6D;YAC9D,IAAI,EAAE;gBACL,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,mCAAmC;aAChD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,+DAA+D;YAChE,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,0BAA0B;aACvC;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,kEAAkE;YACnE,IAAI,EAAE;gBACL,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,4BAA4B;aACzC;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,sDAAsD;YACnE,IAAI,EAAE;gBACL,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,iCAAiC;gBAC9C,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,gEAAgE;SACjE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,yDAAyD;SACtE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,4HAA4H;YAC7H,IAAI,EAAE;gBACL,IAAI,EAAE,QAAQ;aACd;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,4FAA4F;YAC7F,YAAY,EAAE,IAAI;YAClB,IAAI,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,qGAAqG;SACtG;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,sEAAsE;SACvE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,2HAA2H;YAC5H,IAAI,EAAE;gBACL,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,0DAA0D;aACvE;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,uDAAuD;YACpE,IAAI,EAAE;gBACL,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,0CAA0C;aACvD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,mEAAmE;YACpE,YAAY,EAAE,IAAI;SAClB;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,qJAAqJ;YACtJ,IAAI,EAAE;gBACL,IAAI,EAAE,UAAU;aAChB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,iCAAiC;SAC9C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,wEAAwE;SACzE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,0DAA0D;YACvE,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,6CAA6C;aAC1D;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,0EAA0E;YAC3E,YAAY,EAAE,IAAI;YAClB,IAAI,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,WAAW,EACV,gEAAgE;gBACjE,WAAW,EAAE;oBACZ,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,WAAW,EAAE;oBACrB,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,iCAAiC,EAAE;oBAC3C,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAClB,EAAE,IAAI,EAAE,SAAS,EAAE;oBACnB,EAAE,IAAI,EAAE,qBAAqB,EAAE;oBAC/B,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,kBAAkB,EAAE;oBAC5B,EAAE,IAAI,EAAE,oBAAoB,EAAE;oBAC9B,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,2BAA2B,EAAE;oBACrC,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,yBAAyB,EAAE;oBACnC,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,UAAU,EAAE;oBACpB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,OAAO,EAAE;oBACjB,EAAE,IAAI,EAAE,8BAA8B,EAAE;oBACxC,EAAE,IAAI,EAAE,uBAAuB,EAAE;oBACjC,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,UAAU,EAAE;oBACpB,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,kCAAkC,EAAE;oBAC5C,EAAE,IAAI,EAAE,yBAAyB,EAAE;oBACnC,EAAE,IAAI,EAAE,wBAAwB,EAAE;oBAClC,EAAE,IAAI,EAAE,oBAAoB,EAAE;oBAC9B,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,0BAA0B,EAAE;oBACpC,EAAE,IAAI,EAAE,UAAU,EAAE;oBACpB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,yBAAyB,EAAE;oBACnC,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,SAAS,EAAE;oBACnB,EAAE,IAAI,EAAE,qBAAqB,EAAE;oBAC/B,EAAE,IAAI,EAAE,qBAAqB,EAAE;oBAC/B,EAAE,IAAI,EAAE,uBAAuB,EAAE;oBACjC,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAClB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,oBAAoB,EAAE;oBAC9B,EAAE,IAAI,EAAE,kBAAkB,EAAE;oBAC5B,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,eAAe,EAAE;iBACzB;aACD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,uCAAuC;YACpD,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,WAAW,EAAE,oBAAoB;aACjC;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,2EAA2E;SAC5E;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,2HAA2H;YAC5H,IAAI,EAAE;gBACL,IAAI,EAAE,oBAAoB;gBAC1B,WAAW,EAAE,0DAA0D;aACvE;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,uHAAuH;YACxH,IAAI,EAAE;gBACL,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,gCAAgC;gBAC7C,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,uEAAuE;SACxE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,+BAA+B;SAC5C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,6BAA6B;YAC1C,YAAY,EAAE,IAAI;SAClB;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,qCAAqC;SAClD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,0EAA0E;YAC3E,YAAY,EAAE,CAAC;SACf;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,4GAA4G;YAC7G,IAAI,EAAE;gBACL,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,6BAA6B;aAC1C;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,gIAAgI;YACjI,IAAI,EAAE;gBACL,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,4BAA4B;aACzC;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,wBAAwB;SACrC;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,yBAAyB;SACtC;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,gCAAgC;SAC7C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,wDAAwD;SACrE;KACD;CACD,CAAC;AAEF,kBAAe,cAAc,CAAC", "file": "ssh.js", "sourceRoot": "../../../src/"}