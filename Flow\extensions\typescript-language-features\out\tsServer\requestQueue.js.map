{"version": 3, "sources": ["tsServer/requestQueue.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAIhG,IAAY,mBAkBX;AAlBD,WAAY,mBAAmB;IAC9B;;OAEG;IACH,iEAAU,CAAA;IAEV;;OAEG;IACH,2EAAe,CAAA;IAEf;;;;;OAKG;IACH,+DAAS,CAAA;AACV,CAAC,EAlBW,mBAAmB,mCAAnB,mBAAmB,QAkB9B;AASD,MAAa,YAAY;IAAzB;QACkB,UAAK,GAAkB,EAAE,CAAC;QACnC,mBAAc,GAAW,CAAC,CAAC;IA4CpC,CAAC;IA1CA,IAAW,MAAM;QAChB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC1B,CAAC;IAEM,OAAO,CAAC,IAAiB;QAC/B,IAAI,IAAI,CAAC,YAAY,KAAK,mBAAmB,CAAC,MAAM,EAAE,CAAC;YACtD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;YAClC,OAAO,KAAK,IAAI,CAAC,EAAE,CAAC;gBACnB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,YAAY,KAAK,mBAAmB,CAAC,WAAW,EAAE,CAAC;oBACxE,MAAM;gBACP,CAAC;gBACD,EAAE,KAAK,CAAC;YACT,CAAC;YACD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACP,yFAAyF;YACzF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;IACF,CAAC;IAEM,OAAO;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAEM,uBAAuB,CAAC,GAAW;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;gBACvC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACxB,OAAO,IAAI,CAAC;YACb,CAAC;QACF,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,aAAa,CAAC,OAAe,EAAE,IAAS;QAC9C,OAAO;YACN,GAAG,EAAE,IAAI,CAAC,cAAc,EAAE;YAC1B,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO;YAChB,SAAS,EAAE,IAAI;SACf,CAAC;IACH,CAAC;CACD;AA9CD,oCA8CC", "file": "requestQueue.js", "sourceRoot": "../../src/"}