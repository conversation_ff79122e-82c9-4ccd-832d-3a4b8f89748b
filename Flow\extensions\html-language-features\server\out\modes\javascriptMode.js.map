{"version": 3, "sources": ["modes/javascriptMode.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGhG,8CAqTC;AApZD,8DAAkF;AAClF,mDAKyB;AACzB,8CAA2E;AAG3E,+CAAiC;AACjC,yEAAuF;AAEvF,MAAM,aAAa,GAAG,sFAAsF,CAAC;AAE7G,SAAS,sBAAsB,CAAC,UAAyB;IACxD,MAAM,eAAe,GAAuB,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,sBAAsB,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,gBAAgB,EAAE,EAAE,CAAC,oBAAoB,CAAC,OAAO,EAAE,sBAAsB,EAAE,KAAK,EAAE,CAAC;IAE3O,IAAI,mBAAmB,GAAG,4BAAY,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;IAC3E,MAAM,iBAAiB,GAAG,MAAM,CAAC,wCAAwC,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAC5G,MAAM,IAAI,GAA2B;YACpC,sBAAsB,EAAE,GAAG,EAAE,CAAC,eAAe;YAC7C,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC,mBAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC;YAC7D,aAAa,EAAE,CAAC,QAAQ,EAAE,EAAE;gBAC3B,IAAI,QAAQ,KAAK,mBAAmB,CAAC,GAAG,EAAE,CAAC;oBAC1C,OAAO,UAAU,CAAC;gBACnB,CAAC;gBACD,OAAO,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5F,CAAC;YACD,gBAAgB,EAAE,CAAC,QAAgB,EAAE,EAAE;gBACtC,IAAI,QAAQ,KAAK,mBAAmB,CAAC,GAAG,EAAE,CAAC;oBAC1C,OAAO,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;gBAC5C,CAAC;gBACD,OAAO,GAAG,CAAC,CAAC,wCAAwC;YACrD,CAAC;YACD,iBAAiB,EAAE,CAAC,QAAgB,EAAE,EAAE;gBACvC,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAI,QAAQ,KAAK,mBAAmB,CAAC,GAAG,EAAE,CAAC;oBAC1C,IAAI,GAAG,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACP,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACnC,CAAC;gBACD,OAAO;oBACN,OAAO,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC;oBACnD,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM;oBAC5B,cAAc,EAAE,GAAG,EAAE,CAAC,SAAS;iBAC/B,CAAC;YACH,CAAC;YACD,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE;YAC7B,qBAAqB,EAAE,CAAC,QAA4B,EAAE,EAAE,CAAC,aAAa;YACtE,QAAQ,EAAE,CAAC,IAAY,EAAE,SAA8B,EAAsB,EAAE;gBAC9E,IAAI,IAAI,KAAK,mBAAmB,CAAC,GAAG,EAAE,CAAC;oBACtC,OAAO,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACP,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;YACF,CAAC;YACD,UAAU,EAAE,CAAC,IAAY,EAAW,EAAE;gBACrC,IAAI,IAAI,KAAK,mBAAmB,CAAC,GAAG,EAAE,CAAC;oBACtC,OAAO,IAAI,CAAC;gBACb,CAAC;qBAAM,CAAC;oBACP,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;YACF,CAAC;YACD,eAAe,EAAE,CAAC,IAAY,EAAW,EAAE;gBAC1C,+FAA+F;gBAC/F,uCAAuC;gBACvC,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;oBACrC,OAAO,KAAK,CAAC;gBACd,CAAC;gBACD,OAAO,IAAI,CAAC;YAEb,CAAC;SACD,CAAC;QACF,OAAO,EAAE,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC;IACH,OAAO;QACN,KAAK,CAAC,kBAAkB,CAAC,UAAwB;YAChD,mBAAmB,GAAG,UAAU,CAAC;YACjC,OAAO,iBAAiB,CAAC;QAC1B,CAAC;QACD,sBAAsB;YACrB,OAAO,eAAe,CAAC;QACxB,CAAC;QACD,OAAO;YACN,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC;KACD,CAAC;AACH,CAAC;AAED,MAAM,aAAa,GAAG;IACrB,IAAI,EAAG,qEAAqE;IAC5E,IAAI,EAAE,wHAAwH;CAC9H,CAAC;AAEF,SAAgB,iBAAiB,CAAC,eAAwD,EAAE,UAAuC,EAAE,SAAoB;IACxJ,MAAM,WAAW,GAAG,IAAA,0CAAqB,EAAe,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC;IAE3I,MAAM,IAAI,GAAG,sBAAsB,CAAC,UAAU,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IACvG,MAAM,cAAc,GAAa,EAAE,CAAC;IAEpC,SAAS,kBAAkB,CAAC,QAAkB;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACnD,YAAY,CAAC,sBAAsB,GAAG,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,qBAAqB,EAAE,sBAAsB,CAAC;QACzG,YAAY,CAAC,gBAAgB,GAAG,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,qBAAqB,CAAC,gBAAgB,CAAC;IAC7F,CAAC;IAED,OAAO;QACN,KAAK;YACJ,OAAO,UAAU,CAAC;QACnB,CAAC;QACD,KAAK,CAAC,YAAY,CAAC,QAAsB,EAAE,QAAQ,GAAG,SAAS,CAAC,QAAQ;YACvE,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAE7B,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAClE,MAAM,iBAAiB,GAAoB,eAAe,CAAC,uBAAuB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACnG,MAAM,mBAAmB,GAAG,eAAe,CAAC,sBAAsB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACnF,OAAO,iBAAiB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAmB,EAAc,EAAE;gBACzI,OAAO;oBACN,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC;oBACrC,QAAQ,EAAE,kCAAkB,CAAC,KAAK;oBAClC,MAAM,EAAE,UAAU;oBAClB,OAAO,EAAE,EAAE,CAAC,4BAA4B,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;iBAChE,CAAC;YACH,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,KAAK,CAAC,UAAU,CAAC,QAAsB,EAAE,QAAkB,EAAE,gBAAiC;YAC7F,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,WAAW,GAAG,iBAAiB,CAAC,wBAAwB,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,4BAA4B,EAAE,KAAK,EAAE,4BAA4B,EAAE,KAAK,EAAE,CAAC,CAAC;YACrK,IAAI,CAAC,WAAW,EAAE,CAAC;gBAClB,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;YAC3C,CAAC;YACD,MAAM,YAAY,GAAG,YAAY,CAAC,UAAU,EAAE,IAAA,uBAAa,EAAC,UAAU,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;YAC1G,OAAO;gBACN,YAAY,EAAE,KAAK;gBACnB,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBACtC,MAAM,IAAI,GAAuB;wBAChC,UAAU;wBACV,GAAG,EAAE,QAAQ,CAAC,GAAG;wBACjB,MAAM,EAAE,MAAM;qBACd,CAAC;oBACF,OAAO;wBACN,GAAG,EAAE,QAAQ,CAAC,GAAG;wBACjB,QAAQ,EAAE,QAAQ;wBAClB,KAAK,EAAE,KAAK,CAAC,IAAI;wBACjB,QAAQ,EAAE,KAAK,CAAC,QAAQ;wBACxB,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;wBAC7B,QAAQ,EAAE,wBAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC;wBACpD,IAAI;qBACJ,CAAC;gBACH,CAAC,CAAC;aACF,CAAC;QACH,CAAC;QACD,KAAK,CAAC,SAAS,CAAC,QAAsB,EAAE,IAAoB;YAC3D,IAAI,IAAA,oCAAoB,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;gBACpE,MAAM,OAAO,GAAG,iBAAiB,CAAC,yBAAyB,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;gBACtJ,IAAI,OAAO,EAAE,CAAC;oBACb,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,oBAAoB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;oBAC5D,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,oBAAoB,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;oBACpE,OAAO,IAAI,CAAC,IAAI,CAAC;gBAClB,CAAC;YACF,CAAC;YACD,OAAO,IAAI,CAAC;QACb,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,QAAsB,EAAE,QAAkB;YACvD,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,IAAI,GAAG,iBAAiB,CAAC,sBAAsB,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrG,IAAI,IAAI,EAAE,CAAC;gBACV,MAAM,QAAQ,GAAG,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC5D,OAAO;oBACN,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC;oBAC9C,QAAQ,EAAE,CAAC,eAAe,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACvD,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACb,CAAC;QACD,KAAK,CAAC,eAAe,CAAC,QAAsB,EAAE,QAAkB;YAC/D,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,QAAQ,GAAG,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC;YACnH,IAAI,QAAQ,EAAE,CAAC;gBACd,MAAM,GAAG,GAAkB;oBAC1B,eAAe,EAAE,QAAQ,CAAC,iBAAiB;oBAC3C,eAAe,EAAE,QAAQ,CAAC,aAAa;oBACvC,UAAU,EAAE,EAAE;iBACd,CAAC;gBACF,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAE7B,MAAM,SAAS,GAAyB;wBACvC,KAAK,EAAE,EAAE;wBACT,aAAa,EAAE,SAAS;wBACxB,UAAU,EAAE,EAAE;qBACd,CAAC;oBAEF,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACpE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;wBACnC,MAAM,KAAK,GAAG,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;wBACtD,MAAM,SAAS,GAAyB;4BACvC,KAAK,EAAE,KAAK;4BACZ,aAAa,EAAE,EAAE,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC;yBACvD,CAAC;wBACF,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC;wBACzB,SAAS,CAAC,UAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACtC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACtB,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;wBACxE,CAAC;oBACF,CAAC,CAAC,CAAC;oBACH,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACpE,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAChC,CAAC,CAAC,CAAC;gBACH,OAAO,GAAG,CAAC;YACZ,CAAC;YACD,OAAO,IAAI,CAAC;QACb,CAAC;QACD,KAAK,CAAC,QAAQ,CAAC,QAAsB,EAAE,QAAkB,EAAE,OAAe;YACzE,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,kBAAkB,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,EAAE,SAAS,EAAE,GAAG,iBAAiB,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;YAC1F,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChB,OAAO,IAAI,CAAC;YACb,CAAC;YACD,MAAM,WAAW,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,UAAU,CAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;YAE5G,MAAM,KAAK,GAAe,EAAE,CAAC;YAC7B,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE;gBAC7B,KAAK,CAAC,IAAI,CAAC;oBACV,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC;oBACpD,OAAO,EAAE,OAAO;iBAChB,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,OAAO;gBACN,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE;aAClC,CAAC;QACH,CAAC;QACD,KAAK,CAAC,qBAAqB,CAAC,QAAsB,EAAE,QAAkB;YACrE,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5H,MAAM,GAAG,GAAwB,EAAE,CAAC;YACpC,KAAK,MAAM,KAAK,IAAI,UAAU,IAAI,EAAE,EAAE,CAAC;gBACtC,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;oBAC9C,GAAG,CAAC,IAAI,CAAC;wBACR,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC;wBACnD,IAAI,EAAE,SAAS,CAAC,IAAI,KAAK,kBAAkB,CAAC,CAAC,CAAC,qCAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,qCAAqB,CAAC,IAAI;qBACtG,CAAC,CAAC;gBACJ,CAAC;YACF,CAAC;YACD,OAAO,GAAG,CAAC;QACZ,CAAC;QACD,KAAK,CAAC,mBAAmB,CAAC,QAAsB;YAC/C,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,KAAK,GAAG,iBAAiB,CAAC,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACtE,IAAI,KAAK,EAAE,CAAC;gBACX,MAAM,MAAM,GAAwB,EAAE,CAAC;gBACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACrC,MAAM,cAAc,GAAG,CAAC,IAA0B,EAAE,cAAuB,EAAE,EAAE;oBAC9E,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBACxD,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC9C,MAAM,MAAM,GAAsB;4BACjC,IAAI,EAAE,IAAI,CAAC,IAAI;4BACf,IAAI,EAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC;4BAClC,QAAQ,EAAE;gCACT,GAAG,EAAE,QAAQ,CAAC,GAAG;gCACjB,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;6BAC9C;4BACD,aAAa,EAAE,cAAc;yBAC7B,CAAC;wBACF,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;wBACrB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBACpB,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC;oBAC5B,CAAC;oBAED,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACnD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;4BACrC,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;wBACvC,CAAC;oBACF,CAAC;gBAEF,CAAC,CAAC;gBAEF,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC5C,OAAO,MAAM,CAAC;YACf,CAAC;YACD,OAAO,EAAE,CAAC;QACX,CAAC;QACD,KAAK,CAAC,cAAc,CAAC,QAAsB,EAAE,QAAkB;YAC9D,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,iBAAiB,CAAC,uBAAuB,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5G,IAAI,UAAU,EAAE,CAAC;gBAChB,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACpE,OAAO;wBACN,GAAG,EAAE,QAAQ,CAAC,GAAG;wBACjB,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC;qBAC3C,CAAC;gBACH,CAAC,CAAC,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC;QACb,CAAC;QACD,KAAK,CAAC,cAAc,CAAC,QAAsB,EAAE,QAAkB;YAC9D,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,iBAAiB,CAAC,uBAAuB,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5G,IAAI,UAAU,EAAE,CAAC;gBAChB,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;oBACpE,OAAO;wBACN,GAAG,EAAE,QAAQ,CAAC,GAAG;wBACjB,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC;qBAC3C,CAAC;gBACH,CAAC,CAAC,CAAC;YACJ,CAAC;YACD,OAAO,EAAE,CAAC;QACX,CAAC;QACD,KAAK,CAAC,iBAAiB,CAAC,QAAsB,EAAE,QAAkB;YACjE,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,SAAS,qBAAqB,CAAC,cAAiC;gBAC/D,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,qBAAqB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAChG,OAAO,8BAAc,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;YACzF,CAAC;YACD,MAAM,KAAK,GAAG,iBAAiB,CAAC,sBAAsB,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;YACtG,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACrC,CAAC;QACD,KAAK,CAAC,MAAM,CAAC,QAAsB,EAAE,KAAY,EAAE,YAA+B,EAAE,WAAqB,cAAc;YACtH,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YACzF,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAEpE,MAAM,iBAAiB,GAAG,QAAQ,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC;YAExF,MAAM,kBAAkB,GAAG,oBAAoB,CAAC,QAAQ,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;YAC/E,MAAM,cAAc,GAAG,cAAc,CAAC,YAAY,EAAE,iBAAiB,EAAE,kBAAkB,GAAG,CAAC,CAAC,CAAC;YAC/F,MAAM,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACzC,IAAI,aAAa,GAAG,IAAI,CAAC;YACzB,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,KAAK,CAAC,IAAI,IAAA,0BAAgB,EAAC,UAAU,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC;gBAC3B,aAAa,GAAG,qBAAK,CAAC,MAAM,CAAC,wBAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7E,CAAC;YACD,MAAM,KAAK,GAAG,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;YACvG,IAAI,KAAK,EAAE,CAAC;gBACX,MAAM,MAAM,GAAG,EAAE,CAAC;gBAClB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;wBAC3E,MAAM,CAAC,IAAI,CAAC;4BACX,KAAK,EAAE,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC;4BAC1C,OAAO,EAAE,IAAI,CAAC,OAAO;yBACrB,CAAC,CAAC;oBACJ,CAAC;gBACF,CAAC;gBACD,IAAI,aAAa,EAAE,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC;wBACX,KAAK,EAAE,aAAa;wBACpB,OAAO,EAAE,cAAc,CAAC,kBAAkB,EAAE,YAAY,CAAC;qBACzD,CAAC,CAAC;gBACJ,CAAC;gBACD,OAAO,MAAM,CAAC;YACf,CAAC;YACD,OAAO,EAAE,CAAC;QACX,CAAC;QACD,KAAK,CAAC,gBAAgB,CAAC,QAAsB;YAC5C,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,MAAM,KAAK,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAClE,MAAM,MAAM,GAAmB,EAAE,CAAC;YAClC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC1B,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACrD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAClC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC9B,IAAI,SAAS,GAAG,OAAO,EAAE,CAAC;oBACzB,MAAM,YAAY,GAAiB,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;oBAC1D,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;oBACzF,IAAI,KAAK,EAAE,CAAC;wBACX,YAAY,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,gCAAgB,CAAC,OAAO,CAAC;oBACnF,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,CAAC;YACF,CAAC;YACD,OAAO,MAAM,CAAC;QACf,CAAC;QACD,iBAAiB,CAAC,QAAsB;YACvC,WAAW,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;QACD,KAAK,CAAC,iBAAiB,CAAC,QAAsB;YAC7C,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YACpE,OAAO,CAAC,GAAG,IAAA,4CAAiB,EAAC,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9E,CAAC;QACD,sBAAsB;YACrB,OAAO,IAAA,iDAAsB,GAAE,CAAC;QACjC,CAAC;QACD,OAAO;YACN,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,WAAW,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;KACD,CAAC;AACH,CAAC;AAKD,SAAS,YAAY,CAAC,QAAsB,EAAE,IAA+D;IAC5G,IAAI,OAAO,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;QACvC,MAAM,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACnC,OAAO,qBAAK,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC/B,CAAC;IACD,MAAM,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACtD,MAAM,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;IACzE,OAAO,qBAAK,CAAC,MAAM,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,WAAW,CAAC,IAAY;IAChC,QAAQ,IAAI,EAAE,CAAC;QACd,+CAAwB;QACxB;YACC,OAAO,kCAAkB,CAAC,OAAO,CAAC;QAEnC,8BAAgB;QAChB,0BAAc;QACd,+BAAmB;QACnB,0CAAwB;QACxB,8BAAgB;QAChB;YACC,OAAO,kCAAkB,CAAC,QAAQ,CAAC;QAEpC,0CAAyB;QACzB,2CAA4B;QAC5B;YACC,OAAO,kCAAkB,CAAC,KAAK,CAAC;QAEjC,oCAAmB;QACnB;YACC,OAAO,kCAAkB,CAAC,QAAQ,CAAC;QAEpC,gCAAiB;QACjB,+CAA6B;QAC7B,qCAAwB;QACxB;YACC,OAAO,kCAAkB,CAAC,MAAM,CAAC;QAElC;YACC,OAAO,kCAAkB,CAAC,IAAI,CAAC;QAEhC;YACC,OAAO,kCAAkB,CAAC,UAAU,CAAC;QAEtC,gCAAiB;QACjB;YACC,OAAO,kCAAkB,CAAC,MAAM,CAAC;QAElC,8BAAgB;QAChB;YACC,OAAO,kCAAkB,CAAC,KAAK,CAAC;QAEjC;YACC,OAAO,kCAAkB,CAAC,SAAS,CAAC;QAErC;YACC,OAAO,kCAAkB,CAAC,IAAI,CAAC;QAEhC;YACC,OAAO,kCAAkB,CAAC,IAAI,CAAC;QAEhC;YACC,OAAO,kCAAkB,CAAC,MAAM,CAAC;QAElC;YACC,OAAO,kCAAkB,CAAC,QAAQ,CAAC;QAEpC;YACC,OAAO,kCAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;AACF,CAAC;AAkCD,SAAS,iBAAiB,CAAC,IAAY;IACtC,QAAQ,IAAI,EAAE,CAAC;QACd,+BAAgB,CAAC,CAAC,OAAO,0BAAU,CAAC,MAAM,CAAC;QAC3C,6BAAe,CAAC,CAAC,OAAO,0BAAU,CAAC,KAAK,CAAC;QACzC,2BAAc,CAAC,CAAC,OAAO,0BAAU,CAAC,IAAI,CAAC;QACvC,wCAAoB,CAAC,CAAC,OAAO,0BAAU,CAAC,UAAU,CAAC;QACnD,qCAAmB,CAAC,CAAC,OAAO,0BAAU,CAAC,SAAS,CAAC;QACjD,sCAAwB,CAAC,CAAC,OAAO,0BAAU,CAAC,MAAM,CAAC;QACnD,oCAAuB,CAAC,CAAC,OAAO,0BAAU,CAAC,MAAM,CAAC;QAClD,+BAAgB,CAAC,CAAC,OAAO,0BAAU,CAAC,MAAM,CAAC;QAC3C,yCAAwB,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QACrD,0CAA2B,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QACxD,0CAA2B,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QACxD,8BAAkB,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QAC/C,yBAAa,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QAC1C,6BAAe,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QAC5C,yCAAuB,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QACpD,6BAAe,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QAC5C,mCAAkB,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QAC/C,8CAAuB,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;QACpD,8CAA4B,CAAC,CAAC,OAAO,0BAAU,CAAC,WAAW,CAAC;QAC5D,uDAAmC,CAAC,CAAC,OAAO,0BAAU,CAAC,WAAW,CAAC;QACnE,8CAAuB,CAAC,CAAC,OAAO,0BAAU,CAAC,aAAa,CAAC;QACzD,+BAAgB,CAAC,CAAC,OAAO,0BAAU,CAAC,MAAM,CAAC;QAC3C,OAAO,CAAC,CAAC,OAAO,0BAAU,CAAC,QAAQ,CAAC;IACrC,CAAC;AACF,CAAC;AAED,SAAS,cAAc,CAAC,OAA0B,EAAE,cAAmB,EAAE,kBAA0B;IAClG,OAAO;QACN,mBAAmB,EAAE,OAAO,CAAC,YAAY;QACzC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,UAAU,EAAE,OAAO,CAAC,OAAO;QAC3B,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK;QACjC,gBAAgB,EAAE,IAAI;QACtB,cAAc,EAAE,OAAO,CAAC,OAAO,GAAG,kBAAkB;QACpD,8BAA8B,EAAE,OAAO,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,8BAA8B,CAAC;QACzG,2BAA2B,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,2BAA2B,CAAC;QAClG,wCAAwC,EAAE,OAAO,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,wCAAwC,CAAC;QAC7H,wCAAwC,EAAE,OAAO,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,wCAAwC,CAAC;QAC7H,+CAA+C,EAAE,OAAO,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,+CAA+C,CAAC;QAC3I,oDAAoD,EAAE,OAAO,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,oDAAoD,CAAC;QACrJ,oCAAoC,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,oCAAoC,CAAC;QACpH,0DAA0D,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,0DAA0D,CAAC;QAChK,uDAAuD,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,uDAAuD,CAAC;QAC1J,qDAAqD,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,qDAAqD,CAAC;QACtJ,kDAAkD,EAAE,OAAO,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,kDAAkD,CAAC;QACjJ,2DAA2D,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,2DAA2D,CAAC;QAClK,0DAA0D,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,0DAA0D,CAAC;QAChK,6BAA6B,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,6BAA6B,CAAC;QACtG,uCAAuC,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,mCAAmC,CAAC;QACtH,mCAAmC,EAAE,OAAO,CAAC,cAAc,IAAI,cAAc,CAAC,uCAAuC,CAAC;QACtH,UAAU,EAAE,cAAc,EAAE,UAAU;KACtC,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAsB,EAAE,KAAY,EAAE,OAA0B;IAC7F,MAAM,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,wBAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1E,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;IAEnC,IAAI,CAAC,GAAG,SAAS,CAAC;IAClB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAC3B,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAC7B,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;YAChB,MAAM,EAAE,CAAC;QACV,CAAC;aAAM,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;YACxB,MAAM,IAAI,OAAO,CAAC;QACnB,CAAC;aAAM,CAAC;YACP,MAAM;QACP,CAAC;QACD,CAAC,EAAE,CAAC;IACL,CAAC;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,cAAc,CAAC,KAAa,EAAE,OAA0B;IAChE,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;QAC1B,OAAO,IAAA,gBAAM,EAAC,GAAG,EAAE,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;SAAM,CAAC;QACP,OAAO,IAAA,gBAAM,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5B,CAAC;AACF,CAAC", "file": "javascriptMode.js", "sourceRoot": "../../src/"}