{"version": 3, "sources": ["test/unit/textRendering.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,iBAAe;AACf,mCAA6B;AAC7B,6EAAwJ;AAGxJ,MAAM,cAAc,GAAiC;IACpD,UAAU,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,YAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACpC,CAAC;AAEF,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAClC,IAAI,CAAC,yCAAyC,EAAE,GAAG,EAAE;QACpD,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,OAAO;aACb;SACD,EAAE,cAAc,CAAC,EAClB,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACzC,MAAM,CAAC,WAAW,CACjB,IAAA,uCAAuB;QACtB,oHAAoH;QACpH,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,4BAA4B,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,+DAA+D,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EACtZ,EAAE,EACF,cAAc,EAAE,SAAS,CACzB,CAAC,KAAK,EACP,oGAAoG,CAAC,CAAC;IACxG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACnD,MAAM,CAAC,WAAW,CACjB,IAAA,uCAAuB;QACtB,+FAA+F;QAC/F,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,oCAAoC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,kCAAkC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EACjY,EAAE,EACF,cAAc,EAAE,SAAS,CACzB,CAAC,KAAK,EACP,iFAAiF,CAAC,CAAC;IACrF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAC7D,MAAM,CAAC,WAAW,CACjB,IAAA,uCAAuB;QACtB,0FAA0F;QAC1F,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,4BAA4B,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,4BAA4B,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAC5X,EAAE,EACF,cAAc,EAAE,SAAS,CACzB,CAAC,KAAK,EACP,+FAA+F,CAAC,CAAC;IACnG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACtD,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,IAAI,EAAE,OAAO;gBACb,8FAA8F;gBAC9F,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,eAAe,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,oCAAoC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,kCAAkC,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;aACnd;SACD,EAAE,cAAc,CAAC,EAClB,gGAAgG,CAAC,CAAC;IACpG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gEAAgE,EAAE,GAAG,EAAE;QAC3E,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,uCAAuC;aAC7C;SACD,EAAE,cAAc,CAAC,EAClB,oDAAoD,CAAC,CAAC;IACxD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uCAAuC,EAAE,GAAG,EAAE;QAClD,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;aACf;SACD,EAAE,cAAc,CAAC,EAClB,oCAAoC,CACpC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0EAA0E,EAAE,GAAG,EAAE;QACrF,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,6BAA6B;aACnC;SACD,EAAE,cAAc,CAAC,EAClB,2CAA2C,CAC3C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mEAAmE,EAAE,GAAG,EAAE;QAC9E,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,sCAAsC;aAC5C;SACD,EAAE,cAAc,CAAC,EAClB,8CAA8C,CAC9C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uFAAuF,EAAE,GAAG,EAAE;QAClG,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,gDAAgD;aACtD;SACD,EAAE,cAAc,CAAC,EAClB,2CAA2C,CAC3C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oDAAoD,EAAE,GAAG,EAAE;QAC/D,MAAM,CAAC,WAAW,CACjB,IAAA,8BAAc,EAAC;YACd;gBACC,MAAM,EAAE,SAAS;gBACjB,MAAM,EAAE;oBACP;wBACC,MAAM,EAAE,QAAQ;wBAChB,MAAM,EAAE,MAAM;qBACd;oBACD;wBACC,MAAM,EAAE,SAAS;wBACjB,MAAM,EAAE,MAAM;qBACd;oBACD;wBACC,MAAM,EAAE,KAAK;wBACb,MAAM,EAAE,UAAU;qBAClB;oBACD;wBACC,MAAM,EAAE,GAAG;wBACX,MAAM,EAAE,MAAM;qBACd;iBACD;aACD;SACD,EAAE,cAAc,CAAC,EAClB,8CAA8C,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6CAA6C,EAAE,GAAG,EAAE;QACxD,MAAM,CAAC,WAAW,CACjB,IAAA,oCAAoB,EAAC;YACpB,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE;YAChC,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;YACzC;gBACC,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE;oBACT,MAAM,EAAE,eAAe;oBACvB,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;oBACnC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;iBAClC;aACoB;YACtB,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE;YAC/B,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE;SAChC,EAAE,cAAc,CAAC,EAClB,8MAA8M,CAAC,CAAC;IAClN,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sCAAsC,EAAE,GAAG,EAAE;QACjD,MAAM,CAAC,WAAW,CACjB,IAAA,oCAAoB,EAAC;YACpB,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE;YAChC,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE;YACzC;gBACC,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,UAAU;gBAClB,QAAQ,EAAE;oBACT,MAAM,EAAE,eAAe;oBACvB,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;oBACnC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;iBAClC;aACoB;YACtB,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE;YACvC,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE;YAC/B,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE;SAChC,EAAE,cAAc,CAAC,EAClB,gNAAgN,CAAC,CAAC;IACpN,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "textRendering.test.js", "sourceRoot": "../../../src/"}