{"version": 3, "sources": ["test/completions.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA,4CAwBC;AAID,8CA0BC;AA1ED;;;gGAGgG;AAChG,iBAAe;AACf,+CAAiC;AACjC,2CAA6B;AAC7B,2CAAiC;AACjC,0DAA2J;AAC3J,2CAA+C;AAC/C,8DAA8D;AAU9D,SAAgB,gBAAgB,CAAC,WAA2B,EAAE,QAAyB,EAAE,QAAsB;IAC9G,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QACrD,OAAO,UAAU,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC;IAC5C,CAAC,CAAC,CAAC;IACH,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;QAC3B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,iCAAiC,CAAC,CAAC;QAC1F,OAAO;IACR,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,uCAAuC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAChJ,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;QAC5B,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,aAAa,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnB,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IACD,IAAI,QAAQ,CAAC,UAAU,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;QAC3C,MAAM,IAAI,GAAG,wBAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,wBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC7H,MAAM,CAAC,WAAW,CAAC,4BAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;IACpF,CAAC;IACD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;QACtB,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;IACzD,CAAC;AACF,CAAC;AAED,MAAM,OAAO,GAAG,uBAAuB,CAAC;AAEjC,KAAK,UAAU,iBAAiB,CAAC,KAAa,EAAE,QAAuD,EAAE,GAAG,GAAG,OAAO,EAAE,gBAAoC;IAClK,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAClC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE3D,MAAM,SAAS,GAAG;QACjB,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,gBAAgB,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;KACtF,CAAC;IAEF,MAAM,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC5D,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAC7C,MAAM,OAAO,GAAG,IAAA,oCAAkB,EAAC,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC;IAE3D,MAAM,aAAa,GAAG,IAAA,gCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,kCAAkB,CAAC,MAAM,EAAE,IAAA,sBAAa,GAAE,CAAC,CAAC;IAC/H,MAAM,IAAI,GAAG,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAE,CAAC;IAElE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAEjE,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;QACpB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvD,CAAC;IACD,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;QACpB,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnC,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC;IACF,CAAC;AACF,CAAC;AAED,KAAK,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC7B,IAAI,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;QAC9C,MAAM,iBAAiB,CAAC,wCAAwC,EAAE;YACjE,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,+CAA+C,EAAE;aAClF;SACD,CAAC,CAAC;QACH,MAAM,iBAAiB,CAAC,mCAAmC,EAAE;YAC5D,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,yCAAyC,EAAE;aAC3E;SACD,CAAC,CAAC;QACH,MAAM,iBAAiB,CAAC,uEAAuE,EAAE;YAChG,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,uEAAuE,EAAE;aACnG;SACD,EAAE,wBAAwB,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAClC,MAAM,qBAAqB,GAAG;QAC7B,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE,8BAA8B;KACvC,CAAC;IAEF,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,uCAAuC,CAAC,CAAC;IACrF,MAAM,gBAAgB,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,gBAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;IACpF,MAAM,YAAY,GAAG,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAClF,MAAM,YAAY,GAAG,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAExF,IAAI,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;QAC7D,MAAM,iBAAiB,CAAC,oBAAoB,EAAE;YAC7C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,kCAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,yBAAyB,EAAE,OAAO,EAAE,qBAAqB,EAAE;gBAC3H,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,kCAAkB,CAAC,IAAI,EAAE,UAAU,EAAE,6BAA6B,EAAE;gBACjG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,kCAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,uBAAuB,EAAE,OAAO,EAAE,qBAAqB,EAAE;aACvH;SACD,EAAE,YAAY,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACxC,MAAM,iBAAiB,CAAC,oBAAoB,EAAE;YAC7C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,kCAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,yBAAyB,EAAE,OAAO,EAAE,qBAAqB,EAAE;gBAC3H,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,EAAE,kCAAkB,CAAC,IAAI,EAAE,UAAU,EAAE,6BAA6B,EAAE;gBACjG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,kCAAkB,CAAC,MAAM,EAAE,UAAU,EAAE,uBAAuB,EAAE,OAAO,EAAE,qBAAqB,EAAE;aACvH;SACD,EAAE,YAAY,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QACjD,MAAM,iBAAiB,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/D,MAAM,iBAAiB,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QACjE,MAAM,iBAAiB,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QAClE,MAAM,iBAAiB,CAAC,wBAAwB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QACjE,MAAM,iBAAiB,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QAClE,MAAM,iBAAiB,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QACnE,MAAM,iBAAiB,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QAChC,MAAM,iBAAiB,CAAC,qBAAqB,EAAE;YAC9C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,0BAA0B,EAAE;gBAC3D,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,8BAA8B,EAAE;gBACnE,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,wBAAwB,EAAE;aACvD;SACD,EAAE,YAAY,CAAC,CAAC;QAEjB,MAAM,iBAAiB,CAAC,yBAAyB,EAAE;YAClD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,kCAAkC,EAAE;gBACvE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,+BAA+B,EAAE;aACjE;SACD,EAAE,YAAY,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QAChC,MAAM,iBAAiB,CAAC,mBAAmB,EAAE;YAC5C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,wBAAwB,EAAE;gBACzD,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,4BAA4B,EAAE;gBACjE,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,sBAAsB,EAAE;aACrD;SACD,EAAE,YAAY,CAAC,CAAC;QAEjB,MAAM,iBAAiB,CAAC,uBAAuB,EAAE;YAChD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,gCAAgC,EAAE;gBACrE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,6BAA6B,EAAE;aAC/D;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kBAAkB,EAAE,KAAK,IAAI,EAAE;QACnC,uBAAuB;QACvB,MAAM,iBAAiB,CAAC,kBAAkB,EAAE;YAC3C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,uBAAuB,EAAE;gBACxD,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,2BAA2B,EAAE;gBAChE,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE;aACpD;SACD,EAAE,YAAY,CAAC,CAAC;QACjB,uBAAuB;QACvB,MAAM,iBAAiB,CAAC,kBAAkB,EAAE;YAC3C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,0BAA0B,EAAE;gBAC9D,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,2BAA2B,EAAE;gBAChE,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,uBAAuB,EAAE;aACxD;SACD,EAAE,YAAY,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QAClC,MAAM,iBAAiB,CAAC,wBAAwB,EAAE;YACjD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,gCAAgC,EAAE;gBACrE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,6BAA6B,EAAE;aAC/D;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,0BAA0B,EAAE;YACnD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,kCAAkC,EAAE;gBACvE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,+BAA+B,EAAE;aACjE;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QAC1C,uBAAuB;QACvB,MAAM,iBAAiB,CAAC,mBAAmB,EAAE;YAC5C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,uBAAuB,EAAE;gBACxD,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,2BAA2B,EAAE;gBAChE,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE;aACpD;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,sBAAsB,EAAE;YAC/C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,+BAA+B,EAAE;gBACpE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,4BAA4B,EAAE;aAC9D;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,uBAAuB,EAAE;YAChD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,+BAA+B,EAAE;gBACpE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,4BAA4B,EAAE;aAC9D;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,uBAAuB;QACvB,MAAM,iBAAiB,CAAC,mBAAmB,EAAE;YAC5C,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,0BAA0B,EAAE;gBAC9D,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,2BAA2B,EAAE;gBAChE,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,uBAAuB,EAAE;aACxD;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,wBAAwB,EAAE;YACjD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,+BAA+B,EAAE;aAClE;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,yBAAyB,EAAE;YAClD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,+BAA+B,EAAE;aAClE;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACvD,uBAAuB;QACvB,MAAM,iBAAiB,CAAC,gCAAgC,EAAE;YACzD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,+BAA+B,EAAE;gBACpE,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,4BAA4B,EAAE;aAC9D;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,gCAAgC,EAAE;YACzD,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,uBAAuB,EAAE;gBACxD,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,2BAA2B,EAAE;gBAChE,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,qBAAqB,EAAE;aACpD;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,uBAAuB;QACvB,MAAM,iBAAiB,CAAC,kCAAkC,EAAE;YAC3D,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,+BAA+B,EAAE;aAClE;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,kCAAkC,EAAE;YAC3D,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,0BAA0B,EAAE;gBAC9D,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,2BAA2B,EAAE;gBAChE,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,uBAAuB,EAAE;aACxD;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAGH,IAAI,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;QAC5E,MAAM,iBAAiB,CAAC,oCAAoC,EAAE;YAC7D,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,yCAAyC,EAAE;gBAC1E,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,6CAA6C,EAAE;gBAClF,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,uCAAuC,EAAE;aACtE;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAErC,MAAM,iBAAiB,CAAC,oCAAoC,EAAE;YAC7D,KAAK,EAAE;gBACN,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,oCAAoC,EAAE;gBACrE,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,wCAAwC,EAAE;gBAC7E,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,kCAAkC,EAAE;aACjE;SACD,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0DAA0D,EAAE,KAAK,IAAI,EAAE;QAC3E,MAAM,iBAAiB,CAAC,mBAAmB,EAAE;YAC5C,KAAK,EAAE,CAAC;SACR,EAAE,YAAY,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QAChC;;;;;;UAME;IACH,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "completions.test.js", "sourceRoot": "../../src/"}