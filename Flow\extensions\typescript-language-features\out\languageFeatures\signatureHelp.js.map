{"version": 3, "sources": ["languageFeatures/signatureHelp.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwHhG,4BAaC;AAnID,+CAAiC;AAGjC,kEAAoD;AACpD,4DAAkF;AAClF,wEAA8F;AAC9F,gEAAkD;AAElD,MAAM,+BAA+B;IAKpC,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;IAC9C,CAAC;IAEE,KAAK,CAAC,oBAAoB,CAChC,QAA6B,EAC7B,QAAyB,EACzB,KAA+B,EAC/B,OAAoC;QAEpC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAmC;YAC5C,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,EAAE,QAAQ,CAAC;YACxE,aAAa,EAAE,iBAAiB,CAAC,OAAO,CAAC;SACzC,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QAC5G,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QAC1C,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAChG,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACnF,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAEvD,OAAO,MAAM,CAAC;IACf,CAAC;IAEO,kBAAkB,CAAC,OAAoC,EAAE,IAA8B,EAAE,UAAkD;QAClJ,yEAAyE;QACzE,MAAM,yBAAyB,GAAG,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC,OAAO,CAAC,mBAAmB,CAAC,eAAe,CAAC,CAAC;QACvH,IAAI,yBAAyB,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtD,MAAM,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACtG,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO,aAAa,CAAC;YACtB,CAAC;QACF,CAAC;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAC/B,CAAC;IAEO,kBAAkB,CAAC,IAA8B;QACxD,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3D,IAAI,eAAe,EAAE,UAAU,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC3B,CAAC;IAEO,gBAAgB,CAAC,IAA6B,EAAE,OAAmB;QAC1E,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAChD,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,EACpE,SAAS,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;QAEzH,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;QACvC,MAAM,cAAc,GAAG,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/F,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;YACjD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,KAAK,GAAG,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAElF,SAAS,CAAC,UAAU,CAAC,IAAI,CACxB,IAAI,MAAM,CAAC,oBAAoB,CAC9B,CAAC,SAAS,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,EACrC,SAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,aAAa,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;YAEzF,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC;YAC1B,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC;YAEzB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,SAAS,CAAC,KAAK,IAAI,cAAc,CAAC;gBAClC,SAAS,IAAI,cAAc,CAAC,MAAM,CAAC;YACpC,CAAC;QACF,CAAC;QAED,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACxF,OAAO,SAAS,CAAC;IAClB,CAAC;;AApFsB,iDAAiB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACpC,mDAAmB,GAAG,CAAC,GAAG,CAAC,CAAC;AAsFpD,SAAS,iBAAiB,CAAC,OAAoC;IAC9D,QAAQ,OAAO,CAAC,WAAW,EAAE,CAAC;QAC7B,KAAK,MAAM,CAAC,wBAAwB,CAAC,gBAAgB;YACpD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACzB,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,gBAAgB,EAAE,OAAO,CAAC,gBAAuB,EAAE,CAAC;gBACjF,CAAC;qBAAM,CAAC;oBACP,OAAO,EAAE,IAAI,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,OAAO,CAAC,gBAAuB,EAAE,CAAC;gBACtF,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;YAC5B,CAAC;QAEF,KAAK,MAAM,CAAC,wBAAwB,CAAC,aAAa;YACjD,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;QAE1E,KAAK,MAAM,CAAC,wBAAwB,CAAC,MAAM,CAAC;QAC5C;YACC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;IAC7B,CAAC;AACF,CAAC;AACD,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,MAAgC;IAEhC,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,cAAc,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACzF,EAAE,GAAG,EAAE;QACP,OAAO,MAAM,CAAC,SAAS,CAAC,6BAA6B,CAAC,QAAQ,CAAC,MAAM,EACpE,IAAI,+BAA+B,CAAC,MAAM,CAAC,EAAE;YAC7C,iBAAiB,EAAE,+BAA+B,CAAC,iBAAiB;YACpE,mBAAmB,EAAE,+BAA+B,CAAC,mBAAmB;SACxE,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "signatureHelp.js", "sourceRoot": "../../src/"}