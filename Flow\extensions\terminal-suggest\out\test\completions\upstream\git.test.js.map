{"version": 3, "sources": ["test/completions/upstream/git.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;AAEhG,iBAAe;AACf,2CAA2D;AAC3D,4EAAwD;AAExD,ynBAAynB;AACznB,0rBAA0rB;AAC1rB,orBAAorB;AACprB,uZAAuZ;AACvZ,MAAM,mBAAmB,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAG,aAAe,CAAC,WAAW,EAAE,CAAC,CAAC;AAE7E,QAAA,gBAAgB,GAAe;IAC3C,IAAI,EAAE,KAAK;IACX,eAAe,EAAE,aAAO;IACxB,iBAAiB,EAAE,KAAK;IACxB,SAAS,EAAE;QACV,cAAc;QACd,EAAE,KAAK,EAAE,GAAG,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAEnG,qBAAqB;QACrB,EAAE,KAAK,EAAE,IAAI,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACpG,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QACrG,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAEtG,uGAAuG;QACvG,gBAAgB;QAChB,iEAAiE;QAEjE,kBAAkB;QAClB,8HAA8H;QAC9H,iEAAiE;QACjE,8DAA8D;KAC9D;CACD,CAAC", "file": "git.test.js", "sourceRoot": "../../../../src/"}