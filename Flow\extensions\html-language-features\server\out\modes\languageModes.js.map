{"version": 3, "sources": ["modes/languageModes.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AA4DhG,oDAEC;AA6CD,4CA6EC;AAtLD,2EAAmE;AACnE,6EAEqC;AAUrC,8DAAkF;AAClF,uCAAuC;AACvC,uDAA4E;AAC5E,yCAAyC;AACzC,qDAAqD;AAGrD,+DAO+B;AAN9B,wHAAA,eAAe,OAAA;AAAE,uHAAA,cAAc,OAAA;AAAE,uHAAA,cAAc,OAAA;AAAE,2HAAA,kBAAkB,OAAA;AAAc,mHAAA,UAAU,OAAA;AAAE,0HAAA,iBAAiB,OAAA;AAAE,8HAAA,qBAAqB,OAAA;AACrI,qHAAA,YAAY,OAAA;AAAE,qHAAA,YAAY,OAAA;AAAE,yHAAA,gBAAgB,OAAA;AAAE,0HAAA,iBAAiB,OAAA;AAC/D,8GAAA,KAAK,OAAA;AAAE,iHAAA,QAAQ,OAAA;AAAE,iHAAA,QAAQ,OAAA;AAAE,8GAAA,KAAK,OAAA;AAAiB,0HAAA,iBAAiB,OAAA;AAAE,mHAAA,UAAU,OAAA;AAAE,iHAAA,QAAQ,OAAA;AACxF,8GAAA,KAAK,OAAA;AAAE,yHAAA,gBAAgB,OAAA;AAAE,0HAAA,iBAAiB,OAAA;AAAE,sHAAA,aAAa,OAAA;AACzD,6HAAA,oBAAoB,OAAA;AAAE,6HAAA,oBAAoB,OAAA;AAAE,2HAAA,kBAAkB,OAAA;AAC9D,uHAAA,cAAc,OAAA;AAAE,+HAAA,sBAAsB,OAAA;AAGvC,2EAAqJ;AAA5I,iIAAA,kBAAkB,OAAA;AAA2E,wHAAA,SAAS,OAAA;AAE/G,yFAAkE;AAAzD,kIAAA,YAAY,OAAA;AA2BrB,SAAgB,oBAAoB,CAAC,KAAU;IAC9C,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ,CAAC;AAC3H,CAAC;AA6CD,SAAgB,gBAAgB,CAAC,kBAAqD,EAAE,SAAoB,EAAE,kBAAsC,EAAE,cAAkC;IACvL,MAAM,mBAAmB,GAAG,IAAA,gDAAsB,EAAC,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,EAAE,CAAC,CAAC;IAC/G,MAAM,kBAAkB,GAAG,IAAA,kDAAqB,EAAC,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,EAAE,CAAC,CAAC;IAE7G,MAAM,eAAe,GAAG,IAAA,0CAAqB,EAAsB,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAA,oCAAkB,EAAC,mBAAmB,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE1I,IAAI,WAAW,GAA8B,EAAE,CAAC;IAChD,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAElC,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAChC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAA,sBAAW,EAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;IAC5D,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC;QAC/B,KAAK,CAAC,KAAK,CAAC,GAAG,IAAA,oBAAU,EAAC,kBAAkB,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;IAC3E,CAAC;IACD,IAAI,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC;QACtC,KAAK,CAAC,YAAY,CAAC,GAAG,IAAA,kCAAiB,EAAC,eAAe,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;QAClF,KAAK,CAAC,YAAY,CAAC,GAAG,IAAA,kCAAiB,EAAC,eAAe,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;IACnF,CAAC;IACD,OAAO;QACN,KAAK,CAAC,mBAAmB,CAAC,aAAkC;YAC3D,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAC3D,CAAC;QACD,iBAAiB,CAAC,QAAsB,EAAE,QAAkB;YAC3D,MAAM,UAAU,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YACjF,IAAI,UAAU,EAAE,CAAC;gBAChB,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1B,CAAC;YACD,OAAO,SAAS,CAAC;QAClB,CAAC;QACD,eAAe,CAAC,QAAsB,EAAE,KAAY;YACnD,OAAO,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAqB,EAAE;gBAC1F,OAAO;oBACN,KAAK,EAAE,CAAC,CAAC,KAAK;oBACd,GAAG,EAAE,CAAC,CAAC,GAAG;oBACV,IAAI,EAAE,CAAC,CAAC,UAAU,IAAI,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC;oBACzC,cAAc,EAAE,CAAC,CAAC,cAAc;iBAChC,CAAC;YACH,CAAC,CAAC,CAAC;QACJ,CAAC;QACD,qBAAqB,CAAC,QAAsB;YAC3C,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,UAAU,IAAI,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,sBAAsB,EAAE,EAAE,CAAC;gBACjF,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC/B,IAAI,IAAI,EAAE,CAAC;oBACV,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACF,CAAC;YACD,OAAO,MAAM,CAAC;QACf,CAAC;QACD,WAAW;YACV,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,KAAK,MAAM,UAAU,IAAI,KAAK,EAAE,CAAC;gBAChC,MAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;gBAC/B,IAAI,IAAI,EAAE,CAAC;oBACV,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACnB,CAAC;YACF,CAAC;YACD,OAAO,MAAM,CAAC;QACf,CAAC;QACD,OAAO,CAAC,UAAkB;YACzB,OAAO,KAAK,CAAC,UAAU,CAAC,CAAC;QAC1B,CAAC;QACD,iBAAiB,CAAC,QAAsB;YACvC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC1D,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC1B,KAAK,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC;QACF,CAAC;QACD,OAAO;YACN,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;YACxC,WAAW,GAAG,EAAE,CAAC;YACjB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBAC1B,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;YACD,KAAK,GAAG,EAAE,CAAC;QACZ,CAAC;KACD,CAAC;AACH,CAAC", "file": "languageModes.js", "sourceRoot": "../../src/"}