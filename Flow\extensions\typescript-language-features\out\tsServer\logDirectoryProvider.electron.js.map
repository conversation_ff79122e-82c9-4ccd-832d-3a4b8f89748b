{"version": 3, "sources": ["tsServer/logDirectoryProvider.electron.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,uCAAyB;AACzB,2CAA6B;AAC7B,+CAAiC;AACjC,8CAA2C;AAG3C,MAAa,wBAAwB;IACpC,YACkB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;IAC9C,CAAC;IAEE,kBAAkB;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACjC,IAAI,IAAI,EAAE,CAAC;YACV,IAAI,CAAC;gBACJ,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1E,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,OAAO,SAAS,CAAC;YAClB,CAAC;QACF,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAGO,YAAY;QACnB,IAAI,CAAC;YACJ,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YAClC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1B,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAC7B,CAAC;QAAC,MAAM,CAAC;YACR,OAAO,SAAS,CAAC;QAClB,CAAC;IACF,CAAC;CACD;AA7BD,4DA6BC;AAXQ;IADP,iBAAO;4DAWP", "file": "logDirectoryProvider.electron.js", "sourceRoot": "../../src/"}