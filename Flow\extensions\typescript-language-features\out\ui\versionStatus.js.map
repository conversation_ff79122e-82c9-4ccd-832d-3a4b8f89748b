{"version": 3, "sources": ["ui/versionStatus.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,iFAAqF;AACrF,8DAAiE;AAGjE,8CAA8C;AAG9C,MAAa,aAAc,SAAQ,oBAAU;IAI5C,YACkB,OAAiC;QAElD,KAAK,EAAE,CAAC;QAFS,YAAO,GAAP,OAAO,CAA0B;QAIlD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,+BAAiB,CAAC,CAAC,CAAC;QAEtH,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC;QAE9D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7G,CAAC;IAEO,4BAA4B,CAAC,OAA0B;QAC9D,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,WAAW,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG;YAC1B,OAAO,EAAE,wDAA8B,CAAC,EAAE;YAC1C,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC;YACtC,OAAO,EAAE,OAAO,CAAC,IAAI;SACrB,CAAC;IACH,CAAC;CACD;AAzBD,sCAyBC", "file": "versionStatus.js", "sourceRoot": "../../src/"}