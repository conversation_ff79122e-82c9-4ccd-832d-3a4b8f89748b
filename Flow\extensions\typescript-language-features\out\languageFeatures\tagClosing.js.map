{"version": 3, "sources": ["languageFeatures/tagClosing.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8JhG,4BAQC;AApKD,+CAAiC;AAIjC,kEAAoD;AAEpD,8CAA8C;AAC9C,wEAAkF;AAElF,MAAM,UAAW,SAAQ,oBAAU;IAMlC,YACkB,MAAgC;QAEjD,KAAK,EAAE,CAAC;QAFS,WAAM,GAAN,MAAM,CAA0B;QAL1C,cAAS,GAAG,KAAK,CAAC;QAClB,aAAQ,GAA+B,SAAS,CAAC;QACjD,YAAO,GAA+C,SAAS,CAAC;QAMvE,MAAM,CAAC,SAAS,CAAC,uBAAuB,CACvC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAC5C,IAAI,EACJ,IAAI,CAAC,YAAY,CAAC,CAAC;IACrB,CAAC;IAEe,OAAO;QACtB,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5B,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QAC1B,CAAC;IACF,CAAC;IAEO,uBAAuB,CAC9B,EAAE,QAAQ,EAAE,cAAc,EAAE,MAAM,EAAkC;QAEpE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,KAAK,MAAM,CAAC,wBAAwB,CAAC,IAAI,IAAI,MAAM,KAAK,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC;YACvI,OAAO;QACR,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;QAChE,IAAI,QAAQ,KAAK,cAAc,EAAE,CAAC;YACjC,OAAO;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO;QACR,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,WAAW,EAAE,CAAC;YAC1C,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QAC1B,CAAC;QAED,MAAM,UAAU,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAClE,IAAI,UAAU,CAAC,WAAW,GAAG,CAAC,IAAI,aAAa,KAAK,GAAG,IAAI,aAAa,KAAK,GAAG,EAAE,CAAC;YAClF,OAAO;QACR,CAAC;QAED,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC;YAC1D,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACtH,CAAC,CAAC,EAAE,CAAC;QACN,IAAI,cAAc,KAAK,GAAG,EAAE,CAAC;YAC5B,OAAO;QACR,CAAC;QAED,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YACrC,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;YAE1B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,OAAO;YACR,CAAC;YAED,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,IAAI,CAAC;gBACtC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,cAAc,EAAE,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC9E,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAEtH,MAAM,IAAI,GAAmC,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACnH,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;YACpD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACtF,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACpD,OAAO;YACR,CAAC;YAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACpB,OAAO;YACR,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;YACpD,IAAI,CAAC,YAAY,EAAE,CAAC;gBACnB,OAAO;YACR,CAAC;YAED,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC;YAChC,MAAM,cAAc,GAAG,YAAY,CAAC,QAAQ,CAAC;YAC7C,IAAI,QAAQ,KAAK,cAAc,IAAI,cAAc,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;gBACvE,YAAY,CAAC,aAAa,CACzB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAC7B,IAAI,CAAC,qBAAqB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;YACtD,CAAC;QACF,CAAC,EAAE,GAAG,CAAC,CAAC;IACT,CAAC;IAEO,aAAa,CAAC,UAA+B;QACpD,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACjC,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACvC,OAAO,OAAO,CAAC;IAChB,CAAC;IAEO,qBAAqB,CAAC,MAAyB,EAAE,QAAyB;QACjF,MAAM,wBAAwB,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QACtE,OAAO,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7D,CAAC,CAAC,wBAAwB;YAC1B,CAAC,CAAC,QAAQ,CAAC;IACb,CAAC;CACD;AAED,SAAS,4BAA4B,CACpC,QAAiC,EACjC,QAA6B;IAE7B,OAAO,IAAI,iCAAS,CACnB,GAAG,EAAE;QACJ,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnE,OAAO,KAAK,CAAC;QACd,CAAC;QAED,OAAO,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACjG,CAAC,EACD,OAAO,CAAC,EAAE;QACT,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAC5B,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,OAAO,CAAC,EAClD,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,OAAO,CAAC,EAC/C,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,QAA6B,EAC7B,MAAgC;IAEhC,OAAO,IAAA,+CAAuB,EAAC;QAC9B,4BAA4B,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC;KACvD,EAAE,GAAG,EAAE,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;AAClC,CAAC", "file": "tagClosing.js", "sourceRoot": "../../src/"}