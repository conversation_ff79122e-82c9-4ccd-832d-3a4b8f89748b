{"version": 3, "sources": ["languageFeatures/jsDocCompletions.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGhG,8CAoBC;AAED,4BAUC;AAjID,+CAAiC;AAGjC,kEAAoD;AAMpD,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;AAEjE,MAAM,mBAAoB,SAAQ,MAAM,CAAC,cAAc;IACtD,YACiB,QAA6B,EAC7B,QAAyB;QAEzC,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAHhC,aAAQ,GAAR,QAAQ,CAAqB;QAC7B,aAAQ,GAAR,QAAQ,CAAiB;QAGzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACpE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QACjE,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpE,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5F,IAAI,CAAC,KAAK,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IACrD,CAAC;CACD;AAED,MAAM,uBAAuB;IAE5B,YACkB,MAAgC,EAChC,QAA6B,EAC7B,wBAAkD;QAFlD,WAAM,GAAN,MAAM,CAA0B;QAChC,aAAQ,GAAR,QAAQ,CAAqB;QAC7B,6BAAwB,GAAxB,wBAAwB,CAA0B;IAChE,CAAC;IAEE,KAAK,CAAC,sBAAsB,CAClC,QAA6B,EAC7B,QAAyB,EACzB,KAA+B;QAE/B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAAE,CAAC;YAClG,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uCAAuC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;YACvE,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;YAC7D,MAAM,IAAI,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEpF,MAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC/E,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC;QACH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEzD,wBAAwB;QACxB,8EAA8E;QAC9E,0EAA0E;QAC1E,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACxC,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;QAChC,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;IAEO,uCAAuC,CAC9C,QAA6B,EAC7B,QAAyB;QAEzB,qFAAqF;QACrF,uCAAuC;QACvC,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,OAAO,KAAK,CAAC;QACd,CAAC;QAED,wEAAwE;QACxE,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC9C,OAAO,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;CACD;AAED,SAAgB,iBAAiB,CAAC,QAAgB;IACjD,+BAA+B;IAC/B,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,wGAAwG;IACnJ,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;IAC3D,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;IACtE,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,yCAAyC,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;QAC7F,IAAI,GAAG,GAAG,WAAW,CAAC;QACtB,IAAI,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YAC1C,GAAG,IAAI,QAAQ,YAAY,EAAE,QAAQ,CAAC;QACvC,CAAC;aAAM,IAAI,IAAI,EAAE,CAAC;YACjB,GAAG,IAAI,IAAI,GAAG,GAAG,CAAC;QACnB,CAAC;QACD,GAAG,IAAI,IAAI,GAAG,OAAO,YAAY,EAAE,GAAG,CAAC;QACvC,OAAO,GAAG,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,sBAAsB,EAAE,iBAAiB,YAAY,EAAE,GAAG,CAAC,CAAC;IAExF,OAAO,IAAI,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC3C,CAAC;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,QAA6B,EAC7B,MAAgC,EAChC,wBAAkD;IAGlD,OAAO,MAAM,CAAC,SAAS,CAAC,8BAA8B,CAAC,QAAQ,CAAC,MAAM,EACrE,IAAI,uBAAuB,CAAC,MAAM,EAAE,QAAQ,EAAE,wBAAwB,CAAC,EACvE,GAAG,CAAC,CAAC;AACP,CAAC", "file": "jsDocCompletions.js", "sourceRoot": "../../src/"}