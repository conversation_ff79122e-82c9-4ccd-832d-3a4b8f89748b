{"version": 3, "sources": ["tsServer/nodeManager.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AAEjC,0CAA8C;AAC9C,8CAA8C;AAG9C,MAAM,0BAA0B,GAAG,6BAA6B,CAAC;AACjE,MAAM,gCAAgC,GAAG,mCAAmC,CAAC;AAI7E,MAAa,kBAAmB,SAAQ,oBAAU;IAGjD,YACS,aAA6C,EACpC,cAA8B;QAE/C,KAAK,EAAE,CAAC;QAHA,kBAAa,GAAb,aAAa,CAAgC;QACpC,mBAAc,GAAd,cAAc,CAAgB;QAqC/B,yBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC,CAAC;QACxE,wBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAlCrE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,IAAI,SAAS,CAAC;QACtE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YAChC,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;YAC1D,IAAI,gBAAgB,EAAE,CAAC;gBACtB,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;gBACpE,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;oBACpC,IAAA,oBAAY,EAAC,GAAG,EAAE;wBACjB,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBAClC,CAAC,CAAC,CAAC;gBACJ,CAAC;qBACI,IAAI,gBAAgB,EAAE,CAAC;oBAC3B,IAAI,CAAC,eAAe,GAAG,gBAAgB,CAAC;gBACzC,CAAC;YACF,CAAC;QACF,CAAC;aACI,CAAC;YACL,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,EAAE;gBACrE,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;gBAC1D,IAAI,gBAAgB,EAAE,CAAC;oBACtB,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;oBACpE,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;wBACpC,IAAA,oBAAY,EAAC,GAAG,EAAE;4BACjB,IAAI,CAAC,yBAAyB,EAAE,CAAC;wBAClC,CAAC,CAAC,CAAC;oBACJ,CAAC;yBACI,IAAI,gBAAgB,EAAE,CAAC;wBAC3B,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;oBAC5C,CAAC;gBACF,CAAC;YACF,CAAC,CAAC,CAAC,CAAC;QACL,CAAC;IACF,CAAC;IAKD,IAAW,cAAc;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAEM,KAAK,CAAC,mBAAmB,CAAC,iBAAiD;QACjF,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;QACvC,IAAI,gBAAgB,CAAC,cAAc,KAAK,iBAAiB,CAAC,cAAc;eACpE,gBAAgB,CAAC,aAAa,KAAK,iBAAiB,CAAC,aAAa,EAAE,CAAC;YACxE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChC,CAAC;IACF,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC9B,IAAI,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,IAAI,SAAS,CAAC;QAC7D,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QAC1D,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,IAAI,gBAAgB,EAAE,CAAC;YACpD,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;YACpE,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;gBACpC,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,IAAI,OAAO,CAAC;YAC1D,CAAC;iBACI,IAAI,gBAAgB,EAAE,CAAC;gBAC3B,OAAO,GAAG,gBAAgB,CAAC;YAC5B,CAAC;QACF,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAEO,KAAK,CAAC,sBAAsB;QACnC,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QAC1D,IAAI,gBAAgB,KAAK,IAAI,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,yGAAyG,CAAC,CAAC;QAC5H,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACrC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAEzC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,wGAAwG,EAAE,gBAAgB,CAAC,EAClM,KAAK,EACL,QAAQ,EACR,OAAO,CACP,CAAC;QAEF,IAAI,OAAO,GAAG,SAAS,CAAC;QACxB,QAAQ,MAAM,EAAE,CAAC;YAChB,KAAK,KAAK;gBACT,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBAC5D,OAAO,GAAG,gBAAgB,CAAC;gBAC3B,MAAM;YACP,KAAK,QAAQ;gBACZ,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;gBAC7D,MAAM;YACP,KAAK,OAAO;gBACX,MAAM,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;gBACjE,MAAM;QACR,CAAC;QACD,OAAO,OAAO,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,yBAAyB;QACtC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;QACpD,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC3B,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;IACF,CAAC;IAEO,mBAAmB,CAAC,aAAiC;QAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC;QACrC,IAAI,UAAU,KAAK,aAAa,EAAE,CAAC;YAClC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QAClC,CAAC;IACF,CAAC;IAEO,mBAAmB,CAAC,WAAmB;QAC9C,MAAM,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAA8B,gCAAgC,CAAC,CAAC;QACtH,IAAI,sBAAsB,KAAK,WAAW,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAwB,0BAA0B,CAAC,CAAC;QACnF,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAA0B,EAAE,WAAmB;QACrF,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,gCAAgC,EAAE,WAAW,CAAC,CAAC;QAChF,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;CACD;AApID,gDAoIC", "file": "nodeManager.js", "sourceRoot": "../../src/"}