{"version": 3, "sources": ["completions/upstream/python3.ts"], "names": [], "mappings": ";;AAAA,uDAAoD;AAEpD,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,SAAS;IACf,WAAW,EAAE,4BAA4B;IACzC,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE,EAAE;QACnD,MAAM,kCAAkC,GAAG,gCAAgC,CAAC;QAE5E,IACC,CACC,MAAM,mBAAmB,CAAC;YACzB,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,CAAC,IAAI,EAAE,kCAAkC,CAAC;SAChD,CAAC,CACF,CAAC,MAAM,KAAK,CAAC,EACb,CAAC;YACF,OAAO;gBACN,IAAI,EAAE,SAAS;gBACf,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC;aAC9D,CAAC;QACH,CAAC;IACF,CAAC;IACD,IAAI,EAAE;QACL,IAAI,EAAE,eAAe;QACrB,QAAQ,EAAE,IAAI;QACd,UAAU,EAAE,IAAA,qBAAS,EAAC;YACrB,UAAU,EAAE,CAAC,IAAI,CAAC;YAClB,mBAAmB,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;SACrC,CAAC;KACF;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,eAAe;YAC5B,WAAW,EACV,+JAA+J;YAChK,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,IAAI;aACf;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,eAAe;YAC5B,WAAW,EACV,sFAAsF;YACvF,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,IAAI;aACf;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;YAC5B,WAAW,EAAE,uDAAuD;SACpE;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,WAAW,EAAE,0CAA0C;SACvD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,mIAAmI;SACpI;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,gFAAgF;SACjF;QACD;YACC,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EACV,4FAA4F;YAC7F,IAAI,EAAE;gBACL,WAAW,EAAE;oBACZ,EAAE,IAAI,EAAE,SAAS,EAAE;oBACnB,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAClB,EAAE,IAAI,EAAE,OAAO,EAAE;iBACjB;aACD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,qFAAqF;SACtF;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,6FAA6F;SAC9F;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,4LAA4L;SAC7L;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,oKAAoK;SACrK;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,6EAA6E;SAC9E;QACD;YACC,IAAI,EAAE,KAAK;YACX,WAAW,EAAE,mCAAmC;SAChD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,2EAA2E;SAC5E;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,mKAAmK;SACpK;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,wDAAwD;SACrE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,wGAAwG;SACzG;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,qGAAqG;SACtG;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,4HAA4H;SAC7H;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,8FAA8F;YAC/F,IAAI,EAAE,EAAE;SACR;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,2HAA2H;SAC5H;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,sDAAsD;YACnE,IAAI,EAAE;gBACL,WAAW,EAAE;oBACZ,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,KAAK,EAAE;oBACf,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,qBAAqB,EAAE;iBAC/B;aACD;SACD;KACD;CACD,CAAC;AAEF,kBAAe,cAAc,CAAC", "file": "python3.js", "sourceRoot": "../../../src/"}