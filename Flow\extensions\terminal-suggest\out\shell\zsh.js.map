{"version": 3, "sources": ["shell/zsh.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhG,sCAKC;AAiDD,sDAkBC;AAhFD,+CAAiC;AAEjC,qCAAwD;AAExD,yDAAyE;AAEzE,MAAM,wBAAwB,GAA0G,UAAU,CAAC,sDAAmC,CAAC,CAAC;AAEjL,KAAK,UAAU,aAAa,CAAC,OAAsC,EAAE,gBAA8B;IACzG,OAAO;QACN,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC;QAC5B,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC;KAC/C,CAAC;AACH,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,OAAsC;IAC/D,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClF,OAAO,IAAA,yBAAgB,EAAC,KAAK,EAAE,IAAI,EAAE,wEAAwE,EAAE,OAAO,CAAC,CAAC;AACzH,CAAC;AAED,KAAK,UAAU,WAAW,CACzB,OAAsC,EACtC,gBAA8B;IAE9B,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAU,EAAC,+BAA+B,EAAE,OAAO,CAAC,CAAC;IACjF,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACnE,MAAM,QAAQ,GAAa,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACpE,MAAM,WAAW,GAA0B,EAAE,CAAC;IAC9C,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;QACnC,WAAW,CAAC,IAAI,CAAC;YAChB,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,oCAAoC;YAC5C,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM;SAC9C,CAAC,CAAC;IACJ,CAAC;IAGD,KAAK,MAAM,GAAG,IAAI,wBAAwB,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;QAC1D,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACJ,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;gBAC1C,WAAW,CAAC,IAAI,CAAC;oBAChB,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE;oBACvD,MAAM,EAAE,MAAM,EAAE,IAAI;oBACpB,aAAa,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,aAAa,CAAC;oBAC/D,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM;iBAC9C,CAAC,CAAC;YAEJ,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,gBAAgB;gBAChB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;gBAC3C,WAAW,CAAC,IAAI,CAAC;oBAChB,KAAK,EAAE,GAAG;oBACV,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM;iBAC9C,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;IACF,CAAC;IAED,OAAO,WAAW,CAAC;AACpB,CAAC;AAED,SAAgB,qBAAqB,CAAC,OAAe;IACpD,IAAI,CAAC,sDAAmC,EAAE,CAAC;QAC1C,OAAO,SAAS,CAAC;IAClB,CAAC;IACD,MAAM,MAAM,GAAG,wBAAwB,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;IACtD,IAAI,MAAM,EAAE,gBAAgB,EAAE,CAAC;QAC9B,OAAO;YACN,WAAW,EAAE,MAAM,CAAC,gBAAgB;YACpC,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,aAAa,EAAE,MAAM,CAAC,WAAW;SACjC,CAAC;IACH,CAAC;SAAM,CAAC;QACP,OAAO;YACN,WAAW,EAAE,MAAM,EAAE,WAAW;YAChC,IAAI,EAAE,MAAM,EAAE,IAAI;YAClB,aAAa,EAAE,MAAM,EAAE,WAAW;SAClC,CAAC;IACH,CAAC;AACF,CAAC;AAED,SAAS,UAAU,CAAC,KAAa;IAChC,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,OAAO,SAAS,CAAC;IAClB,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,GAAG,EAAwF,CAAC;IAC/G,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAClD,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACxB,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC", "file": "zsh.js", "sourceRoot": "../../src/"}