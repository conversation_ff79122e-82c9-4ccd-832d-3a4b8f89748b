{"version": 3, "sources": ["test/rename.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,0DAA2G;AAC3G,2CAA+C;AAG/C,KAAK,UAAU,UAAU,CAAC,KAAa,EAAE,OAAe,EAAE,kBAA0B;IACnF,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAClC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE3D,MAAM,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC,uBAAuB,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAChF,MAAM,SAAS,GAAG;QACjB,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;KAC7C,CAAC;IACF,MAAM,aAAa,GAAG,IAAA,gCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,kCAAkB,CAAC,MAAM,EAAE,IAAA,sBAAa,GAAE,CAAC,CAAC;IAC/H,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC3D,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAE7C,IAAI,cAAc,EAAE,CAAC;QACpB,MAAM,aAAa,GAAyB,MAAM,cAAc,CAAC,QAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAExG,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,MAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,aAAa,GAAG,4BAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC/D,MAAM,CAAC,WAAW,CAAC,aAAa,EAAE,kBAAkB,EAAE,aAAa,kBAAkB,aAAa,aAAa,EAAE,CAAC,CAAC;IACpH,CAAC;SAAM,CAAC;QACP,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IAClD,CAAC;AACF,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,KAAa,EAAE,OAAe;IACzD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAClC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAE3D,MAAM,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC,uBAAuB,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAChF,MAAM,SAAS,GAAG;QACjB,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;KAC7C,CAAC;IACF,MAAM,aAAa,GAAG,IAAA,gCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,kCAAkB,CAAC,MAAM,EAAE,IAAA,sBAAa,GAAE,CAAC,CAAC;IAC/H,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC3D,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;IAE7C,IAAI,cAAc,EAAE,CAAC;QACpB,MAAM,aAAa,GAAyB,MAAM,cAAc,CAAC,QAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QAExG,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,OAAO,KAAK,SAAS,EAAE,uCAAuC,CAAC,CAAC;IAC1F,CAAC;SAAM,CAAC;QACP,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;IAClD,CAAC;AACF,CAAC;AAED,KAAK,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACpC,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QAClC,MAAM,KAAK,GAAG;YACb,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,eAAe;YACf,iBAAiB;YACjB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,MAAM,GAAG;YACd,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,cAAc;YACd,iBAAiB;YACjB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QAClC,MAAM,KAAK,GAAG;YACb,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,qBAAqB;YACrB,4BAA4B;YAC5B,4BAA4B;YAC5B,GAAG;YACH,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,MAAM,GAAG;YACd,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,qBAAqB;YACrB,0BAA0B;YAC1B,4BAA4B;YAC5B,GAAG;YACH,eAAe;YACf,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QACzC,MAAM,KAAK,GAAG;YACb,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,qBAAqB;YACrB,4BAA4B;YAC5B,4BAA4B;YAC5B,GAAG;YACH,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,MAAM,GAAG;YACd,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,qBAAqB;YACrB,8BAA8B;YAC9B,+BAA+B;YAC/B,GAAG;YACH,gBAAgB;YAChB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QAC/B,MAAM,KAAK,GAAG;YACb,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,eAAe;YACf,uBAAuB;YACvB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,MAAM,GAAG;YACd,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,cAAc;YACd,uBAAuB;YACvB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uBAAuB,EAAE,KAAK,IAAI,EAAE;QACxC,MAAM,kBAAkB,GAAG;YAC1B,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,sBAAsB;YACtB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QACF,MAAM,kBAAkB,GAAG;YAC1B,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,iBAAiB;YACjB,WAAW;YACX,SAAS;YACT,SAAS;SACT,CAAC;QAEF,MAAM,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC,CAAC;QAC/D,MAAM,YAAY,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "rename.test.js", "sourceRoot": "../../src/"}