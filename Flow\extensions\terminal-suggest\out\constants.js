"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.upstreamSpecs = void 0;
exports.upstreamSpecs = [
    'ls',
    'echo',
    'mkdir',
    'rm',
    'rmdir',
    'touch',
    'pwd',
    'brew',
    'cp',
    'mv',
    'cat',
    'less',
    'more',
    'head',
    'tail',
    'nano',
    'vim',
    'chmod',
    'chown',
    'uname',
    'top',
    'df',
    'du',
    'ps',
    'kill',
    'killall',
    'curl',
    'wget',
    'ssh',
    'scp',
    'apt',
    'grep',
    'find',
    'git',
    'npm',
    'yarn',
    'python',
    'python3',
    'pnpm',
    'node',
    'nvm',
];
//# sourceMappingURL=constants.js.map