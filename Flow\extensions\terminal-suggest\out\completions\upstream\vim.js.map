{"version": 3, "sources": ["completions/upstream/vim.ts"], "names": [], "mappings": ";;AAAA,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,KAAK;IACX,WAAW,EAAE,yCAAyC;IACtD,IAAI,EAAE;QACL,QAAQ,EAAE,WAAW;QACrB,6BAA6B;KAC7B;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,qBAAqB;SAClC;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,qBAAqB;SAClC;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,kBAAkB;SAC/B;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,8EAA8E;YAC/E,IAAI,EAAE;gBACL,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,WAAW;gBACrB,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,4BAA4B;SACzC;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,mCAAmC;SAChD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,6BAA6B;SAC1C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,+BAA+B;SAC5C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,2CAA2C;SACxD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,mCAAmC;SAChD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,aAAa;SAC1B;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,WAAW;SACxB;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,kCAAkC;SAC/C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,yCAAyC;SACtD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,8CAA8C;YAC3D,IAAI,EAAE;gBACL;oBACC,IAAI,EAAE,GAAG;iBACT;gBACD;oBACC,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,WAAW;iBACrB;aACD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,gBAAgB;SAC7B;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,+BAA+B;SAC5C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,sFAAsF;YACvF,IAAI,EAAE;gBACL,IAAI,EAAE,UAAU;gBAChB,UAAU,EAAE,IAAI;gBAChB,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,YAAY;YACzB,IAAI,EAAE;gBACL,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,iCAAiC;YAC9C,IAAI,EAAE;gBACL,IAAI,EAAE,UAAU;aAChB;SACD;QACD;YACC,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,oDAAoD;SACjE;QACD;YACC,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,2CAA2C;SACxD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,mCAAmC;YAChD,IAAI,EAAE;gBACL,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,2BAA2B;SACxC;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,+CAA+C;YAC5D,IAAI,EAAE;gBACL,IAAI,EAAE,GAAG;gBACT,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,6CAA6C;YAC1D,IAAI,EAAE;gBACL,IAAI,EAAE,GAAG;gBACT,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,8BAA8B;YAC3C,IAAI,EAAE;gBACL,IAAI,EAAE,GAAG;gBACT,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,GAAG;YACT,WAAW,EACV,uEAAuE;YACxE,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,iDAAiD;YAC9D,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,IAAI;aACf;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,gDAAgD;YAC7D,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;aACf;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,oDAAoD;YACjE,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,+CAA+C;YAC5D,IAAI,EAAE;gBACL,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,8CAA8C;YAC3D,IAAI,EAAE;gBACL,IAAI,EAAE,WAAW;gBACjB,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,sBAAsB;SACnC;QACD;YACC,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE,yCAAyC;YACtD,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,mCAAmC;YAChD,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,WAAW;aACrB;SACD;QACD;YACC,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,sDAAsD;SACnE;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;YACtB,WAAW,EAAE,6BAA6B;SAC1C;QACD;YACC,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,oCAAoC;SACjD;KACD;CACD,CAAC;AAEF,kBAAe,cAAc,CAAC", "file": "vim.js", "sourceRoot": "../../../src/"}