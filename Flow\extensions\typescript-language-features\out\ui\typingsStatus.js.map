{"version": 3, "sources": ["ui/typingsStatus.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AAEjC,8CAA8C;AAG9C,MAAM,qBAAqB,GAAG,EAAE,GAAG,IAAI,CAAC;AAExC,MAAqB,aAAc,SAAQ,oBAAU;IAIpD,YAAY,MAAgC;QAC3C,KAAK,EAAE,CAAC;QAJQ,sBAAiB,GAAG,IAAI,GAAG,EAA0B,CAAC;QAKtE,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,CAAC,SAAS,CACb,IAAI,CAAC,OAAO,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE5F,IAAI,CAAC,SAAS,CACb,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC;IAEe,OAAO;QACtB,KAAK,CAAC,OAAO,EAAE,CAAC;QAEhB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,EAAE,CAAC;YACvD,YAAY,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;IACF,CAAC;IAED,IAAW,kBAAkB;QAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACvD,CAAC;IAEO,qBAAqB,CAAC,OAAe;QAC5C,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACzC,OAAO;QACR,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,EAAE;YACnD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC,EAAE,qBAAqB,CAAC,CAAC,CAAC;IAC5B,CAAC;IAEO,mBAAmB,CAAC,OAAe;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,KAAK,EAAE,CAAC;YACX,YAAY,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;CACD;AA3CD,gCA2CC;AAED,MAAa,mBAAoB,SAAQ,oBAAU;IAIlD,YAAY,MAAgC;QAC3C,KAAK,EAAE,CAAC;QAHQ,cAAS,GAAG,IAAI,GAAG,EAAoB,CAAC;QAIxD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/E,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpF,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,oCAAoC,EAAE,CAAC,CAAC,CAAC;IAC/G,CAAC;IAEQ,OAAO;QACf,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;IAC1C,CAAC;IAEO,QAAQ,CAAC,OAAe;QAC/B,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,qBAAqB,CAAC,CAAC;QACtF,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,OAAO,CAAC,EAAE;YAC3C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE;gBAChC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACrB,OAAO,EAAE,CAAC;YACX,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAC1B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;YACxC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,kDAAkD,CAAC;SACxE,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;IAEO,eAAe,CAAC,OAAe;QACtC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACb,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC/B,OAAO,EAAE,CAAC;QACX,CAAC;IACF,CAAC;IAEO,KAAK,CAAC,oCAAoC;QACjD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAE/D,IAAI,MAAM,CAAC,GAAG,CAAU,sBAAsB,EAAE,IAAI,CAAC,EAAE,CAAC;YACvD,MAAM,aAAa,GAAuB;gBACzC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC;aACxC,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACtD,MAAM,CAAC,IAAI,CAAC,CAAC,CACZ,0NAA0N,EAC1N,gDAAgD,CAChD,EACD,aAAa,CAAC,CAAC;YAEhB,IAAI,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACpD,CAAC;QACF,CAAC;IACF,CAAC;CACD;AA1DD,kDA0DC", "file": "typingsStatus.js", "sourceRoot": "../../src/"}