{"version": 3, "sources": ["languageFeatures/semanticTokens.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhG,4BAUC;AAnBD,+CAAiC;AAGjC,4DAAkF;AAClF,wEAA8F;AAE9F,+GAA+G;AAC/G,MAAM,oBAAoB,GAAG,MAAM,CAAC;AAEpC,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,MAAgC;IAEhC,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACxD,EAAE,GAAG,EAAE;QACP,MAAM,QAAQ,GAAG,IAAI,8BAA8B,CAAC,MAAM,CAAC,CAAC;QAC5D,OAAO,MAAM,CAAC,SAAS,CAAC,2CAA2C,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;IACxH,CAAC,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,8BAA8B;IAEnC,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;IAC9C,CAAC;IAEE,SAAS;QACf,OAAO,IAAI,MAAM,CAAC,oBAAoB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IACpE,CAAC;IAEM,KAAK,CAAC,6BAA6B,CAAC,QAA6B,EAAE,KAA+B;QACxG,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,oBAAoB,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC;QACb,CAAC;QACD,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;IAC3G,CAAC;IAEM,KAAK,CAAC,kCAAkC,CAAC,QAA6B,EAAE,KAAmB,EAAE,KAA+B;QAClI,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,oBAAoB,CAAC,EAAE,CAAC;YACrG,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC7C,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpD,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;IAC7E,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,QAA6B,EAAE,UAA2D,EAAE,KAA+B;QAC9J,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC;QAE9C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qCAAqC,EAAE,EAAE,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE;YAC3H,sBAAsB,EAAE,QAAQ,CAAC,GAAG;SACpC,CAAC,CAAC;QACH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC;QACb,CAAC;QAED,MAAM,mBAAmB,GAAG,QAAQ,CAAC,OAAO,CAAC;QAE7C,IAAI,oBAAoB,KAAK,mBAAmB,EAAE,CAAC;YAClD,iEAAiE;YACjE,qCAAqC;YACrC,EAAE;YACF,uFAAuF;YACvF,4EAA4E;YAC5E,0GAA0G;YAE1G,4GAA4G;YAC5G,MAAM,2BAA2B,CAAC,QAAQ,CAAC,CAAC;YAE5C,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;QACtC,CAAC;QAED,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;QAEtC,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,qBAAqB,EAAE,CAAC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC;YACvC,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9B,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9B,MAAM,gBAAgB,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAExC,MAAM,SAAS,GAAG,8BAA8B,CAAC,gBAAgB,CAAC,CAAC;YACnE,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC7B,SAAS;YACV,CAAC;YAED,MAAM,cAAc,GAAG,kCAAkC,CAAC,gBAAgB,CAAC,CAAC;YAE5E,+GAA+G;YAC/G,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;YAEpD,KAAK,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;gBAC5D,MAAM,cAAc,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,MAAM,YAAY,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACnG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,EAAE,YAAY,GAAG,cAAc,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAC9F,CAAC;QACF,CAAC;QAED,OAAO,OAAO,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;CACD;AAED,SAAS,2BAA2B,CAAC,QAA6B;IACjE,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;IAC/B,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;QACpC,MAAM,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;YAC1B,IAAI,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;gBAClC,aAAa,CAAC,EAAE,CAAC,CAAC;gBAClB,OAAO,EAAE,CAAC;YACX,CAAC;YACD,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QAC5B,CAAC,EAAE,GAAG,CAAC,CAAC;IACT,CAAC,CAAC,CAAC;AACJ,CAAC;AAqCD,SAAS,8BAA8B,CAAC,gBAAwB;IAC/D,IAAI,gBAAgB,6CAAmC,EAAE,CAAC;QACzD,OAAO,CAAC,gBAAgB,0CAAkC,CAAC,GAAG,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC;AAED,SAAS,kCAAkC,CAAC,gBAAwB;IACnE,OAAO,gBAAgB,6CAAmC,CAAC;AAC5D,CAAC;AAED,MAAM,UAAU,GAAa,EAAE,CAAC;AAChC,UAAU,yBAAiB,GAAG,OAAO,CAAC;AACtC,UAAU,wBAAgB,GAAG,MAAM,CAAC;AACpC,UAAU,6BAAqB,GAAG,WAAW,CAAC;AAC9C,UAAU,6BAAqB,GAAG,WAAW,CAAC;AAC9C,UAAU,iCAAyB,GAAG,eAAe,CAAC;AACtD,UAAU,wBAAgB,GAAG,MAAM,CAAC;AACpC,UAAU,6BAAqB,GAAG,WAAW,CAAC;AAC9C,UAAU,4BAAoB,GAAG,UAAU,CAAC;AAC5C,UAAU,8BAAsB,GAAG,YAAY,CAAC;AAChD,UAAU,4BAAoB,GAAG,UAAU,CAAC;AAC5C,UAAU,6BAAoB,GAAG,UAAU,CAAC;AAC5C,UAAU,2BAAkB,GAAG,QAAQ,CAAC;AAExC,MAAM,cAAc,GAAa,EAAE,CAAC;AACpC,cAAc,6BAAqB,GAAG,OAAO,CAAC;AAC9C,cAAc,mCAA2B,GAAG,aAAa,CAAC;AAC1D,cAAc,gCAAwB,GAAG,UAAU,CAAC;AACpD,cAAc,8BAAsB,GAAG,QAAQ,CAAC;AAChD,cAAc,6BAAqB,GAAG,OAAO,CAAC;AAC9C,cAAc,sCAA8B,GAAG,gBAAgB,CAAC", "file": "semanticTokens.js", "sourceRoot": "../../src/"}