{"version": 3, "sources": ["shell/common.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhG,kCAmBC;AAOD,oCAoBC;AAED,gCAUC;AAED,4CA0BC;AA1FD,+CAAiC;AACjC,2DAAoH;AAG7G,KAAK,UAAU,WAAW,CAAC,OAAe,EAAE,IAAc,EAAE,OAAiC;IACnG,mFAAmF;IACnF,oFAAoF;IACpF,+EAA+E;IAC/E,iDAAiD;IACjD,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC9C,MAAM,KAAK,GAAG,IAAA,0BAAK,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAChC,MAAM,IAAI,IAAI,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;gBAChB,MAAM,CAAC,IAAI,KAAK,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACP,OAAO,CAAC,MAAM,CAAC,CAAC;YACjB,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC;AAOM,KAAK,UAAU,YAAY,CAAC,OAAe,EAAE,IAAc,EAAE,OAAiC;IACpG,mFAAmF;IACnF,oFAAoF;IACpF,+EAA+E;IAC/E,iDAAiD;IACjD,OAAO,IAAI,OAAO,CAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1D,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,KAAK,GAAG,IAAA,0BAAK,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC5C,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACrD,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5C,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC1B,OAAO,CAAC;gBACP,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvB,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC;aACpB,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,UAAU,CAAC,WAAmB,EAAE,OAAsC;IAC3F,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC9C,IAAA,yBAAI,EAAC,WAAW,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAC5C,IAAI,KAAK,EAAE,CAAC;gBACX,MAAM,CAAC,KAAK,CAAC,CAAC;YACf,CAAC;iBAAM,CAAC;gBACP,OAAO,CAAC,MAAM,CAAC,CAAC;YACjB,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,gBAAgB,CAAC,OAAe,EAAE,IAAc,EAAE,KAAa,EAAE,OAAsC;IAC5H,mFAAmF;IACnF,oFAAoF;IACpF,+EAA+E;IAC/E,iDAAiD;IACjD,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC9D,MAAM,MAAM,GAA0B,EAAE,CAAC;IACzC,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;YACpB,SAAS;QACV,CAAC;QACD,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAC3B,IAAI,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5B,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;QAChD,CAAC;QACD,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;QACxE,MAAM,CAAC,IAAI,CAAC;YACX,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE;YACxE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ;YAC7B,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,KAAK;YAC7C,iBAAiB;SACjB,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC", "file": "common.js", "sourceRoot": "../../src/"}