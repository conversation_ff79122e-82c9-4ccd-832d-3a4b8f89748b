{"version": 3, "sources": ["test/smoke/referencesCodeLens.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,iBAAe;AACf,+CAAiC;AACjC,oDAA8D;AAC9D,iDAAiD;AAKjD,KAAK,UAAU,YAAY,CAAC,SAA8B;IACzD,MAAM,SAAS,GAAwB,EAAE,CAAC;IAC1C,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;IAC5D,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QAChD,SAAS,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7C,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAC3C,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;aAC/E,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC;AAED,IAAU,MAAM,CAEf;AAFD,WAAU,MAAM;IACF,yBAAkB,GAAG,uCAAuC,CAAC;AAC3E,CAAC,EAFS,MAAM,KAAN,MAAM,QAEf;AAED,KAAK,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACnC,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAsB;QACzD,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE,IAAI;KACjC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAwB,EAAE,CAAC;IAC7C,IAAI,SAAS,GAA2B,EAAE,CAAC;IAE3C,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,2DAA2D;QAC3D,MAAM,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,qCAAqC,CAAE,CAAC,QAAQ,EAAE,CAAC;QAExF,qCAAqC;QACrC,SAAS,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QACnB,IAAA,oBAAU,EAAC,YAAY,CAAC,CAAC;QAEzB,iBAAiB;QACjB,MAAM,YAAY,CAAC,SAAS,CAAC,CAAC;QAE9B,OAAO,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kCAAkC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,4BAA4B,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC9D,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACrC,cAAc,CACd,CAAC;QAEF,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,CAAC;QACxD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC9D,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACrC,aAAa,EACb,gBAAgB,EAChB,kBAAkB,EAClB,GAAG,CACH,CAAC;QAEF,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,CAAC;QACxD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC9D,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACrC,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,GAAG,CACH,CAAC;QAEF,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,CAAC;QACxD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI,CAAC,sGAAsG,EAAE,KAAK,IAAI,EAAE;QAC5H,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC9D,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACrC,gBAAgB,EAChB,wBAAwB,EACxB,GAAG,EACH,WAAW,CACX,CAAC;QAEF,MAAM,IAAA,gBAAI,EAAC,GAAG,CAAC,CAAC;QAChB,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,eAAe,CAAC,CAAC;QACxD,MAAM,CAAC,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,SAAS,aAAa,CAAC,QAAoB;IAC1C,OAAO,MAAM,CAAC,QAAQ,CAAC,cAAc,CAA6B,gCAAgC,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC;AACpH,CAAC", "file": "referencesCodeLens.test.js", "sourceRoot": "../../../src/"}