{"version": 3, "sources": ["languageFeatures/util/textRendering.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4HhG,oDAKC;AAmFD,wCAKC;AAED,0DAWC;AAED,sEAoBC;AA1PD,+CAAiC;AACjC,gEAA+F;AAE/F,qEAAuD;AASvD,SAAS,cAAc,CACtB,GAAuB,EACvB,iBAA+C;IAE/C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACf,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,oEAAoE;IACpE,SAAS,aAAa,CAAC,IAAY;QAClC,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;QACb,CAAC;QACD,OAAO,UAAU,GAAG,IAAI,GAAG,OAAO,CAAC;IACpC,CAAC;IAED,IAAI,IAAI,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;IACxD,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,SAAS,CAAC,CAAC,CAAC;YAChB,oEAAoE;YACpE,8EAA8E;YAC9E,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE7B,yCAAyC;YACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAC9E,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;gBACxD,OAAO,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YAC9F,CAAC;iBAAM,CAAC;gBACP,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACF,CAAC;QACD,KAAK,QAAQ,CAAC,CAAC,CAAC;YACf,sCAAsC;YACtC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC3D,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC;YACb,CAAC;iBAAM,CAAC;gBACP,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5C,CAAC;QACF,CAAC;QACD,KAAK,SAAS,CAAC,CAAC,CAAC;YAChB,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;QAC5B,CAAC;QACD,OAAO,CAAC,CAAC,CAAC;YACT,OAAO,IAAI,CAAC;QACb,CAAC;IACF,CAAC;AACF,CAAC;AAED,SAAS,mBAAmB,CAC3B,GAAuB,EACvB,iBAA+C;IAE/C,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,UAAU,CAAC;QAChB,KAAK,SAAS,CAAC;QACf,KAAK,OAAO,CAAC;QACb,KAAK,UAAU,CAAC,CAAC,CAAC;YACjB,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;YAChD,IAAI,IAAI,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxB,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,IAAI,CAAC;gBAC5C,IAAI,CAAC,GAAG,EAAE,CAAC;oBACV,OAAO,KAAK,CAAC;gBACd,CAAC;gBACD,OAAO,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,WAAW,GAAG,EAAE,CAAC,CAAC;YAC1E,CAAC;YACD,MAAM;QACP,CAAC;QACD,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS,CAAC,CAAC,CAAC;YAChB,6CAA6C;YAC7C,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC;gBACvB,OAAO,SAAS,CAAC;YAClB,CAAC;YAED,MAAM;QACP,CAAC;IACF,CAAC;IAGD,cAAc;IACd,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC;IAC/B,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;IACpD,IAAI,CAAC,IAAI,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACd,CAAC;IACD,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;AAC7E,CAAC;AAED,SAAS,UAAU,CAAC,GAAuB,EAAE,iBAA+C;IAC3F,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC;QACvB,IAAI,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7F,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC1I,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAChD,CAAC;IACF,CAAC;IACD,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,WAAW,CAAC,KAAkD;IACtE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,KAAK,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9C,CAAC;AAED,SAAgB,oBAAoB,CACnC,KAAkD,EAClD,iBAA+C;IAE/C,OAAO,eAAe,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC;AAClD,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CACvB,KAA8D,EAC9D,iBAA+C;IAE/C,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,OAAO,EAAE,CAAC;IACX,CAAC;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,KAAK,CAAC;IACd,CAAC;IAED,MAAM,GAAG,GAAa,EAAE,CAAC;IAEzB,IAAI,WAA8G,CAAC;IACnH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QAC1B,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,MAAM;gBACV,IAAI,WAAW,EAAE,CAAC;oBACjB,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;wBACxB,MAAM,IAAI,GAAG,iBAAiB,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBACnE,MAAM,IAAI,GAA8B;4BACvC,IAAI,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,oDAAoD;4BACjG,QAAQ,EAAE,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;yBACxE,CAAC;wBACF,MAAM,OAAO,GAAG,WAAW,oCAAoB,CAAC,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;wBAEnG,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,iCAAiC,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;wBACjH,GAAG,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC,CAAC;oBACrF,CAAC;yBAAM,CAAC;wBACP,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;wBAClD,IAAI,IAAI,EAAE,CAAC;4BACV,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gCAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gCAC9B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;oCACjD,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gCAC3B,CAAC;qCAAM,CAAC;oCACP,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oCACxE,GAAG,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,iCAAiC,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gCACzH,CAAC;4BACF,CAAC;iCAAM,CAAC;gCACP,GAAG,CAAC,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC,CAAC;4BACnD,CAAC;wBACF,CAAC;oBACF,CAAC;oBACD,WAAW,GAAG,SAAS,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACP,WAAW,GAAG;wBACb,QAAQ,EAAE,IAAI,CAAC,IAAI,KAAK,aAAa;qBACrC,CAAC;gBACH,CAAC;gBACD,MAAM;YAEP,KAAK,UAAU;gBACd,IAAI,WAAW,EAAE,CAAC;oBACjB,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;oBAC7B,WAAW,CAAC,MAAM,GAAI,IAAmC,CAAC,MAAM,CAAC;gBAClE,CAAC;gBACD,MAAM;YAEP,KAAK,UAAU;gBACd,IAAI,WAAW,EAAE,CAAC;oBACjB,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC9B,CAAC;gBACD,MAAM;YAEP;gBACC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpB,MAAM;QACR,CAAC;IACF,CAAC;IACD,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACrB,CAAC;AAED,SAAS,iCAAiC,CAAC,IAAY;IACtD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,iHAAiH;AACrJ,CAAC;AAED,SAAgB,cAAc,CAC7B,IAAmC,EACnC,iBAA+C;IAE/C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACpF,CAAC;AAED,SAAgB,uBAAuB,CACtC,aAA0D,EAC1D,IAAmC,EACnC,iBAA+C,EAC/C,OAA+B;IAE/B,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;IACxC,6BAA6B,CAAC,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC;IAC3E,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;IACtB,GAAG,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,CAAC,oCAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;IAC/D,OAAO,GAAG,CAAC;AACZ,CAAC;AAED,SAAgB,6BAA6B,CAC5C,GAA0B,EAC1B,aAAsE,EACtE,IAA+C,EAC/C,SAAuC;IAEvC,IAAI,aAAa,EAAE,CAAC;QACnB,GAAG,CAAC,cAAc,CAAC,oBAAoB,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IACpE,CAAC;IAED,IAAI,IAAI,EAAE,CAAC;QACV,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACpD,IAAI,WAAW,EAAE,CAAC;YACjB,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC;QAC1C,CAAC;IACF,CAAC;IAED,GAAG,CAAC,SAAS,GAAG,EAAE,eAAe,EAAE,CAAC,oCAAoB,CAAC,EAAE,CAAC,EAAE,CAAC;IAE/D,OAAO,GAAG,CAAC;AACZ,CAAC", "file": "textRendering.js", "sourceRoot": "../../../src/"}