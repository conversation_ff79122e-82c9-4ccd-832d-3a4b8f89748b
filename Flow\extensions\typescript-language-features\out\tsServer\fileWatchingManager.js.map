{"version": 3, "sources": ["tsServer/fileWatchingManager.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,2CAAmC;AACnC,sDAAmD;AAEnD,8CAA2D;AAC3D,sDAAmD;AAQnD,MAAa,kBAAkB;IAc9B,YACkB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;QAbf,kBAAa,GAAG,IAAI,GAAG,EAIpC,CAAC;QAEY,iBAAY,GAAG,IAAI,yBAAW,CAI5C,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,EAAE,2BAA2B,EAAE,KAAK,EAAE,CAAC,CAAC;IAI9D,CAAC;IAEL,OAAO;QACN,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YACjD,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YAChD,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED,MAAM,CAAC,EAAU,EAAE,GAAe,EAAE,eAAwB,EAAE,WAAoB,EAAE,SAAyH;QAC5M,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAEjE,yDAAyD;QACzD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3D,OAAO;QACR,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7K,MAAM,iBAAiB,GAAsB,EAAE,CAAC;QAChD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,CAAC,CAAC;QAE7E,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAAC,CAAC;QAChE,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAAC,CAAC;QAChE,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAAC,CAAC;QAEhE,IAAI,eAAe,IAAI,GAAG,CAAC,MAAM,KAAK,iBAAO,CAAC,QAAQ,EAAE,CAAC;YACxD,mFAAmF;YACnF,KAAK,IAAI,MAAM,GAAG,kBAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,kBAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9F,MAAM,WAAW,GAAkB,EAAE,CAAC;gBAEtC,IAAI,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACrD,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;oBAC1E,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,kBAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,kBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;oBACvF,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACjH,gBAAgB,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;oBACxE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;gBACjD,CAAC;gBACD,gBAAgB,CAAC,QAAQ,EAAE,CAAC;gBAE5B,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBACtB,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI,EAAE;wBAChE,4EAA4E;wBAC5E,IAAI,CAAC;4BACJ,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BACjD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gCACxC,SAAS,CAAC,MAAO,CAAC,GAAG,CAAC,CAAC;4BACxB,CAAC;wBACF,CAAC;wBAAC,MAAM,CAAC;4BACR,OAAO;wBACR,CAAC;oBACF,CAAC,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBACtB,gEAAgE;oBAChE,uFAAuF;oBACvF,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC1E,CAAC;gBAED,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC;YACtD,CAAC;QACF,CAAC;IACF,CAAC;IAGD,MAAM,CAAC,EAAU;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,KAAK,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;YAE5D,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC5C,IAAA,oBAAU,EAAC,UAAU,CAAC,WAAW,CAAC,CAAC;gBAEnC,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;gBAC9D,IAAI,eAAe,EAAE,CAAC;oBACrB,IAAI,EAAE,eAAe,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;wBACrC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;wBAChE,eAAe,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;wBAClC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAC1C,CAAC;gBACF,CAAC;YACF,CAAC;YAED,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;CACD;AA9GD,gDA8GC", "file": "fileWatchingManager.js", "sourceRoot": "../../src/"}