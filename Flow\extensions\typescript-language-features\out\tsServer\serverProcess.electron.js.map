{"version": 3, "sources": ["tsServer/serverProcess.electron.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,6DAA+C;AAC/C,uCAAyB;AACzB,2CAA6B;AAE7B,+CAAiC;AAEjC,8CAA8C;AAC9C,+BAA4B;AAQ5B,MAAM,WAAW,GAAW,IAAI,CAAC;AACjC,MAAM,aAAa,GAAW,kBAAkB,CAAC;AACjD,MAAM,iBAAiB,GAAW,MAAM,CAAC,UAAU,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC3E,MAAM,KAAK,GAAW,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,MAAM,UAAU,GAAW,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,MAAM,UAAU,GAAW,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAExD,MAAM,cAAc;IAApB;QAES,UAAK,GAAW,CAAC,CAAC;QAClB,WAAM,GAAW,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IA6D1D,CAAC;IA3DO,MAAM,CAAC,IAAqB;QAClC,IAAI,QAAQ,GAAkB,IAAI,CAAC;QACnC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,QAAQ,GAAG,IAAI,CAAC;QACjB,CAAC;aAAM,CAAC;YACP,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACxD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC;aAAM,CAAC;YACP,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC;YAC5F,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC1C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;YACpF,CAAC;QACF,CAAC;QACD,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC;IAC/B,CAAC;IAEM,oBAAoB;QAC1B,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;QAChB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,0BAA0B;QAC1B,OAAO,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC;YAC/I,OAAO,EAAE,CAAC;QACX,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,iBAAiB,EAAE,CAAC;YAC9C,OAAO,MAAM,CAAC;QACf,CAAC;QACD,OAAO,IAAI,iBAAiB,CAAC;QAC7B,MAAM,KAAK,GAAG,OAAO,CAAC;QACtB,OAAO,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,EAAE,CAAC;YACpE,OAAO,EAAE,CAAC;QACX,CAAC;QACD,IAAI,OAAO,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE,CAAC;YAChK,OAAO,MAAM,CAAC;QACf,CAAC;QACD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1D,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;QACxC,OAAO,MAAM,CAAC;IACf,CAAC;IAEM,cAAc,CAAC,MAAc;QACnC,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACb,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;QACvD,IAAI,WAAW,GAAG,MAAM,CAAC;QACzB,OAAO,WAAW,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,UAAU,CAAC,EAAE,CAAC;YACzH,WAAW,EAAE,CAAC;QACf,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;QAC9C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QACtC,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AAED,MAAM,MAAU,SAAQ,oBAAU;IAKjC,YAAmB,QAAkB;QACpC,KAAK,EAAE,CAAC;QAJQ,WAAM,GAAmB,IAAI,cAAc,EAAE,CAAC;QACvD,sBAAiB,GAAW,CAAC,CAAC,CAAC;QAOtB,aAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAS,CAAC,CAAC;QAC7D,YAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAE7B,YAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAK,CAAC,CAAC;QACxD,WAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAP3C,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC;IAQO,YAAY,CAAC,IAAqB;QACzC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO;QACR,CAAC;QAED,IAAI,CAAC;YACJ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzB,OAAO,IAAI,EAAE,CAAC;gBACb,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;oBACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;oBAC5D,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE,CAAC;wBACnC,OAAO;oBACR,CAAC;gBACF,CAAC;gBACD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAC/D,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;oBAClB,OAAO;gBACR,CAAC;gBACD,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;gBAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACF,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC;IACF,CAAC;CACD;AAED,SAAS,kBAAkB,CAAC,GAAQ,EAAE,UAAkB,EAAE,WAAoB;IAC7E,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;IAEtC,IAAI,CAAC,WAAW,EAAE,CAAC;QAClB,MAAM,CAAC,sBAAsB,CAAC,GAAG,GAAG,CAAC;IACtC,CAAC;IACD,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAE9D,mCAAmC;IACnC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;IAEpD,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,WAAW,CAAC,IAAyB,EAAE,aAA6C;IAC5F,MAAM,IAAI,GAAa,EAAE,CAAC;IAE1B,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACrC,IAAI,SAAS,EAAE,CAAC;QACf,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,CAAC;QACrE,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,IAAI,SAAS,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,IAAI,aAAa,CAAC,iBAAiB,EAAE,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,wBAAwB,aAAa,CAAC,iBAAiB,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,OAAO,IAAI,CAAC;AACb,CAAC;AAED,SAAS,YAAY,CAAC,IAAyB;IAC9C,IAAI,IAAI,8CAA+B,EAAE,CAAC;QACzC,2DAA2D;QAC3D,OAAO,SAAS,CAAC;IAClB,CAAC;IACD,MAAM,KAAK,GAAG,cAAc,EAAE,IAAI,WAAW,EAAE,CAAC;IAChD,IAAI,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QACb,CAAC;IACF,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC;AAED,SAAS,WAAW;IACnB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,cAAc;IACtB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;AACtF,CAAC;AAED,MAAM,qBAAsB,SAAQ,oBAAU;IAC7C,YACkB,QAAoC;QAErD,KAAK,EAAE,CAAC;QAFS,aAAQ,GAAR,QAAQ,CAA4B;IAGtD,CAAC;IAED,KAAK,CAAC,aAA4B;QACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,CAAC,OAAuC;QAC7C,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;IAED,MAAM,CAAC,OAA6D;QACnE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,CAAC,OAA6B;QACpC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACpC,CAAC;IAED,IAAI;QACH,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;CACD;AAED,MAAM,uBAAwB,SAAQ,oBAAU;IAG/C,YACkB,QAAoC;QAErD,KAAK,EAAE,CAAC;QAFS,aAAQ,GAAR,QAAQ,CAA4B;QAGrD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAiB,IAAI,CAAC,QAAQ,CAAC,MAAO,CAAC,CAAC,CAAC;IAClF,CAAC;IAED,KAAK,CAAC,aAA4B;QACjC,IAAI,CAAC,QAAQ,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,GAAG,MAAM,EAAE,MAAM,CAAC,CAAC;IAC5E,CAAC;IAED,MAAM,CAAC,OAAuC;QAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,OAA6D;QACnE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,CAAC,OAA6B;QACpC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED,IAAI;QACH,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;CACD;AAED,MAAa,6BAA6B;IACzC,IAAI,CACH,OAA0B,EAC1B,IAAuB,EACvB,IAAyB,EACzB,aAA6C,EAC7C,cAAwC,EACxC,kBAAsC,EACtC,YAAqC;QAErC,IAAI,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAExC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YAClC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,sGAAsG,EAAE,YAAY,CAAC,CAAC,CAAC;YACtK,cAAc,CAAC,KAAK,EAAE,CAAC;YACvB,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC,YAAY,CAAC;QAC3D,CAAC;QAED,MAAM,QAAQ,GAAG,kBAAkB,CAAC,cAAc,CAAC;QAEnD,MAAM,GAAG,GAAG,kBAAkB,CAAC,OAAO,CAAC,GAAG,EAAE,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtE,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QAC9B,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAClD,MAAM,MAAM,GAAG,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC,CAAC;QAC9D,IAAI,MAAM,EAAE,CAAC;YACZ,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC;YAC9B,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,GAAG,QAAQ,EAAE,YAAY,EAAE,GAAG,WAAW,CAAC,EAAE;gBAC1E,WAAW,EAAE,IAAI;gBACjB,GAAG,EAAE,SAAS;gBACd,GAAG;aACH,CAAC,CAAC,CAAC;YACJ,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE;gBAC7C,MAAM,EAAE,IAAI;gBACZ,GAAG,EAAE,SAAS;gBACd,GAAG;gBACH,QAAQ;gBACR,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;aAC3D,CAAC,CAAC;QAEJ,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,qBAAqB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,uBAAuB,CAAC,YAAY,CAAC,CAAC;IACrG,CAAC;CACD;AA5CD,sEA4CC", "file": "serverProcess.electron.js", "sourceRoot": "../../src/"}