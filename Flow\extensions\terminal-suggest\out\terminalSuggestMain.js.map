{"version": 3, "sources": ["terminalSuggestMain.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFhG,4BAgEC;AAQD,oDAoCC;AAyBD,0BAEC;AAED,kEAmFC;AA8CD,gEAsBC;AAhXD,gDAAkC;AAClC,2CAA6B;AAC7B,+CAAiC;AACjC,0DAAsC;AACtC,8DAAoD;AACpD,gFAAqE;AACrE,4DAAkD;AAClD,8EAAyD;AACzD,2CAA4C;AAC5C,mEAA4G;AAC5G,qCAA2C;AAC3C,uCAAwD;AACxD,uCAA8C;AAC9C,uCAA8C;AAC9C,uCAA8C;AAC9C,qCAA4C;AAC5C,qCAAmD;AAEnD,6DAAgE;AAChE,qDAAuD;AACvD,2CAA4F;AAC5F,+CAAyD;AACzD,4EAAiE;AACjE,8FAAkF;AAUlF,MAAM,SAAS,GAAG,IAAA,gBAAW,GAAE,CAAC;AAChC,MAAM,aAAa,GAA8D,IAAI,GAAG,EAAE,CAAC;AAC3F,IAAI,mBAAwC,CAAC;AAEhC,QAAA,cAAc,GAAe;IACzC,YAAM;IACN,uBAA0B;IAC1B,cAAkB;IAClB,qBAAwB;IACxB,8BAAgC;IAChC,aAAiB;IACjB,sBAAe;CACf,CAAC;AACF,KAAK,MAAM,IAAI,IAAI,yBAAa,EAAE,CAAC;IAClC,sBAAc,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AACxE,CAAC;AAED,MAAM,uBAAuB,GAAkJ,IAAI,GAAG,CAAC;IACtL,sCAAyB,qBAAc,CAAC;IACxC,oCAAwB,mBAAa,CAAC;IACtC,sFAAsF;IACtF,sCAAyB,qBAAc,CAAC;IACxC,4CAA+B,qBAAc,CAAC;CAC9C,CAAC,CAAC;AAEH,KAAK,UAAU,eAAe,CAAC,SAA4B,EAAE,gBAA8B;IAC1F,IAAI,CAAC;QACJ,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,cAAc,EAAE,CAAC;YACpB,OAAO,cAAc,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;YAChB,OAAO;QACR,CAAC;QACD,MAAM,OAAO,GAAkC,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;QACvF,MAAM,aAAa,GAAiD,MAAM,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAC9I,MAAM,kBAAkB,GAAG,aAAa,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACvH,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;QACjD,OAAO,kBAAkB,CAAC;IAE3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QACzD,OAAO;IACR,CAAC;AACF,CAAC;AAGM,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC9D,mBAAmB,GAAG,IAAI,yCAAmB,EAAE,CAAC;IAChD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAChD,IAAI,kBAAkB,GAAyB,OAAO,CAAC,GAAG,CAAC;IAC3D,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,kCAAkC,CAAC;QAC3E,EAAE,EAAE,kBAAkB;QACtB,KAAK,CAAC,0BAA0B,CAAC,QAAyB,EAAE,eAAiD,EAAE,KAA+B;YAC7I,kBAAkB,GAAG,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC;YAC1E,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBACnC,OAAO,CAAC,KAAK,CAAC,mDAAmD,CAAC,CAAC;gBACnE,OAAO;YACR,CAAC;YAED,MAAM,SAAS,GAAuB,OAAO,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAe,CAAC,CAAC,CAAC,SAAS,CAAC;YAC7G,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACxB,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;gBACvE,OAAO;YACR,CAAC;YAED,MAAM,cAAc,GAAG,MAAM,mBAAmB,CAAC,oBAAoB,CAAC,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7G,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,iBAAiB,EAAE,cAAc,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC;YAC5F,IAAI,CAAC,cAAc,EAAE,mBAAmB,EAAE,CAAC;gBAC1C,OAAO,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC;gBAChE,OAAO;YACR,CAAC;YACD,8FAA8F;YAC9F,MAAM,QAAQ,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,cAAc,CAAC,mBAAmB,CAAC,CAAC;YAC1E,MAAM,MAAM,GAAG,SAAS,CAAC,eAAe,CAAC,WAAW,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;YACtF,MAAM,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAA,qBAAY,EAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;YACnE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;gBACjC,2BAA2B,CAC1B,sBAAc,EACd,eAAe,EACf,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAC9B,cAAc,CAAC,kBAAkB,CAAC,EAClC,QAAQ,CAAC,IAAI,EACb,KAAK,CACL;gBACD,IAAA,8BAAoB,EAAC,GAAG,EAAE,SAAS,CAAC;aACpC,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACb,OAAO;YACR,CAAC;YAED,IAAI,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC;gBACpC,MAAM,iBAAiB,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,CAAC,CAAC;gBAClE,IAAI,iBAAiB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;oBACrE,iBAAiB,CAAC,aAAa,GAAG,IAAA,6BAAuB,EAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC;oBAC9K,iBAAiB,CAAC,IAAI,GAAG,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;gBACnE,CAAC;YACF,CAAC;YAED,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACtE,OAAO,IAAI,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,cAAc,EAAE,MAAM,CAAC,cAAc,EAAE,gBAAgB,EAAE,MAAM,CAAC,gBAAgB,EAAE,cAAc,EAAE,MAAM,CAAC,cAAc,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;YAClP,CAAC;YACD,OAAO,MAAM,CAAC,KAAK,CAAC;QACrB,CAAC;KACD,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;IACf,MAAM,IAAA,0CAAoB,EAAC,OAAO,EAAE,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;AAC9E,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,oBAAoB,CAAC,MAAc,EAAE,UAAuB;IACjF,IAAI,CAAC,UAAU,EAAE,CAAC;QACjB,OAAO;IACR,CAAC;IACD,IAAI,CAAC;QACJ,wFAAwF;QACxF,mDAAmD;QACnD,IAAI,cAAsB,CAAC;QAC3B,IAAI,SAAS,EAAE,CAAC;YACf,wFAAwF;YACxF,wFAAwF;YACxF,mBAAmB;YACnB,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,cAAc,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3B,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC1C,CAAC;QACF,CAAC;aAAM,CAAC;YACP,cAAc,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC;QACD,MAAM,cAAc,GAAG,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;QAEpF,0CAA0C;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;QAEtE,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEzC,uDAAuD;QACvD,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,OAAO,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;QAChD,CAAC;IACF,CAAC;IAAC,MAAM,CAAC;QACR,gBAAgB;IACjB,CAAC;IAED,sBAAsB;IACtB,OAAO,SAAS,CAAC;AAClB,CAAC;AAED,SAAS,SAAS,CAAC,WAAmB,EAAE,cAAsB;IAC7D,qEAAqE;IACrE,IAAI,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;QAC/B,OAAO,EAAE,CAAC;IACX,CAAC;IAED,gFAAgF;IAChF,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;QACnF,OAAO,EAAE,CAAC;IACX,CAAC;IAED,yDAAyD;IACzD,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;IAE1D,wEAAwE;IACxE,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAE9C,iDAAiD;IACjD,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC9B,CAAC;AAID,SAAgB,OAAO,CAAI,CAAU;IACpC,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,CAAC;AAEM,KAAK,UAAU,2BAA2B,CAChD,KAAiB,EACjB,eAAiD,EACjD,iBAAwC,EACxC,MAAc,EACd,SAAoB,EACpB,mBAA2C,EAC3C,GAA2B,EAC3B,IAAY,EACZ,KAAgC,EAChC,gBAAuC;IAEvC,MAAM,KAAK,GAAoC,EAAE,CAAC;IAClD,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI,aAAa,GAAG,KAAK,CAAC;IAC1B,IAAI,cAAoC,CAAC;IAEzC,IAAI,aAAa,GAAG,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;IAC7F,IAAI,SAAS,EAAE,CAAC;QACf,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,eAAe,GAAG,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC;QAC9E,MAAM,YAAY,GAAG,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QACrE,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC,qCAAqC;YAC5D,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QAChG,CAAC;IACF,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,IAAA,gCAAiB,EAAC,KAAK,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE,mBAAmB,EAAE,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,gBAAgB,IAAI,EAAE,cAAc,EAAd,wBAAc,EAAE,qBAAqB,EAArB,+BAAqB,EAAE,EAAE,KAAK,CAAC,CAAC;IAC1N,IAAI,MAAM,EAAE,CAAC;QACZ,aAAa,KAAb,aAAa,GAAK,MAAM,CAAC,aAAa,EAAC;QACvC,cAAc,KAAd,cAAc,GAAK,MAAM,CAAC,cAAc,EAAC;QACzC,gBAAgB,KAAhB,gBAAgB,GAAK,MAAM,CAAC,gBAAgB,EAAC;QAC7C,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QACvC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC;IACF,CAAC;IAED,IAAI,SAAS,8BAAsB,EAAE,CAAC;QACrC,qDAAqD;QACrD,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QAChG,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;YACzC,MAAM,gBAAgB,GAAG,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;YACjG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACnC,KAAK,CAAC,IAAI,CAAC,IAAA,qCAAoB,EAC9B,eAAe,CAAC,cAAc,EAC9B,MAAM,EACN,OAAO,EACP,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,aAAa,EACrB,MAAM,CAAC,0BAA0B,CAAC,MAAM,CACxC,CAAC,CAAC;gBACH,MAAM,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACP,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,gBAAgB,CAAC,CAAC;gBACnH,IAAI,CAAC,YAAY,EAAE,CAAC;oBACnB,SAAS;gBACV,CAAC;gBACD,MAAM,aAAa,GAAG,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;gBAC1D,IAAI,aAAa,EAAE,CAAC;oBACnB,aAAa,CAAC,IAAI,GAAG,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;oBAC9D,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;gBAC7D,CAAC;YACF,CAAC;QACF,CAAC;QACD,cAAc,GAAG,IAAI,CAAC;QACtB,gBAAgB,GAAG,IAAI,CAAC;IACzB,CAAC;IACD,iFAAiF;SAC5E,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,cAAc,IAAI,CAAC,gBAAgB,IAAI,CAAC,aAAa,EAAE,CAAC;QAClF,IAAI,eAAe,CAAC,wBAAwB,EAAE,CAAC;YAC9C,cAAc,GAAG,IAAI,CAAC;YACtB,gBAAgB,GAAG,IAAI,CAAC;QACzB,CAAC;IACF,CAAC;IAED,IAAI,GAA2B,CAAC;IAChC,IAAI,mBAAmB,IAAI,CAAC,cAAc,IAAI,gBAAgB,CAAC,EAAE,CAAC;QACjE,GAAG,GAAG,MAAM,oBAAoB,CAAC,MAAM,EAAE,mBAAmB,CAAC,CAAC;IAC/D,CAAC;IAED,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC;AACzE,CAAC;AAED,SAAS,YAAY,CAAC,YAA2C,EAAE,OAA4B;IAC9F,IAAI,KAAK,GAAG,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjG,KAAK,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnG,KAAK,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,OAAO,CAAC,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvF,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACf,KAAK,IAAI,OAAO,YAAY,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxG,KAAK,IAAI,OAAO,YAAY,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7G,KAAK,IAAI,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,YAAY,CAAC,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjG,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;YAChB,OAAO,EAAE,GAAG,OAAO,EAAE,gBAAgB,EAAE,YAAY,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,YAAY,CAAC,iBAAiB,EAAE,CAAC;QAC3H,CAAC;IACF,CAAC;AACF,CAAC;AAED,SAAS,cAAc,CAAC,mBAAyC;IAChE,MAAM,GAAG,GAA2B,EAAE,CAAC;IACvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/E,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC/B,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QAClB,CAAC;IACF,CAAC;IACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1B,0BAA0B,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,OAAO,GAAG,CAAC;AACZ,CAAC;AAED,SAAS,oBAAoB,CAAC,SAA6B;IAC1D,QAAQ,SAAS,EAAE,CAAC;QACnB,KAAK,MAAM;YACV,2CAA8B;QAC/B,KAAK,KAAK;YACT,yCAA6B;QAC9B,KAAK,MAAM;YACV,iDAAoC;QACrC,KAAK,MAAM;YACV,2CAA8B;QAC/B,KAAK,QAAQ;YACZ,+CAAgC;QACjC;YACC,OAAO,SAAS,CAAC;IACnB,CAAC;AACF,CAAC;AAED,SAAgB,0BAA0B,CAAC,GAA2B,EAAE,GAAG,QAAkB;IAC5F,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAA0B,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACjE,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;QAChB,OAAO,GAAG,CAAC;IACZ,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,MAAM,YAAY,GAAG;QACpB,cAAc;QACd,yEAAyE;QACzE,cAAc;QACd,gBAAgB;KAChB,CAAC;IACF,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjC,OAAO;SACL,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACxB,OAAO,CAAC,MAAM,CAAC,EAAE;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC;gBACnB,MAAM;YACP,CAAC;QACF,CAAC;IACF,CAAC,CAAC,CAAC;AACL,CAAC", "file": "terminalSuggestMain.js", "sourceRoot": "../src/"}