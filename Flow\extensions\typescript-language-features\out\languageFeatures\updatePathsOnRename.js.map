{"version": 3, "sources": ["languageFeatures/updatePathsOnRename.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwRhG,4BAUC;AAhSD,2CAA6B;AAC7B,+CAAiC;AACjC,0EAA4D;AAC5D,8EAA2F;AAE3F,kEAAoD;AACpD,4DAAkF;AAClF,0CAAyC;AACzC,wDAAiD;AACjD,8CAA8C;AAE9C,wEAA8F;AAG9F,MAAM,2BAA2B,GAAG,iCAAiC,CAAC;AAEtE,KAAK,UAAU,WAAW,CAAC,QAAoB;IAC9C,IAAI,CAAC;QACJ,OAAO,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;IACtF,CAAC;IAAC,MAAM,CAAC;QACR,OAAO,KAAK,CAAC;IACd,CAAC;AACF,CAAC;AAgBD,MAAM,gCAAiC,SAAQ,oBAAU;IAKxD,YACkB,MAAgC,EAChC,wBAAkD,EAClD,QAA+C;QAEhE,KAAK,EAAE,CAAC;QAJS,WAAM,GAAN,MAAM,CAA0B;QAChC,6BAAwB,GAAxB,wBAAwB,CAA0B;QAClD,aAAQ,GAAR,QAAQ,CAAuC;QANhD,aAAQ,GAAG,IAAI,eAAO,CAAC,EAAE,CAAC,CAAC;QAC3B,oBAAe,GAAG,IAAI,GAAG,EAAgB,CAAC;QAS1D,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;YAC5D,KAAK,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACrD,IAAI,CAAC,WAAW,EAAE,CAAC;oBAClB,SAAS;gBACV,CAAC;gBAED,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBACrD,IAAI,CAAC,WAAW,EAAE,CAAC;oBAClB,SAAS;gBACV,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAiC,2BAA2B,CAAC,CAAC;gBACxF,IAAI,OAAO,uDAAyC,EAAE,CAAC;oBACtD,SAAS;gBACV,CAAC;gBAED,8CAA8C;gBAC9C,sEAAsE;gBACtE,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBAC1E,IAAI,CAAC,wBAAwB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,wBAAwB,CAAC,EAAE,CAAC;oBACtF,SAAS;gBACV,CAAC;gBAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,wBAAwB,EAAE,CAAC,CAAC;gBAEjG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE;oBAC1B,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;wBAC1B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;wBACxC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,sCAAsC,CAAC;qBAC5D,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;gBAC/B,CAAC,CAAC,CAAC;YACJ,CAAC;QACF,CAAC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,YAAY;QACzB,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACjD,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzC,MAAM,qBAAqB,GAAiB,EAAE,CAAC;YAE/C,KAAK,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,wBAAwB,EAAE,IAAI,KAAK,EAAE,CAAC;gBAC5F,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;gBAEnF,gCAAgC;gBAChC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBACpD,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBAEzD,IAAI,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,EAAE,CAAC;oBAClF,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpC,CAAC;YACF,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;gBAChB,IAAI,MAAM,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,EAAE,CAAC;oBAC7D,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;gBAClE,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,YAAmC;QACtE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACd,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAiC,2BAA2B,CAAC,CAAC;QACxF,QAAQ,OAAO,EAAE,CAAC;YACjB;gBACC,OAAO,IAAI,CAAC;YACb;gBACC,OAAO,KAAK,CAAC;YACd,0DAA2C;YAC3C;gBACC,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACvC,CAAC;IACF,CAAC;IAEO,gBAAgB,CAAC,QAAoB;QAC5C,OAAO,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAA,yDAAmC,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACjI,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,YAAmC;QAC3D,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAuB;YACtC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YAC1B,iBAAiB,EAAE,IAAI;SACvB,CAAC;QAEF,MAAM,UAAU,GAAuB;YACtC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;SAC3B,CAAC;QAEF,MAAM,UAAU,GAAuB;YACtC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;SAC9B,CAAC;QAEF,MAAM,SAAS,GAAuB;YACrC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;SAC7B,CAAC;QAEF,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,YAAY,CAAC,MAAM,KAAK,CAAC;YACxB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACnF,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,6CAA6C,EAAE,YAAY,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,EAAE;YAC5H,KAAK,EAAE,IAAI;SACX,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QAGlD,QAAQ,QAAQ,EAAE,CAAC;YAClB,KAAK,UAAU,CAAC,CAAC,CAAC;gBACjB,OAAO,IAAI,CAAC;YACb,CAAC;YACD,KAAK,UAAU,CAAC,CAAC,CAAC;gBACjB,OAAO,KAAK,CAAC;YACd,CAAC;YACD,KAAK,UAAU,CAAC,CAAC,CAAC;gBACjB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CACZ,2BAA2B,wDAE3B,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC,CAAC;gBACjE,OAAO,IAAI,CAAC;YACb,CAAC;YACD,KAAK,SAAS,CAAC,CAAC,CAAC;gBAChB,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CACZ,2BAA2B,sDAE3B,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,2BAA2B,CAAC,CAAC,CAAC;gBACjE,OAAO,KAAK,CAAC;YACd,CAAC;YACD,OAAO,CAAC,CAAC,CAAC;gBACT,OAAO,KAAK,CAAC;YACd,CAAC;QACF,CAAC;IACF,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,QAAoB;QACvD,IAAI,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC;YAC1C,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,MAAM,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,eAAe,CAAC,QAAQ,EAAE,sBAAsB,CAAC,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;YACtI,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/D,CAAC;IAEO,KAAK,CAAC,sBAAsB,CACnC,KAA2B,EAC3B,QAA6B,EAC7B,WAAmB,EACnB,WAAmB;QAEnB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,EAAE;YACvD,IAAI,CAAC,wBAAwB,CAAC,kCAAkC,CAAC,QAAQ,EAAE,uBAAQ,CAAC,CAAC;YACrF,MAAM,IAAI,GAA2C;gBACpD,WAAW;gBACX,WAAW;aACX,CAAC;YACF,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,uBAAuB,EAAE,IAAI,EAAE,uBAAQ,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAC3D,OAAO,KAAK,CAAC;QACd,CAAC;QAED,cAAc,CAAC,aAAa,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClF,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,YAAY,CAAC,OAA+B;QACnD,MAAM,MAAM,GAAG,IAAI,GAAG,EAA6B,CAAC;QAEpD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC9B,kDAAkD;YAClD,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,wBAAwB,CAAC,EAAE,MAAM,MAAM,IAAA,yDAAmC,EAAC,MAAM,CAAC,wBAAwB,CAAC,EAAE,CAAC;YAC5K,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACtB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YAC5B,CAAC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;IACxB,CAAC;IAEO,iBAAiB,CAAC,KAAa,EAAE,kBAAyC;QACjF,MAAM,iBAAiB,GAAG,EAAE,CAAC;QAE7B,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;QACtB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAEhG,IAAI,kBAAkB,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;YACnD,IAAI,kBAAkB,CAAC,MAAM,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;gBACzD,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC,CAAC,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACP,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,mCAAmC,EAAE,kBAAkB,CAAC,MAAM,GAAG,iBAAiB,CAAC,CAAC,CAAC;YAC/G,CAAC;QACF,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAEO,oBAAoB,CAAC,MAAqC,EAAE,YAAoB;QACvF,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,SAAS,EAAE,oBAAoB,EAAE,CAAC;YACrC,OAAO,MAAM,CAAC,mBAAmB,CAAC,eAAe,CAAC;QACnD,CAAC;QAED,IAAI,SAAS,EAAE,cAAc,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC;QAC7C,CAAC;QAED,OAAO,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;IAC1C,CAAC;CACD;AAED,SAAgB,QAAQ,CACvB,MAAgC,EAChC,wBAAkD,EAClD,OAA8C;IAE9C,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACxD,EAAE,GAAG,EAAE;QACP,OAAO,IAAI,gCAAgC,CAAC,MAAM,EAAE,wBAAwB,EAAE,OAAO,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "updatePathsOnRename.js", "sourceRoot": "../../src/"}