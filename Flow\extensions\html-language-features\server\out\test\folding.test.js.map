{"version": 3, "sources": ["test/folding.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,iBAAe;AACf,+CAAiC;AACjC,sDAAwD;AACxD,0DAAwE;AACxE,2EAAgE;AAChE,2CAA+C;AAQ/C,KAAK,UAAU,YAAY,CAAC,KAAe,EAAE,QAA+B,EAAE,OAAgB,EAAE,OAAgB;IAC/G,MAAM,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC,qBAAqB,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzF,MAAM,SAAS,GAAG;QACjB,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;KAC7C,CAAC;IACF,MAAM,aAAa,GAAG,IAAA,gCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,+CAAkB,CAAC,MAAM,EAAE,IAAA,sBAAa,GAAE,CAAC,CAAC;IAC/H,MAAM,MAAM,GAAG,MAAM,IAAA,8BAAgB,EAAC,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAE9E,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACxC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7E,CAAC;IACD,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;IAC1E,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,CAAC,CAAC,SAAiB,EAAE,OAAe,EAAE,IAAa;IAC3D,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACrC,CAAC;AAED,KAAK,CAAC,cAAc,EAAE,GAAG,EAAE;IAE1B,IAAI,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACtC,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,UAAU;YACf,KAAK,CAAA,gBAAgB;YACrB,KAAK,CAAA,GAAG;YACR,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,SAAS;SACd,CAAC;QACF,MAAM,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;QACvD,MAAM,KAAK,GAAG;YACb,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,aAAa;YACnB,MAAM,CAAA,gBAAgB;YACtB,MAAM,CAAA,aAAa;YACnB,MAAM,CAAA,MAAM;YACZ,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,yBAAyB;YAC/B,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,OAAO;YACb,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACjG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,KAAK,GAAG;YACb,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,aAAa;YACnB,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,OAAO;YACb,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAChD,MAAM,KAAK,GAAG;YACb,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,qBAAqB;YAC3B,MAAM,CAAA,gBAAgB;YACtB,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,kBAAkB;YACxB,MAAM,CAAA,wBAAwB;YAC9B,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QAC/B,MAAM,KAAK,GAAG;YACb,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,oBAAoB;YAC1B,MAAM,CAAA,kBAAkB;YACxB,MAAM,CAAA,KAAK;YACX,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAChD,MAAM,KAAK,GAAG;YACb,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,0BAA0B;YAChC,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,MAAM;YACZ,MAAM,CAAA,gBAAgB;YACtB,MAAM,CAAA,aAAa;YACnB,MAAM,CAAA,MAAM;YACZ,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,uBAAuB;YAC7B,MAAM,CAAA,sBAAsB;YAC5B,MAAM,CAAA,KAAK;YACX,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAClG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QACzC,MAAM,KAAK,GAAG;YACb,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,wBAAwB;YAC9B,MAAM,CAAA,kBAAkB;YACxB,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,oBAAoB;YAC1B,MAAM,CAAA,0BAA0B;YAChC,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC,CAAC,CAAC;IAGH,iEAAiE;IACjE,mBAAmB;IACnB,oBAAoB;IACpB,oBAAoB;IACpB,sBAAsB;IACtB,kBAAkB;IAClB,wBAAwB;IACxB,mBAAmB;IACnB,uBAAuB;IACvB,qBAAqB;IACrB,qBAAqB;IACrB,MAAM;IACN,+EAA+E;IAC/E,MAAM;IAEN,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;QAC7B,MAAM,KAAK,GAAG;YACb,MAAM,CAAA,OAAO;YACb,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,OAAO;YACb,MAAM,CAAA,IAAI;YACV,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,OAAO;YACb,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,IAAI;YACV,MAAM,CAAA,YAAY;YAClB,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,IAAI;YACV,MAAM,CAAA,YAAY;YAClB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,OAAO;YACb,MAAM,CAAA,IAAI;YACV,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,OAAO;YACb,MAAM,CAAA,IAAI;YACV,MAAM,CAAA,QAAQ;YACd,MAAM,CAAA,UAAU;YAChB,MAAM,CAAA,QAAQ;SACd,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QACnI,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAC1H,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAChH,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QACvG,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAC5F,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QACjF,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QACvE,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;QAC9D,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;AAEJ,CAAC,CAAC,CAAC", "file": "folding.test.js", "sourceRoot": "../../src/"}