{"version": 3, "sources": ["commands/commandManager.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AAQjC,MAAa,cAAc;IAA3B;QACkB,aAAQ,GAAG,IAAI,GAAG,EAA0E,CAAC;IA0B/G,CAAC;IAxBO,OAAO;QACb,KAAK,MAAM,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YACnD,YAAY,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IACvB,CAAC;IAEM,QAAQ,CAAoB,OAAU;QAC5C,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,KAAK,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,YAAY,EAAE,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;YAC7G,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACP,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;YACjC,KAAK,CAAC,QAAQ,IAAI,CAAC,CAAC;YACpB,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;gBACzB,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAClC,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CACD;AA3BD,wCA2BC", "file": "commandManager.js", "sourceRoot": "../../src/"}