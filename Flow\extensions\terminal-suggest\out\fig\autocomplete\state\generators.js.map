{"version": 3, "sources": ["fig/autocomplete/state/generators.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAEhG,yFAAgF;AAIhF,8CAA2C;AAE3C,yFAAgF;AAGzE,MAAM,oBAAoB,GAAG,CAAC,EACpC,QAAQ,GACW,EAAoB,EAAE,CAAC,CAAC;IAC3C,uBAAuB,EAAE,QAAQ,CAAC,GAAG,IAAI,EAAE;IAC3C,cAAc,EAAE,QAAQ,CAAC,eAAe,IAAI,EAAE;IAC9C,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB;IACnD,SAAS,EAAE,EAAE;CACb,CAAC,CAAC;AAPU,QAAA,oBAAoB,wBAO9B;AAEH,MAAM,mBAAmB,GAAG,CAAC,KAAwB,EAAoB,EAAE;IAC1E,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC;IACxC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,YAAY,CAAC;IAC3E,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,IAAI,EAAE,CAAC;IACrC,OAAO;QACN,GAAG,IAAA,4BAAoB,EAAC,KAAK,CAAC;QAC9B,WAAW,EAAE,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC;QAC5C,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QACtE,WAAW,EAAE,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC;QAC7C,UAAU;KACV,CAAC;AACH,CAAC,CAAC;AAEK,MAAM,oBAAoB,GAAG;AACnC,8CAA8C;AAC9C,KAAwB,EACxB,gBAAuC,EAGtC,EAAE;IACH,4BAA4B;IAC5B,mCAAmC;IACnC,6CAA6C;IAC7C,MAAM;IACN,mDAAmD;IACnD,qCAAqC;IACrC,qEAAqE;IACrE,0EAA0E;IAC1E,wBAAwB;IACxB,wEAAwE;IACxE,iCAAiC;IACjC,MAAM;IAEN,4CAA4C;IAC5C,4EAA4E;IAC5E,6CAA6C;IAC7C,uDAAuD;IACvD,wBAAwB;IACxB,qBAAqB;IACrB,QAAQ;IACR,yCAAyC;IACzC,6CAA6C;IAC7C,QAAQ;IACR,gCAAgC;IAChC,OAAO;IACP,IAAI;IAEJ,qEAAqE;IACrE,2DAA2D;IAC3D,6BAA6B;IAC7B,6BAA6B;IAC7B,8CAA8C;IAC9C,sBAAsB;IACtB,2EAA2E;IAC3E,UAAU;IACV,OAAO;IACP,KAAK;IACL,0BAA0B;IAC1B,IAAI;IACJ,MAAM,gBAAgB,GAAG,CAAC,YAA4B,EAAE,gBAAsC,EAAE,EAAE;QACjG,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC;QAC5C,IAAI,OAA8C,CAAC;QAEnD,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACxB,sCAAsC;YACtC,wDAAwD;YACxD,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACtC,CAAC;aACI,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAO,GAAG,IAAA,iDAAoB,EAC7B,SAAS,EACT,OAAO,EACP,SAAS,EAAE,qDAAqD;YAChE,gBAAgB,CAChB,CAAC;QACH,CAAC;aACI,CAAC;YACL,OAAO,GAAG,IAAA,iDAAoB,EAAC,SAAS,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;YACrE,2FAA2F;YAC3F,wDAAwD;YACxD,6CAA6C;YAC7C,4BAA4B;YAC5B,gFAAgF;YAChF,uFAAuF;YACvF,2BAA2B;YAC3B,uCAAuC;YACvC,kEAAkE;YAClE,kDAAkD;YAClD,+CAA+C;YAC/C,QAAQ;YACR,MAAM;YACN,wBAAwB;YACxB,SAAS;YACT,IAAI;QACL,CAAC;QACD,OAAO,EAAE,GAAG,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;IACpD,CAAC,CAAC;IAEF,MAAM,iBAAiB,GAAG,CACzB,YAAkC,EAClC,gBAAsC,EACnB,EAAE;QACrB,MAAM,EACL,YAAY,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,kBAAkB,EAAE,GACzE,GAAG,KAAK,CAAC;QACV,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;QAChD,MAAM,UAAU,GAAG,UAAU,EAAE,UAAU,IAAI,EAAE,CAAC;QAChD,MAAM,OAAO,GAAG,mBAAmB,CAAC,EAAE,GAAG,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;QAEhE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,SAAwB,EAAE,KAAa,EAAE,EAAE;YACjE,MAAM,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;YAC9B,MAAM,sBAAsB,GAAG,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5D,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,sBAAsB,IAAI,UAAU,KAAK,WAAW,EAAE,CAAC;gBAC3D,aAAa,GAAG,IAAI,CAAC;YACtB,CAAC;iBAAM,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAClC,gEAAgE;gBAChE,gCAAgC;gBAChC,oCAAoC;gBACpC,aAAa,GAAG,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACP,IAAI,SAA4C,CAAC;gBACjD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;oBACjC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACpB,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACpD,CAAC;qBAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE,CAAC;oBAC1C,SAAS,GAAG,OAAO,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACP,QAAQ,OAAO,CAAC,EAAE,EAAE,CAAC;wBACpB,KAAK,WAAW,CAAC,CAAC,CAAC;4BAClB,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACpB,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;4BAC3D,MAAM;wBACP,CAAC;wBACD,KAAK,OAAO,CAAC,CAAC,CAAC;4BACd,MAAM,OAAO,GACZ,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ;gCACjC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;gCAClB,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;4BACnB,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACpB,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;gCACjC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;4BACnC,MAAM;wBACP,CAAC;wBACD,KAAK,QAAQ,CAAC;wBACd,OAAO,CAAC,CAAC,CAAC;4BACT,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;4BAC9B,MAAM;wBACP,CAAC;oBACF,CAAC;gBACF,CAAC;gBACD,IAAI,CAAC;oBACJ,aAAa,GAAG,SAAS,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;gBAC3D,CAAC;gBAAC,OAAO,IAAI,EAAE,CAAC;oBACf,aAAa,GAAG,IAAI,CAAC;gBACtB,CAAC;YACF,CAAC;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;gBACpB,OAAO,sBAAsB,CAAC;YAC/B,CAAC;YAED,MAAM,MAAM,GAAG,sBAAsB,EAAE,MAAM,IAAI,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YAErE,MAAM,iBAAiB,GAAG,GAAG,EAAE,CAAC,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;YACnF,IAAI,UAAU,EAAE,QAAQ,EAAE,CAAC;gBAC1B,IAAA,aAAK,EACJ,OAAO,UAAU,CAAC,QAAQ,KAAK,QAAQ,IAAI,UAAU,CAAC,QAAQ,GAAG,CAAC;oBACjE,CAAC,CAAC,UAAU,CAAC,QAAQ;oBACrB,CAAC,CAAC,GAAG,CACN,CAAC,CAAC,kEAAkE;gBACrE,OAAO,cAAc,CAAC;YACvB,CAAC;YACD,OAAO,iBAAiB,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO,EAAE,iBAAiB,EAAE,CAAC;AAC9B,CAAC,CAAC;AArKW,QAAA,oBAAoB,wBAqK/B", "file": "generators.js", "sourceRoot": "../../../../src/"}