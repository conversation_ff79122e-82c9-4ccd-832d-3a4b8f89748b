{"version": 3, "sources": ["browser/htmlServerMain.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAEhG,2DAAyH;AACzH,8CAAgE;AAEhE,MAAM,aAAa,GAAG,IAAI,8BAAoB,CAAC,IAAI,CAAC,CAAC;AACrD,MAAM,aAAa,GAAG,IAAI,8BAAoB,CAAC,IAAI,CAAC,CAAC;AAErD,MAAM,UAAU,GAAG,IAAA,0BAAgB,EAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAElE,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AAC9D,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AAElE,MAAM,OAAO,GAAuB;IACnC,KAAK,EAAE;QACN,YAAY,CAAC,QAAkC,EAAE,GAAG,IAAW;YAC9D,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;YAChD,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;QAChD,CAAC;QACD,UAAU,CAAC,QAAkC,EAAE,EAAU,EAAE,GAAG,IAAW;YACxE,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;YACjD,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;QAChD,CAAC;KACD;CACD,CAAC;AAEF,IAAA,wBAAW,EAAC,UAAU,EAAE,OAAO,CAAC,CAAC", "file": "htmlServerMain.js", "sourceRoot": "../../src/"}