{"version": 3, "sources": ["task/tsconfigProvider.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AASjC,MAAa,gBAAgB;IACrB,KAAK,CAAC,sBAAsB,CAAC,KAA+B;QAClE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;YACxC,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;QAC5C,KAAK,MAAM,MAAM,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;YACzD,IAAI,IAAI,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE;oBAC1B,GAAG,EAAE,MAAM;oBACX,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,SAAS,EAAE,MAAM,CAAC,IAAI;oBACtB,eAAe,EAAE,IAAI;iBACrB,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;QACD,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC;IACzB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,KAA+B;QAC5D,OAAO,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,mBAAmB,EAAE,yBAAyB,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;IAC3G,CAAC;CACD;AAxBD,4CAwBC", "file": "tsconfigProvider.js", "sourceRoot": "../../src/"}