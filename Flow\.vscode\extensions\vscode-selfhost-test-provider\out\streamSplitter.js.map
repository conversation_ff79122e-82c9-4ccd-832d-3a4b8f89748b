{"version": 3, "sources": ["streamSplitter.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAEhG,oEAAoE;AAEpE,mCAAmC;AAEnC;;;;;GAKG;AACH,MAAa,cAAe,SAAQ,kBAAS;IAK5C,YAAY,QAAkC;QAC7C,KAAK,EAAE,CAAC;QACR,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;YACzB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACP,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACzC,CAAC;IACF,CAAC;IAEQ,UAAU,CAClB,KAAa,EACb,SAAiB,EACjB,QAAoD;QAEpD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACrB,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;gBAClB,MAAM;YACP,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;YAC9D,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACpF,QAAQ,EAAE,CAAC;IACZ,CAAC;IAEQ,MAAM,CAAC,QAAoD;QACnE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;QAED,QAAQ,EAAE,CAAC;IACZ,CAAC;CACD;AAhDD,wCAgDC", "file": "streamSplitter.js", "sourceRoot": "../src/"}