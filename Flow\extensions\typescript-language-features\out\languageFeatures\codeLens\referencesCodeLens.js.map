{"version": 3, "sources": ["languageFeatures/codeLens/referencesCodeLens.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIhG,4BAaC;AA5ID,+CAAiC;AAKjC,+EAAiE;AACjE,kDAAwD;AACxD,qEAAuD;AACvD,+DAAqF;AACrF,yEAA2H;AAC3H,iEAA4G;AAG5G,MAAa,oCAAqC,SAAQ,qDAA8B;IACvF,YACC,MAAgC,EACtB,eAAsD,EAC/C,QAA6B;QAE9C,KAAK,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAHrB,oBAAe,GAAf,eAAe,CAAuC;QAC/C,aAAQ,GAAR,QAAQ,CAAqB;QAG9C,IAAI,CAAC,SAAS,CACb,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YAC/C,IAAI,GAAG,CAAC,oBAAoB,CAAC,GAAG,QAAQ,CAAC,EAAE,wCAAwC,CAAC,EAAE,CAAC;gBACtF,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;YAC3B,CAAC;QACF,CAAC,CAAC,CACF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,eAAe,CAAC,QAA4B,EAAE,KAA+B;QACzF,MAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACpG,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE;YACrE,WAAW,EAAE,IAAI;YACjB,eAAe,EAAE,wBAAe,CAAC,QAAQ;YACzC,sBAAsB,EAAE,QAAQ,CAAC,QAAQ;SACzC,CAAC,CAAC;QACH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,IAAI,KAAK,WAAW;gBAC/C,CAAC,CAAC,qDAA8B,CAAC,gBAAgB;gBACjD,CAAC,CAAC,qDAA8B,CAAC,YAAY,CAAC;YAC/C,OAAO,QAAQ,CAAC;QACjB,CAAC;QAED,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI;aAClC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC;aAC5C,GAAG,CAAC,SAAS,CAAC,EAAE,CAChB,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;QAE3F,QAAQ,CAAC,OAAO,GAAG;YAClB,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;YACvC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAAE;YAC/D,SAAS,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC;SAC/D,CAAC;QACF,OAAO,QAAQ,CAAC;IACjB,CAAC;IAEO,gBAAgB,CAAC,SAAyC;QACjE,OAAO,SAAS,CAAC,MAAM,KAAK,CAAC;YAC5B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC;YAC9B,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAES,aAAa,CACtB,QAA6B,EAC7B,IAA0B,EAC1B,MAAwC;QAExC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YAChD,OAAO,IAAA,qCAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC;QAED,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC3B,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAG,CAAU,uCAAuC,CAAC,CAAC;gBACrI,IAAI,kBAAkB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACzC,OAAO,IAAA,qCAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;YACF,CAAC;YACD,cAAc;YAEd,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACvB,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;YACrB,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ;gBACxB,8CAA8C;gBAC9C,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;oBAC3C,OAAO,IAAA,qCAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;gBACD,MAAM;YAEP,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK;gBACrB,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAC7B,MAAM;gBACP,CAAC;gBACD,OAAO,IAAA,qCAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAEvC,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YAC3B,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YACtB,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;gBACpB,OAAO,IAAA,qCAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAEvC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACnC,KAAK,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC;YACnC,KAAK,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC;YAC3C,KAAK,MAAM,CAAC,IAAI,CAAC,cAAc;gBAC9B,iDAAiD;gBACjD,uDAAuD;gBACvD,IAAI,MAAM;oBACT,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAC7H,CAAC;oBACF,OAAO,SAAS,CAAC;gBAClB,CAAC;gBAED,6DAA6D;gBAC7D,QAAQ,MAAM,EAAE,IAAI,EAAE,CAAC;oBACtB,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;oBACvB,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBAC3B,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;wBACpB,OAAO,IAAA,qCAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACxC,CAAC;gBACD,MAAM;QACR,CAAC;QAED,OAAO,SAAS,CAAC;IAClB,CAAC;CACD;AAhHD,oFAgHC;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,QAA6B,EAC7B,MAAgC,EAChC,cAAqD;IAErD,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,kDAA0B,EAAC,QAAQ,CAAC,EAAE,EAAE,4BAA4B,CAAC;QACrE,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACxD,EAAE,GAAG,EAAE;QACP,OAAO,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,EACjE,IAAI,oCAAoC,CAAC,MAAM,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "referencesCodeLens.js", "sourceRoot": "../../../src/"}