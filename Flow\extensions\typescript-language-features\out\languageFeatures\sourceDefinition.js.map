{"version": 3, "sources": ["languageFeatures/sourceDefinition.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EhG,4BAWC;AArFD,+CAAiC;AAEjC,8DAAuE;AACvE,yCAAsC;AACtC,kEAAoD;AAIpD,MAAM,uBAAuB;IAO5B,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;QAHlC,OAAE,GAAG,iCAAiC,CAAC;IAInD,CAAC;IAEE,KAAK,CAAC,OAAO;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,uBAAuB,CAAC,UAAU,CAAC,EAAE,CAAC;YACnE,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,2DAA2D,CAAC,CAAC,CAAC;YAC3G,OAAO;QACR,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE,CAAC;YACnB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uDAAuD,CAAC,CAAC,CAAC;YACvG,OAAO;QACR,CAAC;QAED,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC;QAC3C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACnE,IAAI,CAAC,IAAA,qCAAuB,EAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,wDAAwD,CAAC,CAAC,CAAC;YACxG,OAAO;QACR,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE,CAAC;YACtB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,oDAAoD,CAAC,CAAC,CAAC;YACpG,OAAO;QACR,CAAC;QAED,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;YAChC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,MAAM;YACxC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,4BAA4B,CAAC;SAClD,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,EAAE;YAE7B,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC;YAC/C,MAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;YAC1F,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YAChF,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACnD,MAAM,SAAS,GAAsB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAClE,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;gBAE1F,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;oBACtB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC5B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;4BACnE,QAAQ,EAAE,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE;yBAC3F,CAAC,CAAC,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACP,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,8BAA8B,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;oBAC/F,CAAC;oBACD,OAAO;gBACR,CAAC;YACF,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;IACJ,CAAC;;AA5DsB,+BAAO,GAAG,4BAAH,AAA+B,CAAC;AACvC,kCAAU,GAAG,SAAG,CAAC,IAAP,AAAW,CAAC;AA+D9C,SAAgB,QAAQ,CACvB,MAAgC,EAChC,cAA8B;IAE9B,SAAS,aAAa;QACrB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,uBAAuB,CAAC,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1I,CAAC;IACD,aAAa,EAAE,CAAC;IAEhB,cAAc,CAAC,QAAQ,CAAC,IAAI,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7D,OAAO,MAAM,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC,CAAC;AACxD,CAAC", "file": "sourceDefinition.js", "sourceRoot": "../../src/"}