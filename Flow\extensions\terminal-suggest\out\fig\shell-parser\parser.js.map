{"version": 3, "sources": ["fig/shell-parser/parser.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAEhG,yCAAyC;AACzC,gCAAgC;AAChC,sFAAsF;AACtF,kCAAkC;AAClC,uCAAuC;AACvC,EAAE;AACF,uBAAuB;AACvB,yCAAyC;AACzC,qDAAqD;AACrD,yCAAyC;AACzC,EAAE;AACF,cAAc;AACd,uCAAuC;AACvC,0BAA0B;AAC1B,gCAAgC;AAChC,gCAAgC;AAChC,+BAA+B;AAC/B,gCAAgC;AAChC,eAAe;AACf,uBAAuB;AACvB,EAAE;AACF,6DAA6D;AAC7D,uBAAuB;AAEvB,IAAY,QA8BX;AA9BD,WAAY,QAAQ;IACnB,+BAAmB,CAAA;IAEnB,8CAAkC,CAAA;IAClC,qCAAyB,CAAA;IACzB,0CAA8B,CAAA;IAC9B,mCAAuB,CAAA;IAEvB,oDAAwC,CAAA;IACxC,iCAAqB,CAAA;IACrB,+BAAmB,CAAA;IACnB,iCAAqB,CAAA;IACrB,yBAAa,CAAA;IAEb,8BAA8B;IAC9B,wDAA4C,CAAA;IAE5C,sBAAsB;IACtB,2CAA+B,CAAA;IAC/B,yBAAa,CAAA;IACb,6BAAiB,CAAA;IACjB,mCAAuB,CAAA;IACvB,wDAA4C,CAAA;IAE5C,aAAa;IACb,oCAAwB,CAAA;IACxB,yCAA6B,CAAA;IAC7B,gDAAoC,CAAA;IACpC,kDAAsC,CAAA;IACtC,wDAA4C,CAAA;AAC7C,CAAC,EA9BW,QAAQ,wBAAR,QAAQ,QA8BnB;AAsDD,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAU,CAAC;AAInE,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,KAAa,EAAmB,EAAE;IACrE,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QACjC,MAAM,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QACvC,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAyB,CAAC;YACnD,CAAC,CAAE,EAAe;YAClB,CAAC,CAAE,CAAc,CAAC;IACpB,CAAC;IACD,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,YAAY,GAAG,CAAC,IAAc,EAAU,EAAE;IAC/C,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IAChD,IAAI,IAAI,KAAK,QAAQ,CAAC,aAAa,EAAE,CAAC;QACrC,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED,MAAM,oBAAoB,GAAoD;QAC7E,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QAC7B,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAClC,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC;KACrC,CAAC;IACF,MAAM,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAE7D,MAAM,UAAU,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAEnD,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3E,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzB,MAAM,YAAY,GAAG,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC;QAC1D,MAAM,cAAc,GACnB,CAAC,KAAK,IAAI;YACV,IAAI,KAAK,QAAQ,CAAC,MAAM;YACxB,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,YAAY,IAAI,cAAc,EAAE,CAAC;YACpC,CAAC,IAAI,CAAC,CAAC;QACR,CAAC;QAED,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAClB,GAAW,EACX,OAAmB,EACf,EAAE;IACN,mEAAmE;IACnE,MAAM,IAAI,GAAG;QACZ,UAAU,EAAE,CAAC;QACb,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,QAAQ,EAAE,GAAG,CAAC,MAAM;QACpB,IAAI,EAAE,EAAE;QACR,SAAS,EAAE,EAAE;QACb,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,EAAE;QACZ,GAAG,OAAO;KACO,CAAC;IACnB,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACvD,MAAM,SAAS,GAAG,YAAY,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAClD,OAAO,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AACrC,CAAC,CAAC;AAEK,MAAM,cAAc,GAAG,CAC7B,GAAW,EACX,UAAkB,EAClB,IAAY,EACD,EAAE,CACb,UAAU,CAAC,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAL9D,QAAA,cAAc,kBAKgD;AAE3E,MAAM,aAAa,GAAG,CAAC,GAAW,EAAE,KAAa,EAAE,EAAE;IACpD,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAChD,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;QACtB,OAAO,CAAC,CAAC,CAAC;IACX,CAAC;IACD,OAAO,KAAK,GAAG,SAAS,CAAC;AAC1B,CAAC,CAAC;AAEF,+CAA+C;AAC/C,MAAM,oBAAoB,GAAG,CAC5B,GAAW,EACX,KAAa,EACb,aAAuB,EAIhB,EAAE;IACT,MAAM,IAAI,GAAgD;QACzD,UAAU,EAAE,KAAK;QACjB,IAAI,EAAE,QAAQ,CAAC,eAAe;KAC9B,CAAC;IACF,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACzE,OAAO,UAAU,CAAsC,GAAG,EAAE;YAC3D,GAAG,IAAI;YACP,IAAI,EAAE,QAAQ,CAAC,gBAAgB;YAC/B,QAAQ,EAAE,KAAK,GAAG,CAAC;SACnB,CAAC,CAAC;IACJ,CAAC;IACD,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,aAAa,CAAC,CAAC;IACvE,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;IAClB,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,IAAI,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7C,kCAAkC;YAClC,OAAO,CAAC,KAAK,KAAK,GAAG,CAAC;gBACrB,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,UAAU,CAAqC,GAAG,EAAE;oBACrD,GAAG,IAAI;oBACP,QAAQ,EAAE,CAAC;iBACX,CAAC,CAAC;QACL,CAAC;IACF,CAAC;IACD,OAAO,UAAU,CAAqC,GAAG,EAAE;QAC1D,GAAG,IAAI;QACP,QAAQ,EAAE,CAAC;KACX,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,6CAA6C;AAC7C,SAAS,wBAAwB,CAChC,GAAW,EACX,UAAkB,EAClB,YAAoB;IAEpB,MAAM,KAAK,GACV,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC;IAClE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,eAAe,CAChE,GAAG,EACH,KAAK,EACL,YAAY,CACZ,CAAC;IACF,MAAM,UAAU,GAAG,eAAe,KAAK,CAAC,CAAC,CAAC;IAC1C,OAAO,UAAU,CAAyC,GAAG,EAAE;QAC9D,UAAU;QACV,IAAI,EAAE,QAAQ,CAAC,mBAAmB;QAClC,QAAQ,EAAE,UAAU,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;QAC7C,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM;QACvD,QAAQ;KACR,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,WAAW,GAAG,YAAY,CAAkB,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC7E,MAAM,cAAc,GAAG,YAAY,CAClC,QAAQ,CAAC,SAAS,EAClB,IAAI,EACJ,IAAI,CACJ,CAAC;AACF,MAAM,cAAc,GAAG,YAAY,CAClC,QAAQ,CAAC,SAAS,EAClB,IAAI,EACJ,GAAG,CACH,CAAC;AACF,MAAM,gBAAgB,GAAG,YAAY,CACpC,QAAQ,CAAC,WAAW,EACpB,KAAK,EACL,IAAI,CACJ,CAAC;AACF,MAAM,wBAAwB,GAAG,YAAY,CAC5C,QAAQ,CAAC,mBAAmB,EAC5B,KAAK,EACL,IAAI,CACJ,CAAC;AAEF,SAAS,YAAY,CACpB,GAAW,EACX,KAAa,EACb,QAAiB,EACjB,WAAqB;IAErB,MAAM,SAAS,GAAG;QACjB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;QACjB,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;QACrB,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;KACrB,CAAC;IACF,QAAQ,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;QACtB,KAAK,GAAG;YACP,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC1B,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG;oBAC1B,CAAC,CAAC,wBAAwB,CAAC,GAAG,EAAE,KAAK,CAAC;oBACtC,CAAC,CAAC,wBAAwB,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC1B,OAAO,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACnC,CAAC;YACD,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBACxC,OAAO,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACrC,CAAC;YACD,OAAO,oBAAoB,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QACtD,KAAK,GAAG;YACP,OAAO,wBAAwB,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAClD,KAAK,IAAI;YACR,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrD,KAAK,GAAG;YACP,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAClD;YACC,OAAO,IAAI,CAAC;IACd,CAAC;AACF,CAAC;AAED,SAAS,YAAY,CACpB,IAAO,EACP,UAAkB,EAClB,QAAgB;IAEhB,MAAM,eAAe,GACpB,IAAI,KAAK,QAAQ,CAAC,SAAS,IAAI,IAAI,KAAK,QAAQ,CAAC,MAAM,CAAC;IACzD,MAAM,QAAQ,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,CAAC;IAC1C,OAAO,CAAC,GAAW,EAAE,UAAkB,EAAe,EAAE;QACvD,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YACrE,MAAM,KAAK,GAAG,eAAe;gBAC5B,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC;YACR,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;gBACpB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrB,CAAC,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;YACxB,CAAC;iBAAM,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAClE,CAAC,IAAI,CAAC,CAAC;YACR,CAAC;iBAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC3D,OAAO,UAAU,CAAc,GAAG,EAAE;oBACnC,UAAU;oBACV,IAAI;oBACJ,QAAQ;oBACR,QAAQ,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM;iBAC7B,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;QACD,OAAO,UAAU,CAAc,GAAG,EAAE;YACnC,UAAU;YACV,IAAI;YACJ,QAAQ;YACR,QAAQ,EAAE,KAAK;SACf,CAAC,CAAC;IACJ,CAAC,CAAC;AACH,CAAC;AAED,SAAS,eAAe,CACvB,GAAW,EACX,KAAa,EACb,YAAoB,EACpB,aAAa,GAAG,KAAK;IAKrB,MAAM,UAAU,GAAG,EAAE,CAAC;IAEtB,IAAI,CAAC,GAAG,KAAK,CAAC;IACd,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QACvB,sEAAsE;QACtE,IAAI,SAAS,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;QAE1E,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,UAAU,GAAG,OAAO,KAAK,CAAC,CAAC,CAAC;QAClC,IAAI,CAAC,aAAa,IAAI,CAAC,UAAU,IAAI,YAAY,KAAK,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3E,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3B,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,OAAO,EAAE,CAAC;QACjD,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YAChB,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3B,MAAM;QACP,CAAC;QAED,MAAM,EAAE,GAAG,CAAC,UAAU,IAAI,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACtD,IAAI,EAAE,EAAE,CAAC;YACR,gCAAgC;YAChC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,MAAM,CAAC;YACxB,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACxC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3B,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,YAAY,EAAE,CAAC;gBAChE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,CAAC;YACnD,CAAC;QACF,CAAC;aAAM,CAAC;YACP,iDAAiD;YACjD,yCAAyC;YACzC,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC3B,GAAG,SAAS;gBACZ,QAAQ,EACP,SAAS,CAAC,IAAI,KAAK,QAAQ,CAAC,cAAc;oBACzC,CAAC,CAAC,SAAS,CAAC,QAAQ;oBACpB,CAAC,CAAC,KAAK;aACT,CAAC,CAAC;YACH,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3B,CAAC,GAAG,OAAO,CAAC;QACb,CAAC;IACF,CAAC;IACD,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC;AAED,MAAM,+BAA+B,GAAG,CACvC,GAAW,EACX,UAAkB,EAClB,YAAoB,EAC4B,EAAE;IAClD,MAAM,QAAQ,GAAkB,EAAE,CAAC;IAEnC,IAAI,gBAAgB,GAAkB,EAAE,CAAC;IACzC,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC;IAEnB,MAAM,OAAO,GAAG,CAAC,QAAgB,EAAE,EAAE;QACpC,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,GAAG,UAAU,CAA0B,GAAG,EAAE;gBACrD,UAAU,EAAE,SAAS;gBACrB,QAAQ;aACR,CAAC,CAAC;YACH,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QACD,SAAS,GAAG,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,QAAgB,EAAE,EAAE;QACxC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC;QAClC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAChE,QAAQ,GAAG,UAAU,CAAmC,GAAG,EAAE;gBAC5D,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,UAAU;gBAC1C,IAAI,EAAE,QAAQ,CAAC,aAAa;gBAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;gBAC5B,QAAQ,EAAE,gBAAgB;aAC1B,CAAC,CAAC;QACJ,CAAC;QACD,IAAI,QAAQ,EAAE,CAAC;YACd,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QACD,gBAAgB,GAAG,EAAE,CAAC;IACvB,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC1D,IAAI,YAAY,EAAE,CAAC;QAClB,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAChC,CAAC;IAED,IAAI,CAAC,GAAG,UAAU,CAAC;IACnB,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,EAAE,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACjC,IAAI,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK,YAAY,EAAE,CAAC;YACvC,iDAAiD;YACjD,MAAM;QACP,CAAC;QACD,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;QAC3D,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,CAAC,CAAC;YACX,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC,GAAG,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YACpC,WAAW,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBAChB,CAAC,IAAI,CAAC,CAAC;YACR,CAAC;YACD,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;gBACtB,SAAS,GAAG,CAAC,CAAC;YACf,CAAC;QACF,CAAC;IACF,CAAC;IAED,WAAW,CAAC,CAAC,CAAC,CAAC;IAEf,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AAClC,CAAC,CAAC;AAEF,SAAS,YAAY,CACpB,GAAW,EACX,GAAW,EACX,YAAoB;IAEpB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1D,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,+BAA+B,CAC7D,GAAG,EACH,UAAU,EACV,YAAY,CACZ,CAAC;IAEF,OAAO,UAAU,CAA6B,GAAG,EAAE;QAClD,UAAU;QACV,IAAI,EAAE,QAAQ,CAAC,OAAO;QACtB,QAAQ,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC;QAC7B,kCAAkC;QAClC,QAAQ,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM;QACrD,QAAQ;KACR,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,mBAAmB,GAAG,CAC3B,GAAW,EACX,UAAkB,EACD,EAAE;IACnB,MAAM,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACjD,MAAM,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;IAClE,MAAM,sBAAsB,GAC3B,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;IAClD,MAAM,uBAAuB,GAAG,GAAG;SACjC,KAAK,CAAC,UAAU,EAAE,sBAAsB,CAAC;SACzC,OAAO,CAAC,GAAG,CAAC,CAAC;IACf,IAAI,QAAyD,CAAC;IAE9D,MAAM,YAAY,GAAG,UAAU,CAAkC,GAAG,EAAE;QACrE,IAAI,EAAE,QAAQ,CAAC,YAAY;QAC3B,UAAU;QACV,QAAQ,EACP,uBAAuB,KAAK,CAAC,CAAC;YAC7B,CAAC,CAAC,uBAAuB;YACzB,CAAC,CAAC,sBAAsB;KAC1B,CAAC,CAAC;IAEH,IAAI,uBAAuB,KAAK,CAAC,CAAC,EAAE,CAAC;QACpC,MAAM,KAAK,GAAG,UAAU,CAA0B,GAAG,EAAE;YACtD,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,UAAU,EAAE,uBAAuB,GAAG,CAAC;YACvC,QAAQ,EAAE,sBAAsB,GAAG,CAAC;SACpC,CAAC,CAAC;QACH,QAAQ,GAAG,UAAU,CAAgB,GAAG,EAAE;YACzC,IAAI,EAAE,QAAQ,CAAC,SAAS;YACxB,IAAI,EAAE,YAAY;YAClB,UAAU;YACV,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,CAAC;YAC5B,QAAQ,EAAE,CAAC,KAAK,CAAC;SACjB,CAAC,CAAC;IACJ,CAAC;SAAM,CAAC;QACP,QAAQ,GAAG,YAAY,CAAC;IACzB,CAAC;IAED,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,+BAA+B,CAC7D,GAAG,EACH,WAAW,GAAG,CAAC,EACf,GAAG,CACH,CAAC;IACF,OAAO,UAAU,CAAiB,GAAG,EAAE;QACtC,IAAI,EAAE,QAAQ;QACd,UAAU;QACV,QAAQ;QACR,IAAI,EAAE,QAAQ,CAAC,UAAU;QACzB,QAAQ;QACR,QAAQ;QACR,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ;KAChD,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,GAAW,EAAE,KAAa,EAAoB,EAAE;IACzE,MAAM,SAAS,GAAqB,EAAE,CAAC;IACvC,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,OAAO,eAAe,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;QACrC,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAC3D,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC;YACvD,MAAM,cAAc,GAAG,mBAAmB,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;YAChE,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/B,eAAe,GAAG,cAAc,CAAC,QAAQ,CAAC;QAC3C,CAAC;aAAM,CAAC;YACP,OAAO,SAAS,CAAC;QAClB,CAAC;IACF,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,oCAAoC,GAAG,CAC5C,GAAW,EACX,UAAkB,EAClB,YAAoB,EAC8B,EAAE;IACpD,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACtD,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAG,aAAa,CAC7B,GAAG,EACH,aAAa,CAAC,GAAG,EAAE,cAAc,CAAC,QAAQ,CAAC,CAC3C,CAAC;QACF,IAAI,OAA+C,CAAC;QACpD,IACC,CAAC,QAAQ;YACT,cAAc,CAAC,QAAQ;YACvB,cAAc,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EACrC,CAAC;YACF,OAAO,GAAG,YAAY,CAAC,GAAG,EAAE,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QACpE,CAAC;QACD,uEAAuE;QACvE,OAAO,UAAU,CAAqB,GAAG,EAAE;YAC1C,IAAI,EAAE,QAAQ,CAAC,cAAc;YAC7B,UAAU;YACV,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,QAAQ;YAC9D,UAAU,EAAE,CAAC,CAAC,OAAO;YACrB,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW;SAC3D,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,YAAY,CAAC,GAAG,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;AACpD,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CACxB,GAAW,EACX,GAAa,EACb,GAAa,EACb,IAAc,EACH,EAAE,CACb,UAAU,CAAC,GAAG,EAAE;IACf,IAAI;IACJ,UAAU,EAAE,GAAG,CAAC,UAAU;IAC1B,QAAQ,EAAE,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;IACjE,QAAQ,EAAE,GAAG,CAAC,QAAQ;IACtB,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ;CACtC,CAAC,CAAC;AAEJ,SAAS,cAAc,CACtB,GAAW,EACX,KAAa,EACb,YAAoB;IAEpB,IAAI,CAAC,GAAG,aAAa,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,SAAS,GAAG,IAAI,CAAC;IACrB,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxC,uCAAuC;QACvC,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;QACzC,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAEvC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,eAAe,CAChE,GAAG,EACH,CAAC,GAAG,CAAC,EACL,OAAO,EACP,UAAU,CACV,CAAC;QACF,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QACxC,MAAM,UAAU,GAAG,eAAe,KAAK,CAAC,CAAC,CAAC;QAC1C,IAAI,QAAQ,GAAG,eAAe,GAAG,CAAC,CAAC;QACnC,IAAI,CAAC,UAAU,EAAE,CAAC;YACjB,QAAQ,GAAG,WAAW;gBACrB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ;gBACxC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QACf,CAAC;QACD,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC3B,UAAU,EAAE,CAAC;YACb,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ;YACjE,QAAQ;YACR,QAAQ,EAAE,UAAU,IAAI,WAAW;YACnC,QAAQ;SACR,CAAC,CAAC;IACJ,CAAC;SAAM,CAAC;QACP,yEAAyE;QACzE,SAAS,GAAG,oCAAoC,CAAC,GAAG,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;IACxE,CAAC;IAED,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC;IACvB,MAAM,OAAO,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IACtC,MAAM,EAAE,GAAG,OAAO,KAAK,CAAC,CAAC,IAAI,aAAa,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACzD,IACC,CAAC,EAAE;QACH,EAAE,KAAK,GAAG;QACV,EAAE,KAAK,GAAG;QACV,EAAE,KAAK,IAAI;QACX,CAAC,OAAO,KAAK,CAAC,CAAC,IAAI,YAAY,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,EACvE,CAAC;QACF,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,8DAA8D;IAC9D,MAAM,kBAAkB,GAAG,cAAc,CACxC,GAAG,EACH,OAAO,GAAG,EAAE,CAAC,MAAM,EACnB,YAAY,CACZ,CAAC;IACF,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;QAChC,OAAO,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE,kBAAkB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5E,CAAC;IAED,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;QAC/B,IAAI,kBAAkB,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC/C,MAAM,CAAC,aAAa,EAAE,GAAG,aAAa,CAAC,GAAG,kBAAkB,CAAC,QAAQ,CAAC;YACtE,MAAM,aAAa,GAAG,gBAAgB,CACrC,GAAG,EACH,SAAS,EACT,aAAa,EACb,QAAQ,CAAC,QAAQ,CACjB,CAAC;YACF,OAAO,UAAU,CAAC,GAAG,EAAE;gBACtB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,QAAQ,EAAE,CAAC,aAAa,EAAE,GAAG,aAAa,CAAC;gBAC3C,QAAQ,EAAE,kBAAkB,CAAC,QAAQ;gBACrC,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,kBAAkB,CAAC,QAAQ;aAC/D,CAAC,CAAC;QACJ,CAAC;QACD,OAAO,gBAAgB,CACtB,GAAG,EACH,SAAS,EACT,kBAAkB,EAClB,QAAQ,CAAC,QAAQ,CACjB,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AAClB,CAAC;AAEM,MAAM,SAAS,GAAG,CAAC,IAAc,EAAE,EAAE;IAC3C,MAAM,WAAW,GAAG,CAAC,IAAc,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE;QACjD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,QAAQ,GAAG,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,MAAM,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;QAC9F,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ;aAChC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;aAC7C,IAAI,CAAC,IAAI,CAAC,CAAC;QACb,IAAI,YAAY,EAAE,CAAC;YAClB,QAAQ,IAAI,KAAK,YAAY,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACpB,QAAQ,IAAI,KAAK,MAAM,YAAY,CAAC;QACrC,CAAC;QACD,OAAO,QAAQ,CAAC;IACjB,CAAC,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC;AAhBW,QAAA,SAAS,aAgBpB;AAEK,MAAM,KAAK,GAAG,CAAC,GAAW,EAAY,EAAE,CAC9C,UAAU,CAA6B,GAAG,EAAE;IAC3C,UAAU,EAAE,CAAC;IACb,IAAI,EAAE,QAAQ,CAAC,OAAO;IACtB,QAAQ,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,UAAU;CAChD,CAAC,CAAC;AALS,QAAA,KAAK,SAKd", "file": "parser.js", "sourceRoot": "../../../src/"}