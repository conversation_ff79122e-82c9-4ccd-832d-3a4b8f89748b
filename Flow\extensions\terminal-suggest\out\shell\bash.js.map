{"version": 3, "sources": ["shell/bash.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhG,wCAKC;AAOD,kCA6CC;AAED,sDASC;AAzED,+CAAiC;AAGjC,qCAAwD;AAEjD,KAAK,UAAU,cAAc,CAAC,OAAsC,EAAE,gBAA8B;IAC1G,OAAO;QACN,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC;QAC5B,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE,YAAY,EAAE,gBAAgB,CAAC;KAC7D,CAAC;AACH,CAAC;AAED,KAAK,UAAU,UAAU,CAAC,OAAsC;IAC/D,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClF,OAAO,IAAA,yBAAgB,EAAC,MAAM,EAAE,IAAI,EAAE,sDAAsD,EAAE,OAAO,CAAC,CAAC;AACxG,CAAC;AAEM,KAAK,UAAU,WAAW,CAChC,OAAsC,EACtC,WAAmB,EACnB,gBAA8B;IAE9B,MAAM,aAAa,GAAG,MAAM,IAAA,mBAAU,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC;IAC7D,MAAM,MAAM,GAAG,CAAC,GAAW,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACnE,MAAM,QAAQ,GAAa,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACpE,MAAM,WAAW,GAA0B,EAAE,CAAC;IAC9C,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;QACnC,WAAW,CAAC,IAAI,CAAC;YAChB,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,oCAAoC;YAC5C,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM;SAC9C,CAAC,CAAC;IACJ,CAAC;IAED,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC5B,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACJ,MAAM,UAAU,GAAG,CAAC,MAAM,IAAA,mBAAU,EAAC,QAAQ,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;gBACtE,MAAM,SAAS,GAAG,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC1C,8CAA8C;gBAC9C,MAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChF,MAAM,IAAI,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;gBAClE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,qBAAqB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;gBAC9F,WAAW,CAAC,IAAI,CAAC;oBAChB,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,WAAW,EAAE;oBAClC,MAAM;oBACN,aAAa,EAAE,IAAI,MAAM,CAAC,cAAc,CAAC,aAAa,CAAC;oBACvD,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM;iBAC9C,CAAC,CAAC;YAEJ,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,gBAAgB;gBAChB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;gBAC3C,WAAW,CAAC,IAAI,CAAC;oBAChB,KAAK,EAAE,GAAG;oBACV,IAAI,EAAE,MAAM,CAAC,0BAA0B,CAAC,MAAM;iBAC9C,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;IACF,CAAC;IAED,OAAO,WAAW,CAAC;AACpB,CAAC;AAED,SAAgB,qBAAqB,CAAC,WAAoB,EAAE,IAAa;IACxE,IAAI,MAAM,EAAE,aAAa,GAAG,EAAE,CAAC;IAC/B,MAAM,aAAa,GAAG,CAAC,IAAY,EAAU,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IAC1E,IAAI,WAAW,EAAE,CAAC;QACjB,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;QACzC,MAAM,GAAG,IAAI,CAAC;QACd,aAAa,GAAG,WAAW,CAAC;IAC7B,CAAC;IACD,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,CAAC;AAC/C,CAAC", "file": "bash.js", "sourceRoot": "../../src/"}