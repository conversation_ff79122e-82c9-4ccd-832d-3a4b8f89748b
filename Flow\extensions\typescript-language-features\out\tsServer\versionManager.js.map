{"version": 3, "sources": ["tsServer/versionManager.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AAEjC,0CAA8C;AAC9C,8CAA8C;AAI9C,MAAM,0BAA0B,GAAG,6BAA6B,CAAC;AACjE,MAAM,qCAAqC,GAAG,wCAAwC,CAAC;AAMvF,MAAa,wBAAyB,SAAQ,oBAAU;IAIvD,YACS,aAA6C,EACpC,eAA2C,EAC3C,cAA8B;QAE/C,KAAK,EAAE,CAAC;QAJA,kBAAa,GAAb,aAAa,CAAgC;QACpC,oBAAe,GAAf,eAAe,CAA4B;QAC3C,mBAAc,GAAd,cAAc,CAAgB;QA6B/B,yBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC,CAAC;QACxE,wBAAmB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QA1BrE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QAE3D,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAClC,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;gBAChC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;gBACvD,IAAI,YAAY,EAAE,CAAC;oBAClB,IAAI,CAAC,eAAe,GAAG,YAAY,CAAC;gBACrC,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,GAAG,EAAE;oBACrE,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;wBACvC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;oBAC7D,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC;YACL,CAAC;QACF,CAAC;QAED,IAAI,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,EAAE,CAAC;YACtD,IAAA,oBAAY,EAAC,GAAG,EAAE;gBACjB,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC/B,CAAC,CAAC,CAAC;QACJ,CAAC;IAEF,CAAC;IAKM,mBAAmB,CAAC,iBAAiD;QAC3E,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC;QAEvC,IACC,CAAC,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,CAAC;eAClD,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,EACtD,CAAC;YACF,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC/B,CAAC;IACF,CAAC;IAED,IAAW,cAAc;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC7B,CAAC;IAEM,KAAK;QACX,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,oBAAoB;QAChC,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAgB;YACjE,IAAI,CAAC,kBAAkB,EAAE;YACzB,GAAG,IAAI,CAAC,iBAAiB,EAAE;YAC3B;gBACC,IAAI,EAAE,MAAM,CAAC,iBAAiB,CAAC,SAAS;gBACxC,KAAK,EAAE,EAAE;gBACT,GAAG,EAAE,GAAG,EAAE,GAAc,CAAC;aACzB;YACD,iBAAiB;SACjB,EAAE;YACF,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,oFAAoF,CAAC;SAChH,CAAC,CAAC;QAEH,OAAO,QAAQ,EAAE,GAAG,EAAE,CAAC;IACxB,CAAC;IAEO,kBAAkB;QACzB,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QAC3D,OAAO;YACN,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,uBAAuB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS;gBACnE,CAAC,CAAC,IAAI;gBACN,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC;YAC/C,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,MAAM,EAAE,cAAc,CAAC,SAAS;YAChC,GAAG,EAAE,KAAK,IAAI,EAAE;gBACf,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBACpE,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;YAC1C,CAAC;SACD,CAAC;IACH,CAAC;IAEO,iBAAiB;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;YACvD,OAAO;gBACN,KAAK,EAAE,CAAC,IAAI,CAAC,uBAAuB,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC;oBACpG,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC;gBAC/C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,MAAM,EAAE,OAAO,CAAC,SAAS;gBACzB,GAAG,EAAE,KAAK,IAAI,EAAE;oBACf,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC;oBAC/D,IAAI,OAAO,EAAE,CAAC;wBACb,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;wBACnE,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;wBACjE,MAAM,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;wBACxD,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;oBACnC,CAAC;gBACF,CAAC;aACD,CAAC;QACH,CAAC,CAAC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,sBAAsB;QACnC,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;QAE3D,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,gGAAgG,CAAC,CAAC;QACnH,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC/C,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC;QAEhE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uJAAuJ,CAAC,EAC/N,OAAO,EACP,aAAa,EACb,cAAc,CACd,CAAC;QAEF,IAAI,MAAM,KAAK,OAAO,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,CAAC;YACnE,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAC5C,CAAC;aAAM,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,qCAAqC,EAAE,IAAI,CAAC,CAAC;QAC/E,CAAC;IACF,CAAC;IAEO,mBAAmB,CAAC,aAAgC;QAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC;QACvC,IAAI,CAAC,eAAe,GAAG,aAAa,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;YACnC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;QAClC,CAAC;IACF,CAAC;IAED,IAAY,uBAAuB;QAClC,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAU,0BAA0B,EAAE,KAAK,CAAC,CAAC;IAC5E,CAAC;IAED,IAAY,kCAAkC;QAC7C,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAU,qCAAqC,EAAE,KAAK,CAAC,CAAC;IACvF,CAAC;IAEO,4BAA4B,CAAC,aAA6C;QACjF,OAAO,CACN,aAAa,CAAC,SAAS,KAAK,IAAI;eAC7B,aAAa,CAAC,4BAA4B,KAAK,IAAI;eACnD,IAAI,CAAC,kCAAkC,KAAK,KAAK;eACjD,IAAI,CAAC,uBAAuB,KAAK,KAAK,CACzC,CAAC;IACH,CAAC;CACD;AAjKD,4DAiKC;AAED,MAAM,iBAAiB,GAAkB;IACxC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,+CAA+C,CAAC;IACrE,WAAW,EAAE,EAAE;IACf,GAAG,EAAE,GAAG,EAAE;QACT,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,gDAAgD,CAAC,CAAC,CAAC;IAC7F,CAAC;CACD,CAAC", "file": "versionManager.js", "sourceRoot": "../../src/"}