{"version": 3, "sources": ["languageFeatures/codeLens/baseCodeLensProvider.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFhG,wCAwBC;AAtGD,+CAAiC;AAGjC,qEAAuD;AAEvD,+CAAkD;AAClD,iDAAiD;AAGjD,MAAa,kBAAmB,SAAQ,MAAM,CAAC,QAAQ;IACtD,YACQ,QAAoB,EACpB,IAAY,EACnB,KAAmB;QAEnB,KAAK,CAAC,KAAK,CAAC,CAAC;QAJN,aAAQ,GAAR,QAAQ,CAAY;QACpB,SAAI,GAAJ,IAAI,CAAQ;IAIpB,CAAC;CACD;AARD,gDAQC;AAED,MAAsB,8BAA+B,SAAQ,oBAAU;IAetE,YACW,MAAgC,EACzB,cAAqD;QAEtE,KAAK,EAAE,CAAC;QAHE,WAAM,GAAN,MAAM,CAA0B;QACzB,mBAAc,GAAd,cAAc,CAAuC;QAhB7D,kBAAa,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC,CAAC;QACnE,0BAAqB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IAkBxD,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAA6B,EAAE,KAA+B;QACrF,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QAC9H,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAClC,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,kBAAkB,GAAmB,EAAE,CAAC;QAC9C,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAC5G,OAAO,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,kBAAkB,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7F,CAAC;IAQO,WAAW,CAClB,QAA6B,EAC7B,IAA0B,EAC1B,MAAwC,EACxC,OAAuB;QAEvB,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QACzD,IAAI,KAAK,EAAE,CAAC;YACX,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;IACrF,CAAC;;AAxDF,wEAyDC;AArDuB,+CAAgB,GAAmB;IACzD,iGAAiG;IACjG,KAAK,EAAE,EAAE;IACT,OAAO,EAAE,EAAE;CAH2B,AAItC,CAAC;AAEqB,2CAAY,GAAmB;IACrD,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC,CAAC;IACtD,OAAO,EAAE,EAAE;CAFuB,AAGlC,CAAC;AA8CH,SAAgB,cAAc,CAC7B,QAA6B,EAC7B,IAA0B;IAE1B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACzD,CAAC;IAED,oEAAoE;IACpE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;QACX,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACtD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAErC,MAAM,eAAe,GAAG,IAAI,MAAM,CAAC,kBAAkB,IAAA,qBAAY,EAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACrG,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzC,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,YAAY,CAAC;IACnH,OAAO,IAAI,MAAM,CAAC,KAAK,CACtB,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,EAChC,QAAQ,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACvD,CAAC", "file": "baseCodeLensProvider.js", "sourceRoot": "../../../src/"}