[{"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 417, "column": 54, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 14, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 20, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 31, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 36, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 39, "message": "'(' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 43, "message": "')' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 48, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 54, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 58, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 418, "column": 78, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 419, "column": 11, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 419, "column": 17, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 420, "column": 15, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 420, "column": 19, "message": "Unknown keyword or identifier. Did you mean 'require'?"}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 420, "column": 45, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 420, "column": 56, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 420, "column": 60, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 421, "column": 10, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 421, "column": 16, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 421, "column": 21, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 421, "column": 27, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 421, "column": 31, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 422, "column": 10, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 422, "column": 23, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 422, "column": 30, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 422, "column": 34, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 423, "column": 13, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 423, "column": 17, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 423, "column": 21, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 8, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 11, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 16, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 22, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 25, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 39, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 45, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 49, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 56, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 59, "message": "Unknown keyword or identifier. Did you mean 'get _dir_tree'?"}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 72, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 426, "column": 75, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 427, "column": 8, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 427, "column": 13, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 427, "column": 17, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 427, "column": 44, "message": "'(' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 427, "column": 52, "message": "')' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 428, "column": 8, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 428, "column": 11, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 428, "column": 16, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 428, "column": 22, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 428, "column": 25, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 428, "column": 39, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 428, "column": 43, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 429, "column": 8, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 429, "column": 11, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 429, "column": 16, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 429, "column": 22, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 429, "column": 25, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 429, "column": 37, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 429, "column": 41, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 432, "column": 5, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 432, "column": 8, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 432, "column": 39, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 433, "column": 8, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 433, "column": 15, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 433, "column": 52, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 433, "column": 109, "message": "Type expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 434, "column": 5, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 434, "column": 8, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 434, "column": 12, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 434, "column": 49, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 435, "column": 8, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 435, "column": 32, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 435, "column": 69, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 436, "column": 8, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 436, "column": 13, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 436, "column": 38, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 438, "column": 14, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 438, "column": 16, "message": "Unknown keyword or identifier. Did you mean 'for'?"}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 438, "column": 38, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 438, "column": 48, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 438, "column": 61, "message": "Identifier expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 438, "column": 67, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 439, "column": 5, "message": "Invalid character."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 439, "column": 15, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 439, "column": 23, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 440, "column": 5, "message": "Invalid character."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 440, "column": 14, "message": "Unknown keyword or identifier. Did you mean 'using'?"}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 440, "column": 20, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 440, "column": 31, "message": "'(' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 440, "column": 40, "message": "')' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 440, "column": 54, "message": "Module declaration names may only use ' or \" quoted strings."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 450, "column": 31, "message": "Invalid character."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 450, "column": 31, "message": "Declaration or statement expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 451, "column": 5, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 451, "column": 26, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 451, "column": 32, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 451, "column": 40, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 451, "column": 43, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 451, "column": 48, "message": "',' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 451, "column": 55, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 453, "column": 5, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 457, "column": 10, "message": "Invalid character."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 458, "column": 5, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 460, "column": 5, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 15, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 19, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 23, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 29, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 35, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 43, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 46, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 67, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 462, "column": 76, "message": "Identifier expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 19, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 23, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 36, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 43, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 50, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 94, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 100, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 104, "message": "A type predicate is only allowed in return type position for functions and methods."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 107, "message": "Invalid character."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 469, "column": 107, "message": "Declaration or statement expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 470, "column": 1, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 1, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 5, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 10, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 13, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 19, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 32, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 35, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 42, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 47, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 61, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 65, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 69, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 74, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 77, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 83, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 85, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 90, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 93, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 99, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 104, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 108, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 113, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 117, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 130, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 143, "message": "'(' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 474, "column": 152, "message": "Invalid character."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 475, "column": 42, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 65, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 91, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 95, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 99, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 109, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 115, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 135, "message": "';' expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 597, "column": 144, "message": "Identifier expected."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 17, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 20, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 24, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 29, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 32, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 37, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 41, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 46, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 51, "message": "Unexpected keyword or identifier."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 598, "column": 134, "message": "Unterminated string literal."}, {"path": "C:/Users/<USER>/Desktop/testing_purposes/testing_purposes/Flow/src/vs/workbench/contrib/Flow/common/prompt/prompts.ts", "line": 625, "column": 1, "message": "Declaration or statement expected."}]