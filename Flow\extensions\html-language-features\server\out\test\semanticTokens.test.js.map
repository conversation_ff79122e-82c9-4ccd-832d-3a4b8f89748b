{"version": 3, "sources": ["test/semanticTokens.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,iBAAe;AACf,+CAAiC;AACjC,0DAA6G;AAC7G,4DAAmE;AACnE,2CAA+C;AAS/C,KAAK,UAAU,YAAY,CAAC,KAAe,EAAE,QAAyB,EAAE,MAAgB,EAAE,OAAgB;IACzG,MAAM,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC,qBAAqB,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACzF,MAAM,SAAS,GAAG;QACjB,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;KAC7C,CAAC;IACF,MAAM,aAAa,GAAG,IAAA,gCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,kCAAkB,CAAC,MAAM,EAAE,IAAA,sBAAa,GAAE,CAAC,CAAC;IAC/H,MAAM,sBAAsB,GAAG,IAAA,yCAAwB,EAAC,aAAa,CAAC,CAAC;IAEvE,MAAM,MAAM,GAAG,sBAAsB,CAAC,MAAM,CAAC;IAC7C,MAAM,MAAM,GAAG,MAAM,sBAAsB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAEhF,MAAM,YAAY,GAAG,EAAE,CAAC;IACxB,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,aAAa,GAAG,CAAC,CAAC;IACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7H,MAAM,IAAI,GAAG,QAAQ,GAAG,SAAS,CAAC;QAClC,MAAM,SAAS,GAAG,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;QAC1E,MAAM,kBAAkB,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpH,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC;QAC/D,QAAQ,GAAG,IAAI,CAAC;QAChB,aAAa,GAAG,SAAS,CAAC;IAC3B,CAAC;IACD,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,CAAC,CAAC,SAAiB,EAAE,SAAiB,EAAE,MAAc,EAAE,kBAA0B;IAC1F,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,kBAAkB,EAAE,CAAC;AAC7D,CAAC;AAED,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAElC,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QAC5B,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,UAAU;YACf,KAAK,CAAA,wBAAwB;YAC7B,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,mCAAmC;YACxC,KAAK,CAAA,iBAAiB;YACtB,KAAK,CAAA,eAAe;YACpB,KAAK,CAAA,KAAK;YACV,KAAK,CAAA,WAAW;YAChB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;YAChG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,qCAAqC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC;YAC5I,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,4BAA4B,CAAC;YACzC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;SACvB,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QAC5B,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,UAAU;YACf,KAAK,CAAA,sBAAsB;YAC3B,KAAK,CAAA,8BAA8B;YACnC,KAAK,CAAA,KAAK;YACV,KAAK,CAAA,0DAA0D;YAC/D,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,SAAS;SACd,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC;YACzE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC;YAC/H,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC;SAClP,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;QAC1B,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,UAAU;YACf,KAAK,CAAA,aAAa;YAClB,KAAK,CAAA,mBAAmB;YACxB,KAAK,CAAA,YAAY;YACjB,KAAK,CAAA,iDAAiD;YACtD,KAAK,CAAA,+BAA+B;YACpC,KAAK,CAAA,uCAAuC;YAC5C,KAAK,CAAA,sBAAsB;YAC3B,MAAM,CAAA,KAAK;YACX,MAAM,CAAA,WAAW;YACjB,MAAM,CAAA,SAAS;YACf,MAAM,CAAA,SAAS;SACf,CAAC;QAGF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,mBAAmB,CAAC;YAC/B,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,6BAA6B,CAAC;YAC1C,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,sBAAsB,CAAC;YAClC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,cAAc,CAAC;YAC1H,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;YAC3D,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,2BAA2B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;SACvF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,EAAE;QAC7B,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,iCAAiC;YACtC,KAAK,CAAA,gDAAgD;YACrD,KAAK,CAAA,yCAAyC;YAC9C,KAAK,CAAA,2CAA2C;YAChD,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,SAAS;SACd,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC;YAC9G,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,+BAA+B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC;YAC/I,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,+BAA+B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,CAAC;SACjN,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE;QAC3B,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,iCAAiC;YACtC,KAAK,CAAA,gBAAgB;YACrB,KAAK,CAAA,uDAAuD;YAC5D,KAAK,CAAA,qCAAqC;YAC1C,KAAK,CAAA,wCAAwC;YAC7C,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,SAAS;SACd,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,+BAA+B,CAAC;YAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sCAAsC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,6BAA6B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC;YACzK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,qBAAqB,CAAC;YACnK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,kCAAkC,CAAC;SACnR,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAGH,IAAI,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,iCAAiC;YACtC,KAAK,CAAA,qCAAqC;YAC1C,KAAK,CAAA,qDAAqD;YAC1D,KAAK,CAAA,oDAAoD;YACzD,KAAK,CAAA,KAAK;YACV,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,SAAS;SACd,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,0BAA0B,CAAC,CAAC,oBAAoB;YAC5F,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,2BAA2B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,eAAe,CAAC;YACpM,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC;SACtF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QAC5B,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,iCAAiC;YACtC,KAAK,CAAA,gDAAgD;YACrD,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,UAAU;YACf,KAAK,CAAA,0BAA0B;YAC/B,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,SAAS;SACd,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,2BAA2B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,uBAAuB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC;YACzM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,uBAAuB,CAAC;SAC1E,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QACzB,MAAM,KAAK,GAAG;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,QAAQ;YACb,KAAK,CAAA,UAAU;YACf,KAAK,CAAA,0BAA0B;YAC/B,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,UAAU;YACf,KAAK,CAAA,0BAA0B;YAC/B,KAAK,CAAA,WAAW;YAChB,KAAK,CAAA,SAAS;YACd,KAAK,CAAA,SAAS;SACd,CAAC;QACF,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,yBAAyB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,uBAAuB,CAAC;SAC1E,EAAE,CAAC,qBAAK,CAAC,MAAM,CAAC,wBAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,MAAM,YAAY,CAAC,KAAK,EAAE;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,yBAAyB,CAAC;SACrC,EAAE,CAAC,qBAAK,CAAC,MAAM,CAAC,wBAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC,CAAC,CAAC;AAGJ,CAAC,CAAC,CAAC", "file": "semanticTokens.test.js", "sourceRoot": "../../src/"}