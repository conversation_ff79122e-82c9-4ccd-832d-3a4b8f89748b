{"version": 3, "sources": ["extension.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqChG,4BA2OC;AAED,gCAEC;AAlRD,mCAAqC;AACrC,2BAA4B;AAC5B,2CAA6B;AAC7B,+CAAiC;AACjC,yDAAoD;AACpD,2FAAwF;AACxF,qDAAkD;AAClD,yCAAoD;AACpD,2DAAqD;AACrD,yCAMoB;AACpB,yDAA6F;AAC7F,+CAA4C;AAE5C,MAAM,iBAAiB,GAAG,uCAAuC,CAAC;AAElE,MAAM,6BAA6B,GAAG,CAAC,GAAe,EAAE,EAAE,CACzD,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;IAC1E,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;IAC7B,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC;IAC1C,CAAC,CAAC,SAAS,CAAC;AAEd,MAAM,WAAW,GAAkC;IAClD,CAAC,QAAQ,EAAE,UAAU,CAAC;IACtB,CAAC,SAAS,EAAE,SAAS,CAAC;IACtB,CAAC,QAAQ,EAAE,QAAQ,CAAC;CACpB,CAAC;AAIK,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC9D,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,0BAA0B,EAAE,eAAe,CAAC,CAAC;IAC5F,MAAM,kBAAkB,GAAG,IAAI,MAAM,CAAC,YAAY,EAAmB,CAAC;IAEtE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC;QACpE,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM;YACnE,OAAO,CAAC;oBACP,KAAK,EAAE,6BAA6B;oBACpC,OAAO,EAAE,qCAAqC;oBAC9C,SAAS,EAAE,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;iBACzG,CAAC,CAAC;QACJ,CAAC;KACD,CAAC,CAAC,CAAC;IAEJ,IAAI,mBAA2D,CAAC;IAChE,MAAM,cAAc,GAAG,KAAK,EAAE,IAAsB,EAAE,EAAE;QACvD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC1B,mBAAmB,GAAG,sBAAsB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;gBACvE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,mBAAmB,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACP,MAAM,mBAAmB,CAAC;YAC3B,CAAC;YACD,OAAO;QACR,CAAC;QAED,MAAM,IAAI,GAAG,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,IAAI,YAAY,mBAAQ,EAAE,CAAC;YAC9B,mEAAmE;YACnE,+CAA+C;YAC/C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC;IACF,CAAC,CAAC;IAEF,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IAErC,IAAA,+BAAoB,GAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,OAAO;QACR,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,yBAAW,CAC5B,MAAM,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;YACtB,MAAM,cAAc,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;QAChD,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAC;QAEjC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,+BAAc,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9F,CAAC;QAED,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC9F,CAAC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,CACxB,UAAqE,EACrE,IAA+B,EAC/B,OAAiB,EAAE,EAClB,EAAE;QACH,MAAM,SAAS,GAAG,KAAK,EACtB,GAA0B,EAC1B,iBAA2C,EAC1C,EAAE;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,+BAAoB,GAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACb,OAAO;YACR,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;YACtC,MAAM,GAAG,GAAG,MAAM,iBAAiB,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACtF,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YACrC,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC;gBACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;YAED,IAAI,WAA+B,CAAC;YACpC,IAAI,WAAW,GAAG,IAAI,CAAC;YACvB,IAAI,IAAI,KAAK,MAAM,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;gBACjD,+DAA+D;gBAC/D,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBAChC,WAAW,GAAG,IAAI,CAAC,IAAI,CACtB,IAAA,WAAM,GAAE,EACR,wBAAwB,IAAA,oBAAW,EAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CACxD,CAAC;oBACF,WAAW,GAAG;wBACb,GAAG,WAAW;wBACd,YAAY;wBACZ,gBAAgB;wBAChB,WAAW;wBACX,mBAAmB;wBACnB,MAAM;qBACN,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACP,WAAW,GAAG,CAAC,GAAG,WAAW,EAAE,qBAAqB,CAAC,CAAC;gBACvD,CAAC;YACF,CAAC;YAED,OAAO,MAAM,IAAA,kCAAc,EAC1B,GAAG,EACH,IAAI,EACJ,IAAI,KAAK,MAAM,CAAC,kBAAkB,CAAC,KAAK;gBACvC,CAAC,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC;gBACpD,CAAC,CAAC,MAAM,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,EAC7C,WAAW,EACX,iBAAiB,CACjB,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,KAAK,EAAE,GAA0B,EAAE,iBAA2C,EAAE,EAAE;YACxF,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC;gBACrB,OAAO,SAAS,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;YAC1C,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;YACtC,IAAI,SAAqC,CAAC;YAE1C,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;gBAC9D,YAAY,CAAC,SAAS,CAAC,CAAC;gBAExB,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;oBACjF,OAAO;gBACR,CAAC;gBAED,IAAI,OAAO,EAAE,CAAC;oBACb,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACP,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACjC,CAAC;gBAED,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC3B,MAAM,OAAO,GACZ,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;wBACrE,CAAC,GAAG,WAAW,CAAC;6BACd,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;6BACpD,MAAM,CAAC,CAAC,CAAC,EAAwB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,WAAW,CAAC,KAAK,EAAE,CAAC;oBAEpB,SAAS,CACR,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,EAClE,iBAAiB,CACjB,CAAC;gBACH,CAAC,EAAE,IAAI,CAAC,CAAC;YACV,CAAC,CAAC,CAAC;YAEH,iBAAiB,CAAC,uBAAuB,CAAC,GAAG,EAAE;gBAC9C,YAAY,CAAC,SAAS,CAAC,CAAC;gBACxB,QAAQ,CAAC,OAAO,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAAC,gBAAgB,CACpB,iBAAiB,EACjB,MAAM,CAAC,kBAAkB,CAAC,GAAG,EAC7B,gBAAgB,CAAC,qCAAkB,EAAE,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,EACnE,IAAI,EACJ,SAAS,EACT,IAAI,CACJ,CAAC;IAEF,IAAI,CAAC,gBAAgB,CACpB,mBAAmB,EACnB,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,gBAAgB,CAAC,qCAAkB,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EACrE,IAAI,EACJ,SAAS,EACT,IAAI,CACJ,CAAC;IAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CACrC,sBAAsB,EACtB,MAAM,CAAC,kBAAkB,CAAC,QAAQ,EAClC,gBAAgB,CAAC,qCAAkB,EAAE,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EACxE,IAAI,EACJ,SAAS,EACT,IAAI,CACJ,CAAC;IAEF,QAAQ,CAAC,oBAAoB,GAAG,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC,QAAQ,YAAY,iCAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;IACrH,QAAQ,CAAC,2BAA2B,GAAG,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,YAAY,iCAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAE5I,KAAK,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,WAAW,EAAE,CAAC;QACvC,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAChC,UAAU,IAAI,EAAE,EAChB,MAAM,CAAC,kBAAkB,CAAC,GAAG,EAC7B,gBAAgB,CAAC,oCAAiB,EAAE,MAAM,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,EACvF,SAAS,EACT,SAAS,EACT,IAAI,CACJ,CAAC;QAEF,GAAG,CAAC,gBAAgB,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,eAAe,IAAI,EAAE,CAAC,CAAC;QAEzF,IAAI,CAAC,gBAAgB,CACpB,YAAY,IAAI,EAAE,EAClB,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,gBAAgB,CAAC,oCAAiB,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE;YACpE,WAAW;YACX,GAAG;YACH,iBAAiB;SACjB,CAAC,EACF,SAAS,EACT,SAAS,EACT,IAAI,CACJ,CAAC;IACH,CAAC;IAED,SAAS,qBAAqB,CAAC,CAAsB;QACpD,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,IAAI,GAAG,IAAI,IAAI,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,IAAI,YAAY,mBAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,EAAE,IAAK,CAAC,CAAC;QACnD,CAAC;IACF,CAAC;IAED,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;QACvD,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,CAAC,aAAa,CAAC,IAAI,CACzB,IAAI,EACJ,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,EAAE;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;YACd,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YACxC,IAAI,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC9B,CAAC;QACF,CAAC;IACF,CAAC,CAAC,EACF,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,EAC7D,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAChF,IAAA,iCAAsB,EAAC,IAAI,CAAC,EAC5B,IAAI,qEAAiC,EAAE,CACvC,CAAC;AACH,CAAC;AAED,SAAgB,UAAU;IACzB,QAAQ;AACT,CAAC;AAED,SAAS,eAAe,CACvB,UAAiC,EACjC,GAAe;IAEf,MAAM,MAAM,GAAG,6BAA6B,CAAC,GAAG,CAAC,CAAC;IAClD,IAAI,CAAC,MAAM,EAAE,CAAC;QACb,OAAO,SAAS,CAAC;IAClB,CAAC;IAED,MAAM,IAAI,GAAG,IAAI,mBAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACvC,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IACpD,IAAI,QAAQ,EAAE,CAAC;QACd,OAAO,QAAQ,CAAC;IACjB,CAAC;IAED,MAAM,IAAI,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC;IAC3E,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;IAC/B,mBAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAEzB,OAAO,IAAI,CAAC;AACb,CAAC;AAED,SAAS,eAAe,CAAC,UAAqC;IAC7D,MAAM,KAAK,GAAsB,EAAE,CAAC;IACpC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7C,OAAO,KAAK,CAAC;AACd,CAAC;AAED,KAAK,UAAU,sBAAsB,CACpC,UAAiC,EACjC,kBAAwD;IAExD,MAAM,eAAe,GAAG,MAAM,IAAA,+BAAoB,GAAE,CAAC;IACrD,IAAI,CAAC,eAAe,EAAE,CAAC;QACtB,OAAO,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,eAAe,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC;IAC/E,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;IAElE,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;QACzB,eAAe,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACjC,kBAAkB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;IAClD,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC7E,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;QACzB,kBAAkB,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;QAChD,IAAA,+BAAoB,EAAC,GAAG,CAAC,CAAC;QAC1B,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,KAAK,MAAM,IAAI,IAAI,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;QAC9D,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,OAAO,CAAC;AAChB,CAAC;AAED,KAAK,UAAU,iBAAiB,CAAC,IAA2B,EAAE,KAAgC;IAC7F,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;IACtB,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA2B,CAAC;IACpD,OAAO,KAAK,CAAC,MAAM,EAAE,CAAC;QACrB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,GAAG,EAAG,EAAE,CAAC;YACjC,MAAM,IAAI,GAAG,mBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,IAAI,YAAY,mBAAQ,EAAE,CAAC;gBAC9B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBACvB,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACvC,CAAC;gBACD,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,IAAI,YAAY,mBAAQ,EAAE,CAAC;gBACrC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACP,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5C,CAAC;QACF,CAAC;IACF,CAAC;IAED,OAAO,QAAQ,CAAC;AACjB,CAAC", "file": "extension.js", "sourceRoot": "../src/"}