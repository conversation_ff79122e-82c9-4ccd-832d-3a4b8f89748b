"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncOnlyAllowedInAsyncFunctions = exports.extendsShouldBeImplements = exports.cannotFindName = exports.incorrectlyImplementsInterface = exports.notAllCodePathsReturnAValue = exports.fallThroughCaseInSwitch = exports.unusedLabel = exports.unreachableCode = exports.allImportsAreUnused = exports.propertyDeclaretedButNeverUsed = exports.variableDeclaredButNeverUsed = void 0;
exports.variableDeclaredButNeverUsed = new Set([6196, 6133]);
exports.propertyDeclaretedButNeverUsed = new Set([6138]);
exports.allImportsAreUnused = new Set([6192]);
exports.unreachableCode = new Set([7027]);
exports.unusedLabel = new Set([7028]);
exports.fallThroughCaseInSwitch = new Set([7029]);
exports.notAllCodePathsReturnAValue = new Set([7030]);
exports.incorrectlyImplementsInterface = new Set([2420]);
exports.cannotFindName = new Set([2552, 2304]);
exports.extendsShouldBeImplements = new Set([2689]);
exports.asyncOnlyAllowedInAsyncFunctions = new Set([1308]);
//# sourceMappingURL=errorCodes.js.map