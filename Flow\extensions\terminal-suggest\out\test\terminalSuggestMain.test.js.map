{"version": 3, "sources": ["test/terminalSuggestMain.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;AAEhG,mCAAsD;AACtD,iBAAe;AACf,+BAAgC;AAChC,gEAA8E;AAC9E,sCAAyC;AACzC,mDAAuE;AACvE,uDAAuF;AACvF,uCAAuD;AACvD,yEAAyE;AACzE,4DAAiE;AACjE,gEAAqE;AACrE,kEAAuE;AACvE,4DAAiE;AACjE,kEAAuE;AACvE,kEAAuE;AACvE,8DAAmE;AACnE,sCAA4C;AAC5C,+DAAqD;AACrD,yCAAkD;AAGlD,MAAM,UAAU,GAAiB;IAChC;QACC,IAAI,EAAE,iCAAiC;QACvC,eAAe,EAAE,EAAE;QACnB,iBAAiB,EAAE,EAAE;QACrB,SAAS,EAAE;YACV,EAAE,KAAK,EAAE,GAAG,EAAE,mBAAmB,EAAE,EAAE,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;YACvG,EAAE,KAAK,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAAE,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;YACxG,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,EAAE,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;YACzG,EAAE,KAAK,EAAE,eAAe,EAAE,mBAAmB,EAAE,EAAE,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;SACnH;KACD;IAED,GAAG,+BAAoB;IAEvB,eAAe;IACf,yBAAW;IACX,yBAAa;IACb,0CAAqB;IAErB,wBAAwB;IACxB,6BAAiB;IACjB,yBAAe;IACf,+BAAkB;IAClB,yBAAe;IACf,+BAAkB;IAClB,+BAAkB;IAClB,2BAAgB;CAChB,CAAC;AAEF,IAAI,IAAA,gBAAW,GAAE,EAAE,CAAC;IACnB,UAAU,CAAC,IAAI,CAAC;QACf,IAAI,EAAE,sCAAsC;QAC5C,eAAe,EAAE,CAAC,cAAkB,CAAC;QACrC,iBAAiB,EAAE;YAClB,UAAU;YACV,UAAU;YACV,UAAU;YACV,eAAe;SACf;QACD,SAAS,EAAE;YACV,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,EAAE,yCAA6B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;YACvI,EAAE,KAAK,EAAE,YAAY,EAAE,mBAAmB,EAAE,yCAA6B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;YAC3I,EAAE,KAAK,EAAE,YAAY,EAAE,mBAAmB,EAAE,yCAA6B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;YAC3I,EAAE,KAAK,EAAE,YAAY,EAAE,mBAAmB,EAAE,yCAA6B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;YAC3I,EAAE,KAAK,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,yCAA6B,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;SAChJ;KACD,CAAC,CAAC;AACJ,CAAC;AAED,KAAK,CAAC,kBAAkB,EAAE,GAAG,EAAE;IAC9B,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACpC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE;YAC1B,MAAM,eAAe,GAAG,IAAA,6BAAO,EAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YAC3D,MAAM,iBAAiB,GAAG,IAAA,6BAAO,EAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC;YAC/D,KAAK,MAAM,QAAQ,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC5C,IAAI,cAAc,GAAG,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC7H,IAAI,QAAQ,CAAC,wBAAwB,EAAE,CAAC;oBACvC,cAAc,IAAI,MAAM,QAAQ,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC;oBACjE,IAAI,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,KAAK,mBAAS,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;wBAC3E,cAAc,IAAI,MAAM,IAAA,eAAQ,EAAC,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;oBACnF,CAAC;gBACF,CAAC;gBACD,IAAI,CAAC,IAAI,QAAQ,CAAC,KAAK,QAAQ,cAAc,EAAE,EAAE,KAAK,IAAI,EAAE;oBAC3D,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjD,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACnD,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC5E,MAAM,cAAc,GAAG,QAAQ,CAAC,wBAAwB,EAAE,IAAI,KAAK,OAAO,IAAI,QAAQ,CAAC,wBAAwB,EAAE,IAAI,KAAK,MAAM,CAAC;oBACjI,MAAM,gBAAgB,GAAG,QAAQ,CAAC,wBAAwB,EAAE,IAAI,KAAK,SAAS,IAAI,QAAQ,CAAC,wBAAwB,EAAE,IAAI,KAAK,MAAM,CAAC;oBACrI,MAAM,eAAe,GAAG,EAAE,WAAW,EAAE,cAAc,EAAE,wBAAwB,EAAE,IAAI,EAAE,CAAC;oBACxF,MAAM,MAAM,GAAG,MAAM,IAAA,iDAA2B,EAC/C,eAAe,EACf,eAAe,EACf,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EACpD,MAAM,EACN,IAAA,qBAAY,EAAC,eAAe,EAAE,SAAS,CAAC,EACxC,mBAAS,CAAC,GAAG,EACb,EAAE,EACF,UAAU,EACV,SAAS,EACT,IAAI,uBAAuB,EAAE,CAC7B,CAAC;oBACF,IAAA,wBAAe;oBACd,uCAAuC;oBACvC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;wBACpB,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;4BACnD,OAAO,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAC5C,CAAC;wBACD,OAAO,CAAC,CAAC,KAAK,CAAC;oBAChB,CAAC,CAAC,CAAC,IAAI,EAAE,EACT,CAAC,QAAQ,CAAC,mBAAmB,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAC3C,CAAC;oBACF,IAAA,oBAAW,EAAC,MAAM,CAAC,cAAc,EAAE,cAAc,EAAE,gDAAgD,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;oBAC7H,IAAA,oBAAW,EAAC,MAAM,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,kDAAkD,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;oBACrI,IAAI,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE,CAAC;wBAC5C,IAAA,oBAAW,EAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,wBAAwB,CAAC,GAAG,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;oBACnG,CAAC;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;AACF,CAAC,CAAC,CAAC;AAGH,MAAM,uBAAuB;IACrB,KAAK,CAAC,cAAc,CAAC,KAA8B;QACzD,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IACD,KAAK,CAAC,qBAAqB,CAAC,KAA8B;QACzD,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD,IAAI,CAAC;YACJ,OAAO;gBACN,MAAM,EAAE,CAAC;gBACT,MAAM,EAAE,KAAK,CAAC,OAAO;gBACrB,MAAM,EAAE,EAAE;aACV,CAAC;QACH,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,KAAK,CAAC,gCAAgC,OAAO,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC;YACnE,MAAM,GAAG,CAAC;QACX,CAAC;IACF,CAAC;CACD", "file": "terminalSuggestMain.test.js", "sourceRoot": "../../src/"}