{"version": 3, "sources": ["languageFeatures/folding.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EhG,4BAMC;AAnFD,+CAAiC;AAGjC,kEAAoD;AAEpD,4CAA2C;AAE3C,MAAM,yBAAyB;IAE9B,YACkB,MAAgC;QAAhC,WAAM,GAAN,MAAM,CAA0B;IAC9C,CAAC;IAEL,KAAK,CAAC,oBAAoB,CACzB,QAA6B,EAC7B,QAA+B,EAC/B,KAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO;QACR,CAAC;QAED,MAAM,IAAI,GAA0B,EAAE,IAAI,EAAE,CAAC;QAC7C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC7E,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO;QACR,CAAC;QAED,OAAO,IAAA,iBAAQ,EAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvF,CAAC;IAEO,oBAAoB,CAC3B,IAAyB,EACzB,QAA6B;QAE7B,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,MAAM,IAAI,GAAG,yBAAyB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAEjE,wBAAwB;QACxB,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;YACpD,IAAI,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,OAAO,SAAS,CAAC;YAClB,CAAC;QACF,CAAC;QAED,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;QAC/B,MAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QACnD,OAAO,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;IAIO,gBAAgB,CAAC,KAAmB,EAAE,QAA6B;QAC1E,wBAAwB;QACxB,IAAI,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;YACnG,IAAI,yBAAyB,CAAC,qBAAqB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAChF,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC;QACF,CAAC;QAED,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;IACvB,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,IAAyB;QAC3D,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,SAAS,CAAC,CAAC,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACvD,KAAK,QAAQ,CAAC,CAAC,OAAO,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;YACrD,KAAK,SAAS,CAAC,CAAC,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC;YACvD,KAAK,MAAM,CAAC;YACZ,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;QAC3B,CAAC;IACF,CAAC;;AAtBuB,+CAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAyB3E,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,MAAgC;IAEhC,OAAO,MAAM,CAAC,SAAS,CAAC,4BAA4B,CAAC,QAAQ,CAAC,MAAM,EACnE,IAAI,yBAAyB,CAAC,MAAM,CAAC,CAAC,CAAC;AACzC,CAAC", "file": "folding.js", "sourceRoot": "../../src/"}