{"version": 3, "sources": ["completions/upstream/grep.ts"], "names": [], "mappings": ";;AAAA,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,MAAM;IACZ,WAAW,EACV,kFAAkF;IACnF,IAAI,EAAE;QACL;YACC,IAAI,EAAE,gBAAgB;YACtB,mBAAmB,EAAE,IAAI;SACzB;QACD;YACC,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,WAAW;SACrB;KACD;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,QAAQ;YACd,WAAW,EACV,+GAA+G;SAChH;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC;YACjC,WAAW,EACV,iFAAiF;SAClF;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EACV,iIAAiI;SAClI;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EACV,uFAAuF;SACxF;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;YACxB,WAAW,EACV,oKAAoK;YACrK,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;aACf;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC;YACnC,WAAW,EACV,+FAA+F;SAChG;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EACV,yFAAyF;SAC1F;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EACV,kXAAkX;SACnX;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EACV,0FAA0F;SAC3F;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;YACvB,WAAW,EACV,gLAAgL;SACjL;QACD;YACC,IAAI,EAAE,SAAS;YACf,WAAW,EACV,oZAAoZ;YACrZ,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,MAAM;gBACf,WAAW,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;aACxC;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,uBAAuB,CAAC;YACrC,WAAW,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;YAC3C,WAAW,EACV,oKAAoK;SACrK;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;YACpC,WAAW,EAAE,CAAC,IAAI,EAAE,uBAAuB,CAAC;YAC5C,WAAW,EACV,8LAA8L;SAC/L;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC;YAC3B,WAAW,EACV,ymBAAymB;YAC1mB,IAAI,EAAE;gBACL,IAAI,EAAE,KAAK;aACX;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EACV,4GAA4G;SAC7G;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC;YACnC,WAAW,EACV,+MAA+M;SAChN;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EACV,wbAAwb;SACzb;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EACV,oKAAoK;SACrK;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EACV,oGAAoG;SACrG;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EACV,oIAAoI;SACrI;QACD;YACC,IAAI,EAAE,SAAS;YACf,WAAW,EACV,wMAAwM;YACzM,IAAI,EAAE;gBACL,IAAI,EAAE,OAAO;aACb;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EACV,4GAA4G;SAC7G;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EACV,qaAAqa;SACta;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;YACnC,WAAW,EACV,mWAAmW;SACpW;QACD;YACC,IAAI,EAAE,QAAQ;YACd,WAAW,EACV,qeAAqe;SACte;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EAAE,sDAAsD;YACnE,IAAI,EAAE;gBACL,IAAI,EAAE,KAAK;aACX;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,kBAAkB,CAAC;YAChC,WAAW,EACV,sFAAsF;YACvF,IAAI,EAAE;gBACL,IAAI,EAAE,KAAK;aACX;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,WAAW,EACV,2MAA2M;YAC5M,IAAI,EAAE;gBACL,IAAI,EAAE,KAAK;aACX;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;YACtB,WAAW,EACV,8MAA8M;SAC/M;QACD;YACC,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,iDAAiD;YAC9D,IAAI,EAAE;gBACL,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,QAAQ;gBACjB,WAAW,EAAE;oBACZ;wBACC,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,2CAA2C;qBACxD;oBACD;wBACC,IAAI,EAAE,eAAe;wBACrB,WAAW,EAAE,4BAA4B;qBACzC;oBACD;wBACC,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,yBAAyB;qBACtC;iBACD;aACD;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,WAAW,EAAE,4DAA4D;YACzE,IAAI,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,MAAM;gBACf,WAAW,EAAE;oBACZ;wBACC,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,mCAAmC;qBAChD;oBACD;wBACC,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,kCAAkC;qBAC/C;iBACD;aACD;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE,6CAA6C;YAC1D,IAAI,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,MAAM;gBACf,WAAW,EAAE;oBACZ;wBACC,IAAI,EAAE,MAAM;wBACZ,WAAW,EACV,yDAAyD;qBAC1D;oBACD;wBACC,IAAI,EAAE,MAAM;wBACZ,WAAW,EAAE,iCAAiC;qBAC9C;oBACD;wBACC,IAAI,EAAE,SAAS;wBACf,WAAW,EAAE,8BAA8B;qBAC3C;iBACD;aACD;SACD;QACD;YACC,IAAI,EAAE,WAAW;YACjB,WAAW,EACV,iPAAiP;YAClP,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,eAAe;YACrB,WAAW,EACV,oKAAoK;YACrK,YAAY,EAAE,IAAI;YAClB,IAAI,EAAE;gBACL,IAAI,EAAE,KAAK;gBACX,QAAQ,EAAE,SAAS;gBACnB,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,sFAAsF;SACvF;QACD;YACC,IAAI,EAAE,WAAW;YACjB,WAAW,EACV,oOAAoO;YACrO,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,eAAe;YACrB,WAAW,EACV,mKAAmK;YACpK,IAAI,EAAE;gBACL,IAAI,EAAE,KAAK;gBACX,QAAQ,EAAE,SAAS;gBACnB,UAAU,EAAE,IAAI;aAChB;SACD;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;YACjC,WAAW,EAAE,0CAA0C;SACvD;QACD;YACC,IAAI,EAAE,iBAAiB;YACvB,WAAW,EACV,uIAAuI;SACxI;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;YACxB,WAAW,EAAE,uDAAuD;SACpE;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EACV,qEAAqE;SACtE;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,WAAW,EAAE,4DAA4D;SACzE;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE,gDAAgD;SAC7D;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;YACtB,WAAW,EACV,4IAA4I;YAC7I,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;gBACZ,QAAQ,EAAE,WAAW;aACrB;SACD;KACD;IACD,qBAAqB,EAAE;QACtB;YACC,IAAI,EAAE,MAAM;YACZ,WAAW,EACV,yHAAyH;YAC1H,WAAW,EAAE,aAAa;SAC1B;QACD;YACC,IAAI,EAAE,KAAK;YACX,WAAW,EACV,uEAAuE;YACxE,WAAW,EAAE,YAAY;SACzB;KACD;CACD,CAAC;AAEF,kBAAe,cAAc,CAAC", "file": "grep.js", "sourceRoot": "../../../src/"}