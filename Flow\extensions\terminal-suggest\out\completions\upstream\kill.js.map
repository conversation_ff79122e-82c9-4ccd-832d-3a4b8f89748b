{"version": 3, "sources": ["completions/upstream/kill.ts"], "names": [], "mappings": ";AAAA,uBAAuB;;AAEvB,SAAS,WAAW,CAAC,IAAY;IAChC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAClC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;QAChB,OAAO,sBAAsB,CAAC;IAC/B,CAAC;IACD,OAAO,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,+BAA+B;IAC5C,IAAI,EAAE;QACL,IAAI,EAAE,KAAK;QACX,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE;YACX,MAAM,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,0BAA0B,CAAC;YAClD,WAAW,EAAE,CAAC,MAAc,EAAE,EAAE;gBAC/B,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;oBACtC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBACnD,OAAO;wBACN,IAAI,EAAE,GAAG;wBACT,WAAW,EAAE,IAAI;wBACjB,WAAW,EAAE,GAAG,GAAG,KAAK,IAAI,GAAG;wBAC/B,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC;qBACvB,CAAC;gBACH,CAAC,CAAC,CAAC;YACJ,CAAC;SACD;KACD;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,yDAAyD;YACtE,IAAI,EAAE;gBACL,IAAI,EAAE,aAAa;gBACnB,UAAU,EAAE;oBACX,0DAA0D;oBAC1D,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC;oBAC7B,WAAW,EAAE,CAAC,GAAG,EAAE,EAAE,CACpB,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;wBACjC,IAAI;wBACJ,WAAW,EAAE,QAAQ,IAAI,kBAAkB;wBAC3C,IAAI,EAAE,wBAAwB;qBAC9B,CAAC,CAAC;iBACJ;aACD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,8GAA8G;YAC/G,IAAI,EAAE;gBACL,IAAI,EAAE,aAAa;gBACnB,UAAU,EAAE,IAAI;aAChB;SACD;KACD;CACD,CAAC;AAEF,kBAAe,cAAc,CAAC", "file": "kill.js", "sourceRoot": "../../../src/"}