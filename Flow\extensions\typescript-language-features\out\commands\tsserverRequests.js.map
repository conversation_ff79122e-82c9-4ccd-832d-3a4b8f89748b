{"version": 3, "sources": ["commands/tsserverRequests.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;gGAGgG;AAChG,+CAAiC;AAIjC,wDAAiD;AAIjD,SAAS,mBAAmB,CAAC,KAAU;IACtC,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,uBAAuB,KAAK,SAAS,IAAI,OAAO,KAAK,CAAC,uBAAuB,KAAK,UAAU,CAAC;AAC3H,CAAC;AAMD,MAAa,sBAAsB;IAGlC,YACkB,cAAiD;QAAjD,mBAAc,GAAd,cAAc,CAAmC;QAHnD,OAAE,GAAG,4BAA4B,CAAC;IAI9C,CAAC;IAEE,KAAK,CAAC,OAAO,CAAC,OAAiC,EAAE,IAAU,EAAE,MAAY,EAAE,KAAgC;QACjH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,KAAK,GAAG,uBAAQ,CAAC;QAClB,CAAC;QACD,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9D,MAAM,WAAW,GAAG,IAAmB,CAAC;YACxC,IAAI,OAAO,GAAQ,SAAS,CAAC;YAC7B,IAAI,WAAW,CAAC,IAAI,YAAY,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC5C,OAAO,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;gBACtB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC;gBACvD,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACzD,IAAI,GAAG,OAAO,CAAC;YAChB,CAAC;QACF,CAAC;QAED,iFAAiF;QACjF,kFAAkF;QAClF,0CAA0C;QAC1C,MAAM,SAAS,GAAG;YACjB,sCAAsC;YACtC,aAAa;YACb,gCAAgC;YAChC,yBAAyB;YACzB,0BAA0B;YAC1B,2BAA2B;YAC3B,mCAAmC;YACnC,WAAW;YACX,gBAAgB;YAChB,gBAAgB;SAChB,CAAC;QAEF,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QACtF,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;CACD;AA3CD,wDA2CC", "file": "tsserverRequests.js", "sourceRoot": "../../src/"}