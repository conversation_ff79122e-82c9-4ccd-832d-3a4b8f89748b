{"version": 3, "sources": ["completions/upstream/less.ts"], "names": [], "mappings": ";;AAAA,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,MAAM;IACZ,WAAW,EAAE,kBAAkB;IAC/B,IAAI,EAAE;QACL,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,WAAW;KACrB;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;YACtB,WAAW,EACV,qNAAqN;SACtN;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;YACpC,WAAW,EAAE;;;;;;;+DAO+C;SAC5D;QACD;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;YACpC,WAAW,EAAE;;;;;;;aAOH;SACV;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;YACnB,WAAW,EAAE;;;;;;iBAMC;SACd;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EAAE;;;;;;;;qBAQK;SAClB;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EAAE;;8BAEc;SAC3B;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EAAE,2DAA2D;SACxE;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC;YACtB,WAAW,EAAE;;;;cAIF;SACX;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;YACvB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;YACxB,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gEA4DgD;SAC7D;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE;;YAEJ;SACT;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE;QACR;SACL;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;YACvB,WAAW,EAAE;;;;;IAKZ;SACD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;YACpC,WAAW,EAAE;8BACc;SAC3B;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EAAE;;;;YAIJ;SACT;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EAAE;gBACA;SACb;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC;YACjC,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;YACnB,WAAW,EAAE;;;0DAG0C;SACvD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE;;;;gBAIA;SACb;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE;kBACE;SACf;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;YACnB,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;oBAsBI;SACjB;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EAAE;;uDAEuC;SACpD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE;YACjD,WAAW,EAAE;;;;kBAIE;SACf;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EAAE;;;;;QAKR;SACL;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE;;;oCAGoB;SACjC;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE;sDACsC;SACnD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YAC7B,WAAW,EAAE,qDAAqD;SAClE;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EAAE;;;;;;;OAOT;SACJ;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,gBAAgB,CAAC;YAC9B,WAAW,EAAE;oBACI;SACjB;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC;YAC1B,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE;YACjD,WAAW,EAAE;;;mCAGmB;SAChC;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC;YAC1B,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE;YACjD,WAAW,EAAE;;;;;wDAKwC;SACrD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YACzB,WAAW,EAAE;;kCAEkB;SAC/B;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;YACxB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;YACxB,WAAW,EAAE;;;;;;;;;;;;;;+DAc+C;SAC5D;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC;YACnC,WAAW,EAAE;;;;;+CAK+B;SAC5C;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC;YACnC,WAAW,EAAE;;mDAEmC;SAChD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;YACnC,WAAW,EAAE;;;;;;;;wCAQwB;SACrC;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;YACnC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;oCAqBoB;SACjC;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,uBAAuB,CAAC;YACrC,WAAW,EAAE;sDACsC;SACnD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC;YACjC,WAAW,EAAE;;;;gDAIgC;SAC7C;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC;YACrB,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;YACrB,WAAW,EAAE;;;;;;;;;;gDAUgC;SAC7C;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,YAAY,CAAC;YAC1B,IAAI,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;YAC1B,WAAW,EAAE,oDAAoD;SACjE;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;YACnC,WAAW,EAAE;;8BAEc;SAC3B;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,qBAAqB,CAAC;YACnC,WAAW,EAAE;;;;;;;;;;;;;;;;gBAgBA;SACb;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,WAAW,EAAE,qCAAqC;SAClD;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EAAE;;;;;;;YAOJ;SACT;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,iBAAiB,CAAC;YAC/B,WAAW,EAAE;8CAC8B;SAC3C;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;YACvB,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;YACvB,WAAW,EAAE;;;;;mBAKG;SAChB;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC;YACzB,WAAW,EAAE;;;oBAGI;SACjB;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,mBAAmB,CAAC;YACjC,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;YACnB,WAAW,EAAE;;;;0BAIU;SACvB;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,UAAU,CAAC;YACxB,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;YACnB,WAAW,EAAE;;;;;;;8DAO8C;SAC3D;QAED;YACC,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE;;;;;;;;;;;OAWT;SACJ;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;YACvB,WAAW,EAAE;;eAED;SACZ;QAED;YACC,IAAI,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC;YACvB,WAAW,EAAE;;;;;;;;;;MAUV;SACH;QAED;YACC,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE;;;;;;;gEAOgD;SAC7D;QACD;YACC,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE;;0CAE0B;SACvC;QAED;YACC,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE;kDACkC;SAC/C;QACD;YACC,IAAI,EAAE,SAAS;YACf,WAAW,EAAE;;;;;;oDAMoC;SACjD;QACD;YACC,IAAI,EAAE,SAAS;YACf,WAAW,EAAE;qBACK;SAClB;QACD;YACC,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE;;gEAEgD;SAC7D;QACD;YACC,IAAI,EAAE,eAAe;YACrB,WAAW,EAAE;;;;;eAKD;SACZ;QACD;YACC,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE;;;wDAGwC;SACrD;QACD;YACC,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE;8BACc;SAC3B;QACD;YACC,IAAI,EAAE,oBAAoB;YAC1B,WAAW,EAAE;qCACqB;SAClC;QACD;YACC,IAAI,EAAE,iBAAiB;YACvB,WAAW,EAAE;;;;QAIR;SACL;QACD;YACC,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE;;;;;6BAKa;SAC1B;QACD;YACC,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;YACnB,WAAW,EAAE,kEAAkE;SAC/E;KACD;CACD,CAAC;AAEF,kBAAe,cAAc,CAAC", "file": "less.js", "sourceRoot": "../../../src/"}