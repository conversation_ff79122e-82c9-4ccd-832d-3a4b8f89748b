{"version": 3, "sources": ["tsServer/serverProcess.browser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;gGAGgG;AAChG,iCAAiC;AACjC,6DAAoE;AACpE,+DAAgE;AAChE,+CAAiC;AAGjC,gDAAgE;AAChE,+DAA2D;AAqB3D,MAAa,0BAA0B;IACtC,YACkB,aAAyB,EACzB,OAAe;QADf,kBAAa,GAAb,aAAa,CAAY;QACzB,YAAO,GAAP,OAAO,CAAQ;IAC7B,CAAC;IAEE,IAAI,CACV,OAA0B,EAC1B,IAAuB,EACvB,IAAyB,EACzB,aAA6C,EAC7C,eAAyC,EACzC,mBAAuC,EACvC,WAAoC;QAEpC,MAAM,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QAC1C,MAAM,UAAU,GAAG;YAClB,GAAG,IAAI;YACP,oEAAoE;YACpE,qBAAqB,EAAE,YAAY;YACnC,sCAAsC;YACtC,CAAC,aAAa,CAAC,yBAAyB,IAAI,IAAA,sCAA2B,GAAE,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC,qCAAqC,CAAC;SACpJ,CAAC;QAEF,OAAO,IAAI,mBAAmB,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/G,CAAC;CACD;AA1BD,gEA0BC;AAED,MAAM,mBAAmB;IAoBxB,YACkB,IAAyB,EAC1C,YAAoB,EACpB,YAAwB,EACxB,IAAuB,EACN,WAAoC,EACrD,MAAc;QALG,SAAI,GAAJ,IAAI,CAAqB;QAIzB,gBAAW,GAAX,WAAW,CAAyB;QArBrC,OAAE,GAAG,mBAAmB,CAAC,MAAM,EAAE,CAAC;QAElC,oBAAe,GAAG,IAAI,GAAG,EAAkC,CAAC;QAC5D,qBAAgB,GAAG,IAAI,GAAG,EAAwB,CAAC;QACnD,oBAAe,GAAG,IAAI,GAAG,EAAwD,CAAC;QAoBlG,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE,EAAE,IAAI,EAAE,MAAM,IAAI,YAAY,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAEnF,IAAI,CAAC,QAAQ,GAAG,IAAI,wCAAkB,CAAC,MAAM,CAAC,CAAC;QAE/C,MAAM,eAAe,GAAG,IAAI,cAAc,EAAE,CAAC;QAC7C,MAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;QAC5C,MAAM,WAAW,GAAG,IAAI,cAAc,EAAE,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC;QAEjC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;YACpC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAC/B,OAAO,CAAC,KAAK,CAAC,+CAA+C,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACtF,OAAO;YACR,CAAC;YACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC5C,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACrB,CAAC;QACF,CAAC,CAAC;QAEF,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,KAAsC,EAAE,EAAE;YACpE,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACzB,KAAK,SAAS,CAAC,CAAC,CAAC;oBAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACpC,MAAM;gBACP,CAAC;gBACD,KAAK,gBAAgB,CAAC;gBACtB,KAAK,WAAW,CAAC,CAAC,CAAC;oBAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,mBAAmB,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE;wBACtH,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;wBACjF,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;wBACjF,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC;qBACjF,CAAC,CAAC;oBACH,MAAM;gBACP,CAAC;gBACD;oBACC,OAAO,CAAC,KAAK,CAAC,0CAA0C,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACnF,CAAC;QACF,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,GAAQ,EAAE,EAAE;YACrC,mBAAmB;YACnB,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAC7B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9B,OAAO;YACR,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,uCAAuC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7E,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,GAAe,EAAE,EAAE;YAC1C,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/C,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC7C,oHAAoH;gBACpH,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;QACF,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,WAAW,CACvB,EAAE,IAAI,EAAE,YAAY,EAAE,EACtB,CAAC,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,CAChE,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,2BAAiB,CAAW,WAAW,CAAC,KAAK,CAAC,CAAC;QACtE,IAAI,6BAAU,CAAC,wBAAwB,EAAE,UAAU,CAAC,CAAC;QACrD,UAAU,CAAC,WAAW,EAAE,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,aAA4B;QACjC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,OAA2C;QACjD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,CAAC,OAA6B;QACpC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,OAA6D;QACnE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClC,wBAAwB;IACzB,CAAC;IAED,IAAI;QACH,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACzB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;IACzB,CAAC;IAEO,SAAS,CAAC,GAAW;QAC5B,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC;QAC1E,CAAC;IACF,CAAC;;AA3Hc,0BAAM,GAAG,CAAH,AAAI,CAAC", "file": "serverProcess.browser.js", "sourceRoot": "../../src/"}