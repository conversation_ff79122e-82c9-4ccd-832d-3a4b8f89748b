{"version": 3, "sources": ["tsServer/spawner.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,kEAA6H;AAK7H,4DAAwF;AACxF,8CAA2C;AAC3C,gDAAyE;AACzE,+BAA4B;AAI5B,qCAAuL;AAmBvL,MAAa,uBAAuB;IAG5B,AAAO,MAAD,KAAK,wBAAwB;QACzC,OAAO,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,CAAC,CAAC,CAAC;IAClF,CAAC;IAED,YACkB,gBAA4C,EAC5C,eAAyC,EACzC,mBAAuC,EACvC,qBAA4C,EAC5C,oBAAmD,EACnD,OAAe,EACf,kBAAqC,EACrC,OAAe,EACf,QAAgC;QARhC,qBAAgB,GAAhB,gBAAgB,CAA4B;QAC5C,oBAAe,GAAf,eAAe,CAA0B;QACzC,wBAAmB,GAAnB,mBAAmB,CAAoB;QACvC,0BAAqB,GAArB,qBAAqB,CAAuB;QAC5C,yBAAoB,GAApB,oBAAoB,CAA+B;QACnD,YAAO,GAAP,OAAO,CAAQ;QACf,uBAAkB,GAAlB,kBAAkB,CAAmB;QACrC,YAAO,GAAP,OAAO,CAAQ;QACf,aAAQ,GAAR,QAAQ,CAAwB;IAC9C,CAAC;IAEE,KAAK,CACX,OAA0B,EAC1B,YAAgC,EAChC,aAA6C,EAC7C,aAA4B,EAC5B,gBAAgD,EAChD,QAA0B;QAE1B,IAAI,aAAgC,CAAC;QACrC,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC;QACrF,MAAM,kCAAkC,GAAG,IAAI,CAAC,kCAAkC,CAAC,aAAa,CAAC,CAAC;QAElG,QAAQ,UAAU,EAAE,CAAC;YACpB,gDAAwC;YACxC;gBACC,CAAC;oBACA,MAAM,oBAAoB,GAAG,CAAC,kCAAkC,IAAI,UAAU,sDAA8C,CAAC;oBAC7H,aAAa,GAAG,IAAI,8BAAqB,CAAC;wBACzC,MAAM,EAAE,IAAI,CAAC,aAAa,4CAA6B,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC;wBAC/G,QAAQ,EAAE,IAAI,CAAC,aAAa,gDAA+B,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC;qBACnH,EAAE,QAAQ,EAAE,oBAAoB,CAAC,CAAC;oBACnC,MAAM;gBACP,CAAC;YACF;gBACC,CAAC;oBACA,aAAa,GAAG,IAAI,CAAC,aAAa,wCAA2B,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;oBACtH,MAAM;gBACP,CAAC;YACF;gBACC,CAAC;oBACA,aAAa,GAAG,IAAI,CAAC,aAAa,4CAA6B,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;oBACxH,MAAM;gBACP,CAAC;QACH,CAAC;QAED,IAAI,kCAAkC,EAAE,CAAC;YACxC,OAAO,IAAI,8BAAqB,CAAC;gBAChC,MAAM,EAAE,IAAI,CAAC,aAAa,sDAAkC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,gBAAgB,CAAC;gBACpH,OAAO,EAAE,aAAa;aACtB,EAAE,QAAQ,CAAC,CAAC;QACd,CAAC;QAED,OAAO,aAAa,CAAC;IACtB,CAAC;IAEO,sBAAsB,CAC7B,OAA0B,EAC1B,YAAgC,EAChC,aAA6C;QAE7C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,oCAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClD,8CAAsC;QACvC,CAAC;QAED,QAAQ,aAAa,CAAC,eAAe,EAAE,CAAC;YACvC;gBACC,8CAAsC;YAEvC;gBACC,0CAAkC;YAEnC;gBACC,OAAO,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC;oBACvC,CAAC;oBACD,CAAC,2CAAmC,CAAC;QACxC,CAAC;IACF,CAAC;IAEO,kCAAkC,CACzC,aAA6C;QAE7C,OAAO,aAAa,CAAC,wBAAwB,CAAC;IAC/C,CAAC;IAEO,aAAa,CACpB,IAAyB,EACzB,OAA0B,EAC1B,aAA6C,EAC7C,aAA4B,EAC5B,gBAAgD;QAEhD,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,SAAG,CAAC,cAAc,CAAC;QAE5D,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC9D,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,sBAAsB,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAEpK,IAAI,uBAAuB,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7D,IAAI,WAAW,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,eAAe,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YACpE,CAAC;iBAAM,IAAI,WAAW,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC3C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,qBAAqB,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,kCAAkC,CAAC,CAAC;YAChE,CAAC;QACF,CAAC;QAED,IAAI,aAAa,CAAC,qBAAqB,EAAE,CAAC;YACzC,IAAI,sBAAsB,EAAE,CAAC;gBAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,sBAAsB,sBAAsB,CAAC,MAAM,EAAE,CAAC,CAAC;YAClF,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,oCAAoC,CAAC,CAAC;YAClE,CAAC;QACF,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,cAAc,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;QACpI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,eAAe,CAAC,CAAC;QAE3C,OAAO,IAAI,uBAAc,CACxB,IAAI,EACJ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAC3B,OAAQ,EACR,WAAW,EACX,SAAS,EACT,OAAO,EACP,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,OAAO,CAAC,CAAC;IAChB,CAAC;IAEO,gBAAgB,CAAC,IAAyB;QACjD,QAAQ,IAAI,EAAE,CAAC;YACd;gBACC,OAAO,8BAAU,CAAC,MAAM,CAAC;YAE1B,2CAA8B;YAC9B,mDAAkC;YAClC,yDAAqC;YACrC;gBACC,OAAO,8BAAU,CAAC,QAAQ,CAAC;QAC7B,CAAC;IACF,CAAC;IAEO,eAAe,CACtB,IAAyB,EACzB,aAA6C,EAC7C,cAAiC,EACjC,UAAe,EACf,aAA4B,EAC5B,oBAAwC;QAExC,MAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,IAAI,WAAoC,CAAC;QACzC,IAAI,sBAA8C,CAAC;QAEnD,IAAI,IAAI,8CAA+B,EAAE,CAAC;YACzC,IAAI,UAAU,CAAC,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC3B,CAAC;QACF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAEhD,IAAI,aAAa,CAAC,+BAA+B,IAAI,IAAI,8CAA+B,IAAI,IAAI,wDAAoC,EAAE,CAAC;YACtI,IAAI,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,kDAAiC,IAAI,IAAI,0CAA6B,EAAE,CAAC;YAChF,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAChC,CAAC;QAED,IAAI,oBAAoB,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,oBAAoB,GAAG,GAAG,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,uBAAuB,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YAC7D,IAAI,IAAA,gBAAK,GAAE,EAAE,CAAC;gBACb,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,gCAAgB,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACvF,WAAW,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,uBAAuB,CAAC,wBAAwB,EAAE,CAAC;YAC5F,CAAC;iBAAM,CAAC;gBACP,MAAM,MAAM,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;gBAC/D,IAAI,MAAM,EAAE,CAAC;oBACZ,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;oBAChE,WAAW,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC;oBAEjD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,gCAAgB,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;oBACvF,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;gBAC5C,CAAC;YACF,CAAC;QACF,CAAC;QAED,IAAI,aAAa,CAAC,qBAAqB,IAAI,CAAC,IAAA,gBAAK,GAAE,EAAE,CAAC;YACrD,sBAAsB,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,CAAC;YACzE,IAAI,sBAAsB,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;YACrE,CAAC;QACF,CAAC;QAED,MAAM,WAAW,GAAG,IAAA,gBAAK,GAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,cAAc,EAAE,CAAC;QAE9E,IAAI,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAE/E,MAAM,+BAA+B,GAAG,cAAc,CAAC,IAAI,KAAK,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC;YAC1G,KAAK,MAAM,MAAM,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;gBAC5C,IAAI,+BAA+B,IAAI,MAAM,CAAC,oCAAoC,EAAE,CAAC;oBACpF,WAAW,CAAC,IAAI,CAAC,IAAA,gBAAK,GAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACvE,CAAC;YACF,CAAC;QACF,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,aAAa,CAAC,WAAW,IAAI,CAAC,IAAA,gBAAK,GAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,aAAa,CAAC,WAAW,GAAG,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,uBAAuB,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE1C,MAAM,sBAAsB,GAAG,aAAa,CAAC,gBAAgB,CAAC;QAC9D,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;QACzC,IACC,UAAU,CAAC,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC;eACrB,sBAAsB;eACtB,CAAC,SAAS,CAAC,uEAAuE;UACpF,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACP,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,gFAAgF,CAAC,CAAC;YAC7G,CAAC;iBAAM,IAAI,SAAS,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,2EAA2E,CAAC,CAAC;YACxG,CAAC;QACF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE1C,IAAI,IAAA,wCAA6B,GAAE,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,sBAAsB,EAAE,CAAC;IACtD,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,aAA6C;QAC5E,OAAO,aAAa,CAAC,gBAAgB,KAAK,gCAAgB,CAAC,GAAG,CAAC;IAChE,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,aAA6C;QACvE,OAAO,aAAa,CAAC,MAAM;YAC1B,CAAC,CAAC,aAAa,CAAC,MAAM;YACtB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC;IACxB,CAAC;CACD;AA3QD,0DA2QC;AAxQc;IADb,iBAAO;6DAGP", "file": "spawner.js", "sourceRoot": "../../src/"}