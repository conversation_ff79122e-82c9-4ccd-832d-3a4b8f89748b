{"version": 3, "sources": ["tsServer/protocol/errorCodes.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAEnF,QAAA,4BAA4B,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACrD,QAAA,8BAA8B,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,QAAA,mBAAmB,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACtC,QAAA,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAClC,QAAA,WAAW,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9B,QAAA,uBAAuB,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1C,QAAA,2BAA2B,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9C,QAAA,8BAA8B,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,QAAA,cAAc,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACvC,QAAA,yBAAyB,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5C,QAAA,gCAAgC,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC", "file": "errorCodes.js", "sourceRoot": "../../../src/"}