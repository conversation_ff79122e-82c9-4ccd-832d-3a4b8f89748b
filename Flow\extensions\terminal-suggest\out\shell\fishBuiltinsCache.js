"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.fishBuiltinsCommandDescriptionsCache = void 0;
exports.fishBuiltinsCommandDescriptionsCache = {
    ".": {
        "shortDescription": "source - evaluate contents of file",
        "description": "source evaluates the commands of the specified FILE in the current\nshell as a new block of code. This is different from starting a new\nprocess to perform the commands (i.e. fish < FILE) since the commands\nwill be evaluated by the current shell, which means that changes in\nshell variables will affect the current shell. If additional\narguments are specified after the file name, they will be inserted\ninto the argv variable. The argv variable will not include the name\nof the sourced file.\n\nfish will search the working directory to resolve relative paths but\nwill not search PATH .\n\nIf no file is specified and stdin is not the terminal, or if the file\nname - is used, stdin will be read.\n\nThe exit status of source is the exit status of the last job to\nexecute. If something goes wrong while opening or reading the file,\nsource exits with a non-zero status.\n\n. (a single period) is an alias for the source command. The use of .\nis deprecated in favour of source, and . will be removed in a future\nversion of fish.\n\nsource creates a new local scope; set --local within a sourced block\nwill not affect variables in the enclosing scope.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\n\n   source ~/.config/fish/config.fish\n   # Causes fish to re-read its initialization file.\n\nCAVEATS\nIn fish versions prior to 2.3.0, the argv variable would have a\nsingle element (the name of the sourced file) if no arguments are\npresent. Otherwise, it would contain arguments without the name of\nthe sourced file. That behavior was very confusing and unlike other\nshells such as bash and zsh.",
        "args": "source FILE [ARGUMENTS ...]\nSOMECOMMAND | source"
    },
    ":": {
        "shortDescription": "No operation command",
        "description": "The `:` command is a no-op (no operation) command that returns a successful (zero) exit status. It can be used as a placeholder in scripts where a command is syntactically required but no action is desired."
    },
    "[": {
        "shortDescription": "Test if a statement is true",
        "description": "Evaluate an expression and return a status of true (0) or false (non-zero). Unlike the `test` command, the `[` command requires a closing `]`.",
        "args": "EXPRESSION ]"
    },
    "_": {
        "shortDescription": "",
        "description": ""
    },
    "abbr": {
        "shortDescription": "manage fish abbreviations",
        "description": "abbr manages abbreviations - user-defined words that are replaced\nwith longer phrases when entered.\n\nNOTE:\n   Only typed-in commands use abbreviations. Abbreviations are not\n   expanded in scripts.\n\nFor example, a frequently-run command like git checkout can be\nabbreviated to gco.  After entering gco and pressing Space or Enter,\nthe full text git checkout will appear in the command line.  To avoid\nexpanding something that looks like an abbreviation, the default\nControl+Space binding inserts a space without expanding.\n\nAn abbreviation may match a literal word, or it may match a pattern\ngiven by a regular expression. When an abbreviation matches a word,\nthat word is replaced by new text, called its expansion. This\nexpansion may be a fixed new phrase, or it can be dynamically created\nvia a fish function. This expansion occurs after pressing space or\nenter.\n\nCombining these features, it is possible to create custom syntaxes,\nwhere a regular expression recognizes matching tokens, and the\nexpansion function interprets them. See the Examples section.\n\nChanged in version 3.6.0: Previous versions of this allowed saving\nabbreviations in universal variables.  That's no longer possible.\nExisting variables will still be imported and abbr --erase will also\nerase the variables.  We recommend adding abbreviations to\nconfig.fish by just adding the abbr --add command.  When you run\nabbr, you will see output like this\n\n   > abbr\n   abbr -a -- foo bar # imported from a universal variable, see `help abbr`\n\nIn that case you should take the part before the # comment and save\nit in config.fish, then you can run abbr --erase to remove the\nuniversal variable:\n\n   > abbr >> ~/.config/fish/config.fish\n   > abbr --erase (abbr --list)\n\nADD SUBCOMMAND\nabbr [-a | --add] NAME [--position command | anywhere] [-r | --regex PATTERN]\n     [--set-cursor[=MARKER]] ([-f | --function FUNCTION] | EXPANSION)\n\nabbr --add creates a new abbreviation. With no other options, the\nstring NAME is replaced by EXPANSION.\n\nWith --position command, the abbreviation will only expand when it is\npositioned as a command, not as an argument to another command. With\n--position anywhere the abbreviation may expand anywhere in the\ncommand line. The default is command.\n\nWith --regex, the abbreviation matches using the regular expression\ngiven by PATTERN, instead of the literal NAME. The pattern is\ninterpreted using PCRE2 syntax and must match the entire token. If\nmultiple abbreviations match the same token, the last abbreviation\nadded is used.\n\nWith --set-cursor=MARKER, the cursor is moved to the first occurrence\nof MARKER in the expansion. The MARKER value is erased. The MARKER\nmay be omitted (i.e. simply --set-cursor), in which case it defaults\nto %.\n\nWith -f FUNCTION or --function FUNCTION, FUNCTION is treated as the\nname of a fish function instead of a literal replacement. When the\nabbreviation matches, the function will be called with the matching\ntoken as an argument. If the function's exit status is 0 (success),\nthe token will be replaced by the function's output; otherwise the\ntoken will be left unchanged. No EXPANSION may be given separately.\n\n   Examples\n\n   abbr --add gco git checkout\n\nAdd a new abbreviation where gco will be replaced with git checkout.\n\n   abbr -a --position anywhere -- -C --color\n\nAdd a new abbreviation where -C will be replaced with --color. The --\nallows -C to be treated as the name of the abbreviation, instead of\nan option.\n\n   abbr -a L --position anywhere --set-cursor \"% | less\"\n\nAdd a new abbreviation where L will be replaced with | less, placing\nthe cursor before the pipe.\n\n   function last_history_item\n       echo $history[1]\n   end\n   abbr -a !! --position anywhere --function last_history_item\n\nThis first creates a function last_history_item which outputs the\nlast entered command. It then adds an abbreviation which replaces !!\nwith the result of calling this function. Taken together, this is\nsimilar to the !! history expansion feature of bash.\n\n   function vim_edit\n       echo vim $argv\n   end\n   abbr -a vim_edit_texts --position command --regex \".+\\.txt\" --function vim_edit\n\nThis first creates a function vim_edit which prepends vim before its\nargument. It then adds an abbreviation which matches commands ending\nin .txt, and replaces the command with the result of calling this\nfunction. This allows text files to be \"executed\" as a command to\nopen them in vim, similar to the \"suffix alias\" feature in zsh.\n\n   abbr 4DIRS --set-cursor=! \"$(string join \\n -- 'for dir in */' 'cd $dir' '!' 'cd ..' 'end')\"\n\nThis creates an abbreviation \"4DIRS\" which expands to a multi-line\nloop \"template.\" The template enters each directory and then leaves\nit. The cursor is positioned ready to enter the command to run in\neach directory, at the location of the !, which is itself erased.\n\nOTHER SUBCOMMANDS\n\n   abbr --rename OLD_NAME NEW_NAME\n\nRenames an abbreviation, from OLD_NAME to NEW_NAME\n\n   abbr [-s | --show]\n\nShow all abbreviations in a manner suitable for import and export\n\n   abbr [-l | --list]\n\nPrints the names of all abbreviation\n\n   abbr [-e | --erase] NAME\n\nErases the abbreviation with the given name\n\n   abbr -q or --query [NAME...]\n\nReturn 0 (true) if one of the NAME is an abbreviation.\n\n   abbr -h or --help\n\nDisplays help for the abbr command.",
        "args": "abbr --add NAME [--position command | anywhere] [-r | --regex PATTERN]\n[--set-cursor[=MARKER]] ([-f | --function FUNCTION] | EXPANSION)\nabbr --erase NAME ...\nabbr --rename OLD_WORD NEW_WORD\nabbr --show\nabbr --list\nabbr --query NAME ..."
    },
    "and": {
        "shortDescription": "conditionally execute a command",
        "description": "and is used to execute a command if the previous command was\nsuccessful (returned a status of 0).\n\nand statements may be used as part of the condition in an while or if\nblock.\n\nand does not change the current exit status itself, but the command\nit runs most likely will. The exit status of the last foreground\ncommand to exit can always be accessed using the $status variable.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\nThe following code runs the make command to build a program. If the\nbuild succeeds, make's exit status is 0, and the program is\ninstalled. If either step fails, the exit status is 1, and make clean\nis run, which removes the files created by the build process.\n\n   make; and make install; or make clean\n\nSEE ALSO\n\n• or command\n\n• not command",
        "args": "PREVIOUS; and COMMAND"
    },
    "argparse": {
        "shortDescription": "parse options passed to a fish script or function",
        "description": "This command makes it easy for fish scripts and functions to handle\narguments. You pass arguments that define the known options, followed\nby a literal --, then the arguments to be parsed (which might also\ninclude a literal --). argparse then sets variables to indicate the\npassed options with their values, and sets $argv to the remaining\narguments. See the usage section below.\n\nEach option specification (OPTION_SPEC) is written in the domain\nspecific language described below. All OPTION_SPECs must appear after\nany argparse flags and before the -- that separates them from the\narguments to be parsed.\n\nEach option that is seen in the ARG list will result in variables\nnamed _flag_X, where X is the short flag letter and the long flag\nname (if they are defined). For example a --help option could cause\nargparse to define one variable called _flag_h and another called\n_flag_help.\n\nThe variables will be set with local scope (i.e., as if the script\nhad done set -l _flag_X). If the flag is a boolean (that is, it just\nis passed or not, it doesn't have a value) the values are the short\nand long flags seen. If the option is not a boolean the values will\nbe zero or more values corresponding to the values collected when the\nARG list is processed. If the flag was not seen the flag variable\nwill not be set.\n\nOPTIONS\nThe following argparse options are available. They must appear before\nall OPTION_SPECs:\n\n-n or --name\n       The command name for use in error messages. By default the\n       current function name will be used, or argparse if run outside\n       of a function.\n\n-x or --exclusive OPTIONS\n       A comma separated list of options that are mutually exclusive.\n       You can use this more than once to define multiple sets of\n       mutually exclusive options.  You give either the short or long\n       version of each option, and you still need to otherwise define\n       the options.\n\n-N or --min-args NUMBER\n       The minimum number of acceptable non-option arguments. The\n       default is zero.\n\n-X or --max-args NUMBER\n       The maximum number of acceptable non-option arguments. The\n       default is infinity.\n\n-i or --ignore-unknown\n       Ignores unknown options, keeping them and their arguments in\n       $argv instead.\n\n-s or --stop-nonopt\n       Causes scanning the arguments to stop as soon as the first\n       non-option argument is seen. Among other things, this is\n       useful to implement subcommands that have their own options.\n\n-h or --help\n       Displays help about using this command.\n\nUSAGE\nTo use this command, pass the option specifications (OPTION_SPEC), a\nmandatory --, and then the arguments to be parsed.\n\nA simple example:\n\n   argparse --name=my_function 'h/help' 'n/name=' -- $argv\n   or return\n\nIf $argv is empty then there is nothing to parse and argparse returns\nzero to indicate success. If $argv is not empty then it is checked\nfor flags -h, --help, -n and --name. If they are found they are\nremoved from the arguments and local variables called _flag_OPTION\nare set so the script can determine which options were seen. If $argv\ndoesn't have any errors, like a missing mandatory value for an\noption, then argparse exits with a status of zero. Otherwise it\nwrites appropriate error messages to stderr and exits with a status\nof one.\n\nThe or return means that the function returns argparse's status if it\nfailed, so if it goes on argparse succeeded.\n\nThe -- argument is required. You do not have to include any option\nspecifications or arguments after the -- but you must include the --.\nFor example, this is acceptable:\n\n   set -l argv foo\n   argparse 'h/help' 'n/name' -- $argv\n   argparse --min-args=1 -- $argv\n\nBut this is not:\n\n   set -l argv\n   argparse 'h/help' 'n/name' $argv\n\nThe first -- seen is what allows the argparse command to reliably\nseparate the option specifications and options to argparse itself\n(like --ignore-unknown) from the command arguments, so it is\nrequired.\n\nOPTION SPECIFICATIONS\nEach option specification consists of:\n\n• An optional alphanumeric short flag character, followed by a / if\n  the short flag can be used by someone invoking your command or, for\n  backwards compatibility, a - if it should not be exposed as a valid\n  short flag (in which case it will also not be exposed as a flag\n  variable).\n\n• An optional long flag name, which if not present the short flag can\n  be used, and if that is also not present, an error is reported\n\n• Nothing if the flag is a boolean that takes no argument or is an\n  integer flag, or\n\n     • = if it requires a value and only the last instance of the\n       flag is saved, or\n\n     • =? if it takes an optional value and only the last instance of\n       the flag is saved, or\n\n     • =+ if it requires a value and each instance of the flag is\n       saved.\n\n• Optionally a ! followed by fish script to validate the value.\n  Typically this will be a function to run. If the exit status is\n  zero the value for the flag is valid. If non-zero the value is\n  invalid. Any error messages should be written to stdout (not\n  stderr). See the section on Flag Value Validation for more\n  information.\n\nSee the fish_opt command for a friendlier but more verbose way to\ncreate option specifications.\n\nIf a flag is not seen when parsing the arguments then the\ncorresponding _flag_X var(s) will not be set.\n\nINTEGER FLAG\nSometimes commands take numbers directly as options, like foo -55. To\nallow this one option spec can have the # modifier so that any\ninteger will be understood as this flag, and the last number will be\ngiven as its value (as if = was used).\n\nThe # must follow the short flag letter (if any), and other modifiers\nlike = are not allowed, except for - (for backwards compatibility):\n\n   m#maximum\n\nThis does not read numbers given as +NNN, only those that look like\nflags - -NNN.\n\nNOTE: OPTIONAL ARGUMENTS\nAn option defined with =? can take optional arguments. Optional\narguments have to be directly attached to the option they belong to.\n\nThat means the argument will only be used for the option if you use\nit like:\n\n   cmd --flag=value\n   # or\n   cmd  -fvalue\n\nbut not if used like:\n\n   cmd --flag value\n   # \"value\" here will be used as a positional argument\n   # and \"--flag\" won't have an argument.\n\nIf this weren't the case, using an option without an optional\nargument would be difficult if you also wanted to use positional\narguments.\n\nFor example:\n\n   grep --color auto\n   # Here \"auto\" will be used as the search string,\n   # \"color\" will not have an argument and will fall back to the default,\n   # which also *happens to be* auto.\n   grep --color always\n   # Here grep will still only use color \"auto\"matically\n   # and search for the string \"always\".\n\nThis isn't specific to argparse but common to all things using\ngetopt(3) (if they have optional arguments at all). That grep example\nis how GNU grep actually behaves.\n\nFLAG VALUE VALIDATION\nSometimes you need to validate the option values. For example, that\nit is a valid integer within a specific range, or an ip address, or\nsomething entirely different. You can always do this after argparse\nreturns but you can also request that argparse perform the validation\nby executing arbitrary fish script. To do so simply append an !\n(exclamation-mark) then the fish script to be run. When that code is\nexecuted three vars will be defined:\n\n• _argparse_cmd will be set to the value of the value of the argparse\n  --name value.\n\n• _flag_name will be set to the short or long flag that being\n  processed.\n\n• _flag_value will be set to the value associated with the flag being\n  processed.\n\nThese variables are passed to the function as local exported\nvariables.\n\nThe script should write any error messages to stdout, not stderr. It\nshould return a status of zero if the flag value is valid otherwise a\nnon-zero status to indicate it is invalid.\n\nFish ships with a _validate_int function that accepts a --min and\n--max flag. Let's say your command accepts a -m or --max flag and the\nminimum allowable value is zero and the maximum is 5. You would\ndefine the option like this: m/max=!_validate_int --min 0 --max 5.\nThe default if you just call _validate_int without those flags is to\nsimply check that the value is a valid integer with no limits on the\nmin or max value allowed.\n\nHere are some examples of flag validations:\n\n   # validate that a path is a directory\n   argparse 'p/path=!test -d \"$_flag_value\"' -- --path $__fish_config_dir\n   # validate that a function does not exist\n   argparse 'f/func=!not functions -q \"$_flag_value\"' -- -f alias\n   # validate that a string matches a regex\n   argparse 'c/color=!string match -rq \\'^#?[0-9a-fA-F]{6}$\\' \"$_flag_value\"' -- -c 'c0ffee'\n   # validate with a validator function\n   argparse 'n/num=!_validate_int --min 0 --max 99' -- --num 42\n\nEXAMPLE OPTION_SPECS\nSome OPTION_SPEC examples:\n\n• h/help means that both -h and --help are valid. The flag is a\n  boolean and can be used more than once. If either flag is used then\n  _flag_h and _flag_help will be set to however either flag was seen,\n  as many times as it was seen. So it could be set to -h, -h and\n  --help, and count $_flag_h would yield \"3\".\n\n• help means that only --help is valid. The flag is a boolean and can\n  be used more than once. If it is used then _flag_help will be set\n  as above. Also h-help (with an arbitrary short letter) for\n  backwards compatibility.\n\n• longonly= is a flag --longonly that requires an option, there is no\n  short flag or even short flag variable.\n\n• n/name= means that both -n and --name are valid. It requires a\n  value and can be used at most once. If the flag is seen then\n  _flag_n and _flag_name will be set with the single mandatory value\n  associated with the flag.\n\n• n/name=? means that both -n and --name are valid. It accepts an\n  optional value and can be used at most once. If the flag is seen\n  then _flag_n and _flag_name will be set with the value associated\n  with the flag if one was provided else it will be set with no\n  values.\n\n• name=+ means that only --name is valid. It requires a value and can\n  be used more than once. If the flag is seen then _flag_name will be\n  set with the values associated with each occurrence.\n\n• x means that only -x is valid. It is a boolean that can be used\n  more than once. If it is seen then _flag_x will be set as above.\n\n• x=, x=?, and x=+ are similar to the n/name examples above but there\n  is no long flag alternative to the short flag -x.\n\n• #max (or #-max) means that flags matching the regex \"^--?\\d+$\" are\n  valid. When seen they are assigned to the variable _flag_max. This\n  allows any valid positive or negative integer to be specified by\n  prefixing it with a single \"-\". Many commands support this idiom.\n  For example head -3 /a/file to emit only the first three lines of\n  /a/file.\n\n• n#max means that flags matching the regex \"^--?\\d+$\" are valid.\n  When seen they are assigned to the variables _flag_n and _flag_max.\n  This allows any valid positive or negative integer to be specified\n  by prefixing it with a single \"-\". Many commands support this\n  idiom. For example head -3 /a/file to emit only the first three\n  lines of /a/file. You can also specify the value using either flag:\n  -n NNN or --max NNN in this example.\n\n• #longonly causes the last integer option to be stored in\n  _flag_longonly.\n\nAfter parsing the arguments the argv variable is set with local scope\nto any values not already consumed during flag processing. If there\nare no unbound values the variable is set but count $argv will be\nzero.\n\nIf an error occurs during argparse processing it will exit with a\nnon-zero status and print error messages to stderr.\n\nEXAMPLES\nA simple use:\n\n   argparse h/help -- $argv\n   or return\n\n   if set -q _flag_help\n       # TODO: Print help here\n       return 0\n   end\n\nThis just wants one option - -h / --help. Any other option is an\nerror. If it is given it prints help and exits.\n\nHow fish_add_path - add to the path parses its args:\n\n   argparse -x g,U -x P,U -x a,p g/global U/universal P/path p/prepend a/append h/help m/move v/verbose n/dry-run -- $argv\n\nThere are a variety of boolean flags, all with long and short\nversions. A few of these cannot be used together, and that is what\nthe -x flag is used for.  -x g,U means that --global and --universal\nor their short equivalents conflict, and if they are used together\nyou get an error.  In this case you only need to give the short or\nlong flag, not the full option specification.\n\nAfter this it figures out which variable it should operate on\naccording to the --path flag:\n\n   set -l var fish_user_paths\n   set -q _flag_path\n   and set var PATH\n\nLIMITATIONS\nOne limitation with --ignore-unknown is that, if an unknown option is\ngiven in a group with known options, the entire group will be kept in\n$argv. argparse will not do any permutations here.\n\nFor instance:\n\n   argparse --ignore-unknown h -- -ho\n   echo $_flag_h # is -h, because -h was given\n   echo $argv # is still -ho\n\nThis limitation may be lifted in future.\n\nAdditionally, it can only parse known options up to the first unknown\noption in the group - the unknown option could take options, so it\nisn't clear what any character after an unknown option means.",
        "args": "argparse [OPTIONS] OPTION_SPEC ... -- [ARG ...]"
    },
    "begin": {
        "shortDescription": "start a new block of code",
        "description": "begin is used to create a new block of code.\n\nA block allows the introduction of a new variable scope, redirection\nof the input or output of a set of commands as a group, or to specify\nprecedence when using the conditional commands like and.\n\nThe block is unconditionally executed. begin; ...; end is equivalent\nto if true; ...; end.\n\nbegin does not change the current exit status itself. After the block\nhas completed, $status will be set to the status returned by the most\nrecent command.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\nThe following code sets a number of variables inside of a block\nscope. Since the variables are set inside the block and have local\nscope, they will be automatically deleted when the block ends.\n\n   begin\n       set -l PIRATE Yarrr\n\n       ...\n   end\n\n   echo $PIRATE\n   # This will not output anything, since the PIRATE variable\n   # went out of scope at the end of the block\n\nIn the following code, all output is redirected to the file out.html.\n\n   begin\n       echo $xml_header\n       echo $html_header\n       if test -e $file\n           ...\n       end\n       ...\n   end > out.html",
        "args": "begin; [COMMANDS ...]; end"
    },
    "bg": {
        "shortDescription": "send jobs to background",
        "description": "bg sends jobs to the background, resuming them if they are stopped.\n\nA background job is executed simultaneously with fish, and does not\nhave access to the keyboard. If no job is specified, the last job to\nbe used is put in the background. If PID is specified, the jobs\ncontaining the specified process IDs are put in the background.\n\nFor compatibility with other shells, job expansion syntax is\nsupported for bg. A PID of the format %1 will be interpreted as the\nPID of job 1. Job numbers can be seen in the output of jobs.\n\nWhen at least one of the arguments isn't a valid job specifier, bg\nwill print an error without backgrounding anything.\n\nWhen all arguments are valid job specifiers, bg will background all\nmatching jobs that exist.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\nbg *********** will background the jobs that contain processes 123,\n456 and 789.\n\nIf only 123 and 789 exist, it will still background them and print an\nerror about 456.\n\nbg 123 banana or bg banana 123 will complain that \"banana\" is not a\nvalid job specifier.\n\nbg %1 will background job 1.",
        "args": "bg [PID ...]"
    },
    "bind": {
        "shortDescription": "handle fish key bindings",
        "description": "bind manages bindings.\n\nIt can add bindings if given a SEQUENCE of characters to bind to.\nThese should be written as fish escape sequences. The most important\nof these are \\c for the control key, and \\e for escape, and because\nof historical reasons also the Alt key (sometimes also called\n\"Meta\").\n\nFor example, Alt+W can be written as \\ew, and Control+X (^X) can be\nwritten as \\cx. Note that Alt-based key bindings are case sensitive\nand Control-based key bindings are not. This is a constraint of\ntext-based terminals, not fish.\n\nThe generic key binding that matches if no other binding does can be\nset by specifying a SEQUENCE of the empty string (that is, '' ). For\nmost key bindings, it makes sense to bind this to the self-insert\nfunction (i.e. bind '' self-insert). This will insert any keystrokes\nnot specifically bound to into the editor. Non-printable characters\nare ignored by the editor, so this will not result in control\nsequences being inserted.\n\nIf the -k switch is used, the name of a key (such as 'down', 'up' or\n'backspace') is used instead of a sequence. The names used are the\nsame as the corresponding curses variables, but without the 'key'\nprefix. (See terminfo(5) for more information, or use bind\n--key-names for a list of all available named keys). Normally this\nwill print an error if the current $TERM entry doesn't have a given\nkey, unless the -s switch is given.\n\nTo find out what sequence a key combination sends, you can use\nfish_key_reader.\n\nCOMMAND can be any fish command, but it can also be one of a set of\nspecial input functions. These include functions for moving the\ncursor, operating on the kill-ring, performing tab completion, etc.\nUse bind --function-names for a complete list of these input\nfunctions.\n\nWhen COMMAND is a shellscript command, it is a good practice to put\nthe actual code into a function and simply bind to the function name.\nThis way it becomes significantly easier to test the function while\nediting, and the result is usually more readable as well.\n\nNOTE:\n   Special input functions cannot be combined with ordinary shell\n   script commands. The commands must be entirely a sequence of\n   special input functions (from bind -f) or all shell script\n   commands (i.e., valid fish script). To run special input functions\n   from regular fish script, use commandline -f (see also\n   commandline). If a script produces output, it should finish by\n   calling commandline -f repaint to tell fish that a repaint is in\n   order.\n\nIf no SEQUENCE is provided, all bindings (or just the bindings in the\ngiven MODE) are printed. If SEQUENCE is provided but no COMMAND, just\nthe binding matching that sequence is printed.\n\nTo save custom key bindings, put the bind statements into\nconfig.fish. Alternatively, fish also automatically executes a\nfunction called fish_user_key_bindings if it exists.\n\nKey bindings may use \"modes\", which mimics Vi's modal input behavior.\nThe default mode is \"default\". Every key binding applies to a single\nmode; you can specify which one with -M MODE. If the key binding\nshould change the mode, you can specify the new mode with -m\nNEW_MODE. The mode can be viewed and changed via the $fish_bind_mode\nvariable. If you want to change the mode from inside a fish function,\nuse set fish_bind_mode MODE.\n\nOPTIONS\nThe following options are available:\n\n-k or --key\n       Specify a key name, such as 'left' or 'backspace' instead of a\n       character sequence\n\n-K or --key-names\n       Display a list of available key names. Specifying -a or --all\n       includes keys that don't have a known mapping\n\n-f or --function-names\n       Display a list of available input functions\n\n-L or --list-modes\n       Display a list of defined bind modes\n\n-M MODE or --mode MODE\n       Specify a bind mode that the bind is used in. Defaults to\n       \"default\"\n\n-m NEW_MODE or --sets-mode NEW_MODE\n       Change the current mode to NEW_MODE after this binding is\n       executed\n\n-e or --erase\n       Erase the binding with the given sequence and mode instead of\n       defining a new one.  Multiple sequences can be specified with\n       this flag.  Specifying -a or --all with -M or --mode erases\n       all binds in the given mode regardless of sequence.\n       Specifying -a or --all without -M or --mode erases all binds\n       in all modes regardless of sequence.\n\n-a or --all\n       See --erase and --key-names\n\n--preset and --user\n       Specify if bind should operate on user or preset bindings.\n       User bindings take precedence over preset bindings when fish\n       looks up mappings.  By default, all bind invocations work on\n       the \"user\" level except for listing, which will show both\n       levels.  All invocations except for inserting new bindings can\n       operate on both levels at the same time (if both --preset and\n       --user are given).  --preset should only be used in full\n       binding sets (like when working on fish_vi_key_bindings).\n\n-s or --silent\n       Silences some of the error messages, including for unknown key\n       names and unbound sequences.\n\n-h or --help\n       Displays help about using this command.\n\nSPECIAL INPUT FUNCTIONS\nThe following special input functions are available:\n\nand    only execute the next function if the previous succeeded\n       (note: only some functions report success)\n\naccept-autosuggestion\n       accept the current autosuggestion\n\nbackward-char\n       move one character to the left.  If the completion pager is\n       active, select the previous completion instead.\n\nbackward-bigword\n       move one whitespace-delimited word to the left\n\nbackward-delete-char\n       deletes one character of input to the left of the cursor\n\nbackward-kill-bigword\n       move the whitespace-delimited word to the left of the cursor\n       to the killring\n\nbackward-kill-line\n       move everything from the beginning of the line to the cursor\n       to the killring\n\nbackward-kill-path-component\n       move one path component to the left of the cursor to the\n       killring. A path component is everything likely to belong to a\n       path component, i.e. not any of the following: /={,}'\":@\n       |;<>&, plus newlines and tabs.\n\nbackward-kill-word\n       move the word to the left of the cursor to the killring. The\n       \"word\" here is everything up to punctuation or whitespace.\n\nbackward-word\n       move one word to the left\n\nbeginning-of-buffer\n       moves to the beginning of the buffer, i.e. the start of the\n       first line\n\nbeginning-of-history\n       move to the beginning of the history\n\nbeginning-of-line\n       move to the beginning of the line\n\nbegin-selection\n       start selecting text\n\ncancel cancel the current commandline and replace it with a new empty\n       one\n\ncancel-commandline\n       cancel the current commandline and replace it with a new empty\n       one, leaving the old one in place with a marker to show that\n       it was cancelled\n\ncapitalize-word\n       make the current word begin with a capital letter\n\nclear-screen\n       clears the screen and redraws the prompt. if the terminal\n       doesn't support clearing the screen it is the same as repaint.\n\ncomplete\n       guess the remainder of the current token\n\ncomplete-and-search\n       invoke the searchable pager on completion options (for\n       convenience, this also moves backwards in the completion\n       pager)\n\ndelete-char\n       delete one character to the right of the cursor\n\ndelete-or-exit\n       delete one character to the right of the cursor, or exit the\n       shell if the commandline is empty\n\ndown-line\n       move down one line\n\ndowncase-word\n       make the current word lowercase\n\nend-of-buffer\n       moves to the end of the buffer, i.e. the end of the first line\n\nend-of-history\n       move to the end of the history\n\nend-of-line\n       move to the end of the line\n\nend-selection\n       end selecting text\n\nexpand-abbr\n       expands any abbreviation currently under the cursor\n\nexecute\n       run the current commandline\n\nexit   exit the shell\n\nforward-bigword\n       move one whitespace-delimited word to the right\n\nforward-char\n       move one character to the right; or if at the end of the\n       commandline, accept the current autosuggestion.  If the\n       completion pager is active, select the next completion\n       instead.\n\nforward-single-char\n       move one character to the right; or if at the end of the\n       commandline, accept a single char from the current\n       autosuggestion.\n\nforward-word\n       move one word to the right; or if at the end of the\n       commandline, accept one word from the current autosuggestion.\n\nhistory-pager\n       invoke the searchable pager on history (incremental search);\n       or if the history pager is already active, search further\n       backwards in time.\n\nhistory-pager-delete\n       permanently delete the history item selected in the history\n       pager\n\nhistory-search-backward\n       search the history for the previous match\n\nhistory-search-forward\n       search the history for the next match\n\nhistory-prefix-search-backward\n       search the history for the previous prefix match\n\nhistory-prefix-search-forward\n       search the history for the next prefix match\n\nhistory-token-search-backward\n       search the history for the previous matching argument\n\nhistory-token-search-forward\n       search the history for the next matching argument\n\nforward-jump and backward-jump\n       read another character and jump to its next occurence\n       after/before the cursor\n\nforward-jump-till and backward-jump-till\n       jump to right before the next occurence\n\nrepeat-jump and repeat-jump-reverse\n       redo the last jump in the same/opposite direction\n\nkill-bigword\n       move the next whitespace-delimited word to the killring\n\nkill-line\n       move everything from the cursor to the end of the line to the\n       killring\n\nkill-selection\n       move the selected text to the killring\n\nkill-whole-line\n       move the line (including the following newline) to the\n       killring. If the line is the last line, its preceeding newline\n       is also removed\n\nkill-inner-line\n       move the line (without the following newline) to the killring\n\nkill-word\n       move the next word to the killring\n\nnextd-or-forward-word\n       if the commandline is empty, then move forward in the\n       directory history, otherwise move one word to the right; or if\n       at the end of the commandline, accept one word from the\n       current autosuggestion.\n\nor     only execute the next function if the previous did not succeed\n       (note: only some functions report failure)\n\npager-toggle-search\n       toggles the search field if the completions pager is visible;\n       or if used after history-pager, search forwards in time.\n\nprevd-or-backward-word\n       if the commandline is empty, then move backward in the\n       directory history, otherwise move one word to the left\n\nrepaint\n       reexecutes the prompt functions and redraws the prompt (also\n       force-repaint for backwards-compatibility)\n\nrepaint-mode\n       reexecutes the fish_mode_prompt and redraws the prompt. This\n       is useful for vi-mode. If no fish_mode_prompt exists or it\n       prints nothing, it acts like a normal repaint.\n\nself-insert\n       inserts the matching sequence into the command line\n\nself-insert-notfirst\n       inserts the matching sequence into the command line, unless\n       the cursor is at the beginning\n\nsuppress-autosuggestion\n       remove the current autosuggestion. Returns true if there was a\n       suggestion to remove.\n\nswap-selection-start-stop\n       go to the other end of the highlighted text without changing\n       the selection\n\ntranspose-chars\n       transpose two characters to the left of the cursor\n\ntranspose-words\n       transpose two words to the left of the cursor\n\ntogglecase-char\n       toggle the capitalisation (case) of the character under the\n       cursor\n\ntogglecase-selection\n       toggle the capitalisation (case) of the selection\n\ninsert-line-under\n       add a new line under the current line\n\ninsert-line-over\n       add a new line over the current line\n\nup-line\n       move up one line\n\nundo and redo\n       revert or redo the most recent edits on the command line\n\nupcase-word\n       make the current word uppercase\n\nyank   insert the latest entry of the killring into the buffer\n\nyank-pop\n       rotate to the previous entry of the killring\n\nADDITIONAL FUNCTIONS\nThe following functions are included as normal functions, but are\nparticularly useful for input editing:\n\nup-or-search and down-or-search\n       move the cursor or search the history depending on the cursor\n       position and current mode\n\nedit_command_buffer\n       open the visual editor (controlled by the VISUAL or EDITOR\n       environment variables) with the current command-line contents\n\nfish_clipboard_copy\n       copy the current selection to the system clipboard\n\nfish_clipboard_paste\n       paste the current selection from the system clipboard before\n       the cursor\n\nfish_commandline_append\n       append the argument to the command-line. If the command-line\n       already ends with the argument, this removes the suffix\n       instead. Starts with the last command from history if the\n       command-line is empty.\n\nfish_commandline_prepend\n       prepend the argument to the command-line. If the command-line\n       already starts with the argument, this removes the prefix\n       instead. Starts with the last command from history if the\n       command-line is empty.\n\nEXAMPLES\nExit the shell when Control+D is pressed:\n\n   bind \\cd 'exit'\n\nPerform a history search when Page Up is pressed:\n\n   bind -k ppage history-search-backward\n\nTurn on Vi key bindings and rebind Control+C to clear the input line:\n\n   set -g fish_key_bindings fish_vi_key_bindings\n   bind -M insert \\cc kill-whole-line repaint\n\nLaunch git diff and repaint the commandline afterwards when Control+G\nis pressed:\n\n   bind \\cg 'git diff; commandline -f repaint'\n\nTERMINAL LIMITATIONS\nUnix terminals, like the ones fish operates in, are at heart 70s\ntechnology. They have some limitations that applications running\ninside them can't workaround.\n\nFor instance, the control key modifies a character by setting the top\nthree bits to 0. This means:\n\n• Many characters + control are indistinguishable from other keys.\n  Control+I is tab, Control+J is newline (\\n).\n\n• Control and shift don't work simultaneously\n\nOther keys don't have a direct encoding, and are sent as escape\nsequences. For example → (Right) often sends \\e\\[C. These can differ\nfrom terminal to terminal, and the mapping is typically available in\nterminfo(5). Sometimes however a terminal identifies as e.g.\nxterm-256color for compatibility, but then implements xterm's\nsequences incorrectly.\n\nSPECIAL CASE: THE ESCAPE CHARACTER\nThe escape key can be used standalone, for example, to switch from\ninsertion mode to normal mode when using Vi keybindings. Escape can\nalso be used as a \"meta\" key, to indicate the start of an escape\nsequence, like for function or arrow keys. Custom bindings can also\nbe defined that begin with an escape character.\n\nHolding alt and something else also typically sends escape, for\nexample holding alt+a will send an escape character and then an \"a\".\n\nfish waits for a period after receiving the escape character, to\ndetermine whether it is standalone or part of an escape sequence.\nWhile waiting, additional key presses make the escape key behave as a\nmeta key. If no other key presses come in, it is handled as a\nstandalone escape. The waiting period is set to 30 milliseconds (0.03\nseconds). It can be configured by setting the fish_escape_delay_ms\nvariable to a value between 10 and 5000 ms. This can be a universal\nvariable that you set once from an interactive session.  So the\nescape character has its own timeout configured with\nfish_escape_delay_ms.\n\nSee also Key sequences.",
        "args": "bind [(-M | --mode) MODE] [(-m | --sets-mode) NEW_MODE] [--preset | --user] [-s | --silent] [-k | --key] SEQUENCE COMMAND ...\nbind [(-M | --mode) MODE] [-k | --key] [--preset] [--user] SEQUENCE\nbind (-K | --key-names) [-a | --all] [--preset] [--user]\nbind (-f | --function-names)\nbind (-L | --list-modes)\nbind (-e | --erase) [(-M | --mode) MODE] [--preset] [--user] [-a | --all] | [-k | --key] SEQUENCE ..."
    },
    "block": {
        "shortDescription": "temporarily block delivery of events",
        "description": "block prevents events triggered by fish or the emit command from\nbeing delivered and acted upon while the block is in place.\n\nIn functions, block can be useful while performing work that should\nnot be interrupted by the shell.\n\nThe block can be removed. Any events which triggered while the block\nwas in place will then be delivered.\n\nEvent blocks should not be confused with code blocks, which are\ncreated with begin, if, while or for\n\nWithout options, the block command acts with function scope.\n\nThe following options are available:\n\n-l or --local\n       Release the block automatically at the end of the current\n       innermost code block scope.\n\n-g or --global\n       Never automatically release the lock.\n\n-e or --erase\n       Release global block.\n\n-h or --help\n       Displays help about using this command.\n\nEXAMPLE\n\n   # Create a function that listens for events\n   function --on-event foo foo; echo 'foo fired'; end\n\n   # Block the delivery of events\n   block -g\n\n   emit foo\n   # No output will be produced\n\n   block -e\n   # 'foo fired' will now be printed\n\nNOTES\nEvents are only received from the current fish process as there is no\nway to send events from one fish process to another (yet).",
        "args": "block [(--local | --global)]\nblock --erase"
    },
    "break": {
        "shortDescription": "Exit the current loop",
        "description": "Terminate the execution of the nearest enclosing `while` or `for` loop and proceed with the next command after the loop."
    },
    "breakpoint": {
        "shortDescription": "Launch debug mode",
        "description": "Pause execution and launch an interactive debug prompt. This is useful for inspecting the state of a script at a specific point."
    },
    "builtin": {
        "shortDescription": "run a builtin command",
        "description": "builtin forces the shell to use a builtin command named BUILTIN,\nrather than a function or external program.\n\nThe following options are available:\n\n-n or --names\n       Lists the names of all defined builtins.\n\n-q or --query BUILTIN\n       Tests if any of the specified builtins exist. If any exist, it\n       returns 0, 1 otherwise.\n\n-h or --help\n       Displays help about using this command.\n\nEXAMPLE\n\n   builtin jobs\n   # executes the jobs builtin, even if a function named jobs exists",
        "args": "builtin [OPTIONS] BUILTINNAME\nbuiltin --query BUILTINNAME ...\nbuiltin --names"
    },
    "case": {
        "shortDescription": "Match a value against patterns",
        "description": "Within a `switch` block, the `case` command specifies patterns to match against the given value, executing the associated block if a match is found.",
        "args": "PATTERN..."
    },
    "cd": {
        "shortDescription": "change directory",
        "description": "cd changes the current working directory.\n\nIf DIRECTORY is given, it will become the new directory. If no\nparameter is given, the HOME environment variable will be used.\n\nIf DIRECTORY is a relative path, all the paths in the CDPATH will be\ntried as prefixes for it, in addition to PWD.  It is recommended to\nkeep . as the first element of CDPATH, or PWD will be tried last.\n\nFish will also try to change directory if given a command that looks\nlike a directory (starting with ., / or ~, or ending with /), without\nexplicitly requiring cd.\n\nFish also ships a wrapper function around the builtin cd that\nunderstands cd - as changing to the previous directory.  See also\nprevd.  This wrapper function maintains a history of the 25 most\nrecently visited directories in the $dirprev and $dirnext global\nvariables.  If you make those universal variables your cd history is\nshared among all fish instances.\n\nAs a special case, cd . is equivalent to cd $PWD, which is useful in\ncases where a mountpoint has been recycled or a directory has been\nremoved and recreated.\n\nThe --help or -h option displays help about using this command, and\ndoes not change the directory.\n\nEXAMPLES\n\n   cd\n   # changes the working directory to your home directory.\n\n   cd /usr/src/fish-shell\n   # changes the working directory to /usr/src/fish-shell\n\nSEE ALSO\nNavigate directories using the directory history or the directory\nstack",
        "args": "cd [DIRECTORY]"
    },
    "command": {
        "shortDescription": "run a program",
        "description": "command forces the shell to execute the program COMMANDNAME and\nignore any functions or builtins with the same name.\n\nThe following options are available:\n\n-a or --all\n       Prints all COMMAND found in PATH, in the order found.\n\n-q or --query\n       Silence output and print nothing, setting only exit status.\n       Implies --search.  For compatibility, this is also --quiet\n       (deprecated).\n\n-v (or -s or --search)\n       Prints the external command that would be executed, or prints\n       nothing if no file with the specified name could be found in\n       PATH.\n\n-h or --help\n       Displays help about using this command.\n\nWith the -v option, command treats every argument as a separate\ncommand to look up and sets the exit status to 0 if any of the\nspecified commands were found, or 127 if no commands could be found.\n--quiet used with -v prevents commands being printed, like type -q.\n\nEXAMPLES\ncommand ls executes the ls program, even if an ls function also exists.\ncommand -s ls prints the path to the ls program.\ncommand -q git; and command git log runs git log only if git exists.",
        "args": "command [OPTIONS] [COMMANDNAME [ARG ...]]"
    },
    "commandline": {
        "shortDescription": "set or get the current command line buffer",
        "description": "commandline can be used to set or get the current contents of the\ncommand line buffer.\n\nWith no parameters, commandline returns the current value of the\ncommand line.\n\nWith CMD specified, the command line buffer is erased and replaced\nwith the contents of CMD.\n\nThe following options are available:\n\n-C or --cursor\n       Set or get the current cursor position, not the contents of\n       the buffer.  If no argument is given, the current cursor\n       position is printed, otherwise the argument is interpreted as\n       the new cursor position.  If one of the options -j, -p or -t\n       is given, the position is relative to the respective substring\n       instead of the entire command line buffer.\n\n-B or --selection-start\n       Get current position of the selection start in the buffer.\n\n-E or --selection-end\n       Get current position of the selection end in the buffer.\n\n-f or --function\n       Causes any additional arguments to be interpreted as input\n       functions, and puts them into the queue, so that they will be\n       read before any additional actual key presses are.  This\n       option cannot be combined with any other option.  See bind for\n       a list of input functions.\n\n-h or --help\n       Displays help about using this command.\n\nThe following options change the way commandline updates the command\nline buffer:\n\n-a or --append\n       Do not remove the current commandline, append the specified\n       string at the end of it.\n\n-i or --insert\n       Do not remove the current commandline, insert the specified\n       string at the current cursor position\n\n-r or --replace\n       Remove the current commandline and replace it with the\n       specified string (default)\n\nThe following options change what part of the commandline is printed\nor updated:\n\n-b or --current-buffer\n       Select the entire commandline, not including any displayed\n       autosuggestion (default).\n\n-j or --current-job\n       Select the current job - a job here is one pipeline.  Stops at\n       logical operators or terminators (;, &, and newlines).\n\n-p or --current-process\n       Select the current process - a process here is one command.\n       Stops at logical operators, terminators, and pipes.\n\n-s or --current-selection\n       Selects the current selection\n\n-t or --current-token\n       Selects the current token\n\nThe following options change the way commandline prints the current\ncommandline buffer:\n\n-c or --cut-at-cursor\n       Only print selection up until the current cursor position.  If\n       combined with --tokenize, this will print up until the last\n       completed token - excluding the token the cursor is in.  This\n       is typically what you would want for instance in completions.\n       To get both, use both commandline --cut-at-cursor --tokenize;\n       commandline --cut-at-cursor --current-token, or commandline\n       -co; commandline -ct for short.\n\n-o or --tokenize\n       Tokenize the selection and print one string-type token per\n       line.\n\nIf commandline is called during a call to complete a given string\nusing complete -C STRING, commandline will consider the specified\nstring to be the current contents of the command line.\n\nThe following options output metadata about the commandline state:\n\n-L or --line\n       Print the line that the cursor is on, with the topmost line\n       starting at 1.\n\n-S or --search-mode\n       Evaluates to true if the commandline is performing a history\n       search.\n\n-P or --paging-mode\n       Evaluates to true if the commandline is showing pager\n       contents, such as tab completions.\n\n--paging-full-mode\n       Evaluates to true if the commandline is showing pager\n       contents, such as tab completions and all lines are shown (no\n       \"<n> more rows\" message).\n\n--is-valid\n       Returns true when the commandline is syntactically valid and\n       complete.  If it is, it would be executed when the execute\n       bind function is called.  If the commandline is incomplete,\n       return 2, if erroneus, return 1.\n\nEXAMPLE\ncommandline -j $history[3] replaces the job under the cursor with the\nthird item from the command line history.\n\nIf the commandline contains\n\n   > echo $flounder >&2 | less; and echo $catfish\n\n(with the cursor on the \"o\" of \"flounder\")\n\nThe echo $flounder >& is the first process, less the second and and\necho $catfish the third.\n\necho $flounder >&2 | less is the first job, and echo $catfish the\nsecond.\n\n$flounder is the current token.\n\nThe most common use for something like completions is\n\n   set -l tokens (commandline -opc)\n\nwhich gives the current process (what is being completed), tokenized\ninto separate entries, up to but excluding the currently being\ncompleted token\n\nIf you are then also interested in the in-progress token, add\n\n::     set -l current (commandline -ct)\n\nNote that this makes it easy to render fish's infix matching moot -\nif possible it's best if the completions just print all possibilities\nand leave the matching to the current token up to fish's logic.\n\nMore examples:\n\n   > commandline -t\n   $flounder\n   > commandline -ct\n   $fl\n   > commandline -b # or just commandline\n   echo $flounder >&2 | less; and echo $catfish\n   > commandline -p\n   echo $flounder >&2\n   > commandline -j\n   echo $flounder >&2 | less",
        "args": "commandline [OPTIONS] [CMD]"
    },
    "complete": {
        "shortDescription": "edit command-specific tab-completions",
        "description": "complete defines, removes or lists completions for a command.\n\nFor an introduction to writing your own completions, see Writing your\nown completions in the fish manual.\n\nThe following options are available:\n\n-c or --command COMMAND\n       Specifies that COMMAND is the name of the command. If there is\n       no -c or -p, one non-option argument will be used as the\n       command.\n\n-p or --path COMMAND\n       Specifies that COMMAND is the absolute path of the command\n       (optionally containing wildcards).\n\n-e or --erase\n       Deletes the specified completion.\n\n-s or --short-option SHORT_OPTION\n       Adds a short option to the completions list.\n\n-l or --long-option LONG_OPTION\n       Adds a GNU-style long option to the completions list.\n\n-o or --old-option OPTION\n       Adds an old-style short or long option (see below for\n       details).\n\n-a or --arguments ARGUMENTS\n       Adds the specified option arguments to the completions list.\n\n-k or --keep-order\n       Keeps the order of ARGUMENTS instead of sorting\n       alphabetically. Multiple complete calls with -k result in\n       arguments of the later ones displayed first.\n\n-f or --no-files\n       This completion may not be followed by a filename.\n\n-F or --force-files\n       This completion may be followed by a filename, even if another\n       applicable complete specified --no-files.\n\n-r or --require-parameter\n       This completion must have an option argument, i.e. may not be\n       followed by another option.\n\n-x or --exclusive\n       Short for -r and -f.\n\n-w or --wraps WRAPPED_COMMAND\n       Causes the specified command to inherit completions from\n       WRAPPED_COMMAND (see below for details).\n\n-n or --condition CONDITION\n       This completion should only be used if the CONDITION (a shell\n       command) returns 0. This makes it possible to specify\n       completions that should only be used in some cases. If\n       multiple conditions are specified, fish will try them in the\n       order they are specified until one fails or all succeeded.\n\n-C or --do-complete STRING\n       Makes complete try to find all possible completions for the\n       specified string. If there is no STRING, the current\n       commandline is used instead.\n\n--escape\n       When used with -C, escape special characters in completions.\n\n-h or --help\n       Displays help about using this command.\n\nCommand-specific tab-completions in fish are based on the notion of\noptions and arguments. An option is a parameter which begins with a\nhyphen, such as -h, -help or --help. Arguments are parameters that do\nnot begin with a hyphen. Fish recognizes three styles of options, the\nsame styles as the GNU getopt library. These styles are:\n\n• Short options, like -a. Short options are a single character long,\n  are preceded by a single hyphen and can be grouped together (like\n  -la, which is equivalent to -l -a). Option arguments may be\n  specified by appending the option with the value (-w32), or, if\n  --require-parameter is given, in the following parameter (-w 32).\n\n• Old-style options, long like -Wall or -name or even short like -a.\n  Old-style options can be more than one character long, are preceded\n  by a single hyphen and may not be grouped together. Option\n  arguments are specified by default following a space (-foo null) or\n  after = (-foo=null).\n\n• GNU-style long options, like --colors. GNU-style long options can\n  be more than one character long, are preceded by two hyphens, and\n  can't be grouped together. Option arguments may be specified after\n  a = (--quoting-style=shell), or, if --require-parameter is given,\n  in the following parameter (--quoting-style shell).\n\nMultiple commands and paths can be given in one call to define the\nsame completions for multiple commands.\n\nMultiple command switches and wrapped commands can also be given to\ndefine multiple completions in one call.\n\nInvoking complete multiple times for the same command adds the new\ndefinitions on top of any existing completions defined for the\ncommand.\n\nWhen -a or --arguments is specified in conjunction with long, short,\nor old-style options, the specified arguments are only completed as\narguments for any of the specified options. If -a or --arguments is\nspecified without any long, short, or old-style options, the\nspecified arguments are used when completing non-option arguments to\nthe command (except when completing an option argument that was\nspecified with -r or --require-parameter).\n\nCommand substitutions found in ARGUMENTS should return a\nnewline-separated list of arguments, and each argument may optionally\nhave a tab character followed by the argument description.\nDescription given this way override a description given with -d or\n--description.\n\nDescriptions given with --description are also used to group options\ngiven with -s, -o or -l. Options with the same (non-empty)\ndescription will be listed as one candidate, and one of them will be\npicked. If the description is empty or no description was given this\nis skipped.\n\nThe -w or --wraps options causes the specified command to inherit\ncompletions from another command, \"wrapping\" the other command. The\nwrapping command can also have additional completions. A command can\nwrap multiple commands, and wrapping is transitive: if A wraps B, and\nB wraps C, then A automatically inherits all of C's completions.\nWrapping can be removed using the -e or --erase options. Wrapping\nonly works for completions specified with -c or --command and are\nignored when specifying completions with -p or --path.\n\nWhen erasing completions, it is possible to either erase all\ncompletions for a specific command by specifying complete -c COMMAND\n-e, or by specifying a specific completion option to delete.\n\nWhen complete is called without anything that would define or erase\ncompletions (options, arguments, wrapping, ...), it shows matching\ncompletions instead. So complete without any arguments shows all\nloaded completions, complete -c foo shows all loaded completions for\nfoo. Since completions are autoloaded, you will have to trigger them\nfirst.\n\nEXAMPLES\nThe short-style option -o for the gcc command needs a file argument:\n\n   complete -c gcc -s o -r\n\nThe short-style option -d for the grep command requires one of read,\nskip or recurse:\n\n   complete -c grep -s d -x -a \"read skip recurse\"\n\nThe su command takes any username as an argument. Usernames are given\nas the first colon-separated field in the file /etc/passwd. This can\nbe specified as:\n\n   complete -x -c su -d \"Username\" -a \"(cat /etc/passwd | cut -d : -f 1)\"\n\nThe rpm command has several different modes. If the -e or --erase\nflag has been specified, rpm should delete one or more packages, in\nwhich case several switches related to deleting packages are valid,\nlike the nodeps switch.\n\nThis can be written as:\n\n   complete -c rpm -n \"__fish_contains_opt -s e erase\" -l nodeps -d \"Don't check dependencies\"\n\nwhere __fish_contains_opt is a function that checks the command line\nbuffer for the presence of a specified set of options.\n\nTo implement an alias, use the -w or --wraps option:\n\n   complete -c hub -w git\n\nNow hub inherits all of the completions from git. Note this can also\nbe specified in a function declaration (function thing -w\notherthing).\n\n   complete -c git\n\nShows all completions for git.\n\nAny command foo that doesn't support grouping multiple short options\nin one string (not supporting -xf as short for -x -f) or a short\noption and its value in one string (not supporting -d9 instead of -d\n9) should be specified as a single-character old-style option instead\nof as a short-style option; for example, complete -c foo -o s;\ncomplete -c foo -o v would never suggest foo -ov but rather foo -o\n-v.",
        "args": "complete ((-c | --command) | (-p | --path)) COMMAND [OPTIONS]\ncomplete (-C | --do-complete) [--escape] STRING"
    },
    "contains": {
        "shortDescription": "test if a word is present in a list",
        "description": "contains tests whether the set VALUES contains the string KEY.  If\nso, contains exits with code 0; if not, it exits with code 1.\n\nThe following options are available:\n\n-i or --index\n       Print the index (number of the element in the set) of the\n       first matching element.\n\n-h or --help\n       Displays help about using this command.\n\nNote that contains interprets all arguments starting with a - as an\noption to contains, until an -- argument is reached.\n\nSee the examples below.\n\nEXAMPLE\nIf animals is a list of animals, the following will test if animals\ncontains \"cat\":\n\n   if contains cat $animals\n      echo Your animal list is evil!\n   end\n\nThis code will add some directories to PATH if they aren't yet\nincluded:\n\n   for i in ~/bin /usr/local/bin\n       if not contains $i $PATH\n           set PATH $PATH $i\n       end\n   end\n\nWhile this will check if function hasargs is being ran with the -q\noption:\n\n   function hasargs\n       if contains -- -q $argv\n           echo '$argv contains a -q option'\n       end\n   end\n\nThe -- here stops contains from treating -q to an option to itself.\nInstead it treats it as a normal string to check.",
        "args": "contains [OPTIONS] KEY [VALUES ...]"
    },
    "continue": {
        "shortDescription": "Skip to the next iteration of a loop",
        "description": "Within a `while` or `for` loop, `continue` skips the remaining commands in the current iteration and proceeds to the next iteration of the loop."
    },
    "count": {
        "shortDescription": "",
        "description": ""
    },
    "disown": {
        "shortDescription": "remove a process from the list of jobs",
        "description": "disown removes the specified job from the list of jobs. The job\nitself continues to exist, but fish does not keep track of it any\nlonger.\n\nJobs in the list of jobs are sent a hang-up signal when fish\nterminates, which usually causes the job to terminate; disown allows\nthese processes to continue regardless.\n\nIf no process is specified, the most recently-used job is removed\n(like bg and fg).  If one or more PIDs are specified, jobs with the\nspecified process IDs are removed from the job list. Invalid jobs are\nignored and a warning is printed.\n\nIf a job is stopped, it is sent a signal to continue running, and a\nwarning is printed. It is not possible to use the bg builtin to\ncontinue a job once it has been disowned.\n\ndisown returns 0 if all specified jobs were disowned successfully,\nand 1 if any problems were encountered.\n\nThe --help or -h option displays help about using this command.\n\nEXAMPLE\nfirefox &; disown will start the Firefox web browser in the\nbackground and remove it from the job list, meaning it will not be\nclosed when the fish process is closed.\n\ndisown (jobs -p) removes all jobs from the job list without\nterminating them.",
        "args": "disown [PID ...]"
    },
    "echo": {
        "shortDescription": "",
        "description": ""
    },
    "else": {
        "shortDescription": "Execute commands if the previous condition was false",
        "description": "In an `if` block, the `else` section contains commands that execute if none of the preceding `if` or `else if` conditions were true."
    },
    "emit": {
        "shortDescription": "emit a generic event",
        "description": "emit emits, or fires, an event. Events are delivered to, or caught\nby, special functions called event handlers. The arguments are passed\nto the event handlers as function arguments.\n\nThe --help or -h option displays help about using this command.\n\nEXAMPLE\nThe following code first defines an event handler for the generic\nevent named 'test_event', and then emits an event of that type.\n\n   function event_test --on-event test_event\n       echo event test: $argv\n   end\n\n   emit test_event something\n\nNOTES\nNote that events are only sent to the current fish process as there\nis no way to send events from one fish process to another.",
        "args": "emit EVENT_NAME [ARGUMENTS ...]"
    },
    "end": {
        "shortDescription": "Terminate a block of code",
        "description": "Conclude a block of code initiated by constructs like `if`, `switch`, `while`, `for`, or `function`."
    },
    "eval": {
        "shortDescription": "Execute arguments as a command",
        "description": "Concatenate all arguments into a single command and execute it. This allows for dynamic construction and execution of commands.",
        "args": "COMMAND..."
    },
    "exec": {
        "shortDescription": "execute command in current process",
        "description": "exec replaces the currently running shell with a new command. On\nsuccessful completion, exec never returns. exec cannot be used inside\na pipeline.\n\nThe --help or -h option displays help about using this command.\n\nEXAMPLE\nexec emacs starts up the emacs text editor, and exits fish. When\nemacs exits, the session will terminate.",
        "args": "exec COMMAND"
    },
    "exit": {
        "shortDescription": "exit the shell",
        "description": "exit is a special builtin that causes the shell to exit. Either 255\nor the CODE supplied is used, whichever is lesser.  Otherwise, the\nexit status will be that of the last command executed.\n\nIf exit is called while sourcing a file (using the source builtin)\nthe rest of the file will be skipped, but the shell itself will not\nexit.\n\nThe --help or -h option displays help about using this command.",
        "args": "exit [CODE]"
    },
    "false": {
        "shortDescription": "Return an unsuccessful result",
        "description": "A command that returns a non-zero exit status, indicating failure. It is often used in scripts to represent a false condition."
    },
    "fg": {
        "shortDescription": "bring job to foreground",
        "description": "The fg builtin brings the specified job to the foreground, resuming\nit if it is stopped.  While a foreground job is executed, fish is\nsuspended.  If no job is specified, the last job to be used is put in\nthe foreground.  If PID is specified, the job containing a process\nwith the specified process ID is put in the foreground.\n\nFor compatibility with other shells, job expansion syntax is\nsupported for fg. A PID of the format %1 will foreground job 1.  Job\nnumbers can be seen in the output of jobs.\n\nThe --help or -h option displays help about using this command.\n\nEXAMPLE\nfg will put the last job in the foreground.\n\nfg %3 will put job 3 into the foreground.",
        "args": "fg [PID]"
    },
    "for": {
        "shortDescription": "perform a set of commands multiple times",
        "description": "for is a loop construct. It will perform the commands specified by\nCOMMANDS multiple times. On each iteration, the local variable\nspecified by VARNAME is assigned a new value from VALUES. If VALUES\nis empty, COMMANDS will not be executed at all. The VARNAME is\nvisible when the loop terminates and will contain the last value\nassigned to it. If VARNAME does not already exist it will be set in\nthe local scope. For our purposes if the for block is inside a\nfunction there must be a local variable with the same name. If the\nfor block is not nested inside a function then global and universal\nvariables of the same name will be used if they exist.\n\nMuch like set, for does not modify $status, but the evaluation of its\nsubordinate commands can.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\n\n   for i in foo bar baz; echo $i; end\n\n   # would output:\n   foo\n   bar\n   baz\n\nNOTES\nThe VARNAME was local to the for block in releases prior to 3.0.0.\nThis means that if you did something like this:\n\n   for var in a b c\n       if break_from_loop\n           break\n       end\n   end\n   echo $var\n\nThe last value assigned to var when the loop terminated would not be\navailable outside the loop. What echo $var would write depended on\nwhat it was set to before the loop was run. Likely nothing.",
        "args": "for VARNAME in [VALUES ...]; COMMANDS ...; end"
    },
    "function": {
        "shortDescription": "create a function",
        "description": "function creates a new function NAME with the body BODY.\n\nA function is a list of commands that will be executed when the name\nof the function is given as a command.\n\nThe following options are available:\n\n-a NAMES or --argument-names NAMES\n       Assigns the value of successive command-line arguments to the\n       names given in NAMES. These are the same arguments given in\n       argv, and are still available there. See also Argument\n       Handling.\n\n-d DESCRIPTION or --description DESCRIPTION\n       A description of what the function does, suitable as a\n       completion description.\n\n-w WRAPPED_COMMAND or --wraps WRAPPED_COMMAND\n       Inherit completions from the given WRAPPED_COMMAND. See the\n       documentation for complete for more information.\n\n-e EVENT_NAME or --on-event EVENT_NAME\n       Run this function when the specified named event is emitted.\n       Fish internally generates named events, for example, when\n       showing the prompt. Custom events can be emitted using the\n       emit command.\n\n-v VARIABLE_NAME or --on-variable VARIABLE_NAME\n       Run this function when the variable VARIABLE_NAME changes\n       value. Note that fish makes no guarantees on any particular\n       timing or even that the function will be run for every single\n       set. Rather it will be run when the variable has been set at\n       least once, possibly skipping some values or being run when\n       the variable has been set to the same value (except for\n       universal variables set in other shells - only changes in the\n       value will be picked up for those).\n\n-j PID or --on-job-exit PID\n       Run this function when the job containing a child process with\n       the given process identifier PID exits. Instead of a PID, the\n       string 'caller' can be specified. This is only allowed when in\n       a command substitution, and will result in the handler being\n       triggered by the exit of the job which created this command\n       substitution.\n\n-p PID or --on-process-exit PID\n       Run this function when the fish child process with process ID\n       PID exits. Instead of a PID, for backward compatibility,\n       \"%self\" can be specified as an alias for $fish_pid, and the\n       function will be run when the current fish instance exits.\n\n-s SIGSPEC or --on-signal SIGSPEC\n       Run this function when the signal SIGSPEC is delivered.\n       SIGSPEC can be a signal number, or the signal name, such as\n       SIGHUP (or just HUP). Note that the signal must have been\n       delivered to fish; for example, Ctrl-C sends SIGINT to the\n       foreground process group, which will not be fish if you are\n       running another command at the time. Observing a signal will\n       prevent fish from exiting in response to that signal.\n\n-S or --no-scope-shadowing\n       Allows the function to access the variables of calling\n       functions. Normally, any variables inside the function that\n       have the same name as variables from the calling function are\n       \"shadowed\", and their contents are independent of the calling\n       function.\n\n       It's important to note that this does not capture referenced\n       variables or the scope at the time of function declaration! At\n       this time, fish does not have any concept of closures, and\n       variable lifetimes are never extended. In other words, by\n       using --no-scope-shadowing the scope of the function each time\n       it is run is shared with the scope it was called from rather\n       than the scope it was defined in.\n\n-V or --inherit-variable NAME\n       Snapshots the value of the variable NAME and defines a local\n       variable with that same name and value when the function is\n       defined. This is similar to a closure in other languages like\n       Python but a bit different. Note the word \"snapshot\" in the\n       first sentence. If you change the value of the variable after\n       defining the function, even if you do so in the same scope\n       (typically another function) the new value will not be used by\n       the function you just created using this option. See the\n       function notify example below for how this might be used.\n\nThe event handler switches (on-event, on-variable, on-job-exit,\non-process-exit and on-signal) cause a function to run automatically\nat specific events. New named events for --on-event can be fired\nusing the emit builtin. Fish already generates a few events, see\nEvent handlers for more.\n\nFunctions may not be named the same as a reserved keyword. These are\nelements of fish syntax or builtin commands which are essential for\nthe operations of the shell. Current reserved words are [, , and,\nargparse, begin, break, builtin, case, command, continue, else, end,\neval, exec, for, function, if, not, or, read, return, set, status,\nstring, switch, test, time, and while.\n\nEXAMPLE\n\n   function ll\n       ls -l $argv\n   end\n\nwill run the ls command, using the -l option, while passing on any\nadditional files and switches to ls.\n\n   function mkdir -d \"Create a directory and set CWD\"\n       command mkdir $argv\n       if test $status = 0\n           switch $argv[(count $argv)]\n               case '-*'\n\n               case '*'\n                   cd $argv[(count $argv)]\n                   return\n           end\n       end\n   end\n\nThis will run the mkdir command, and if it is successful, change the\ncurrent working directory to the one just created.\n\n   function notify\n       set -l job (jobs -l -g)\n       or begin; echo \"There are no jobs\" >&2; return 1; end\n\n       function _notify_job$job --on-job-exit $job --inherit-variable job\n           echo -n \\a # beep\n           functions -e _notify_job$job\n       end\n   end\n\nThis will beep when the most recent job completes.\n\nNOTES\nEvents are only received from the current fish process as there is no\nway to send events from one fish process to another.\n\nSEE MORE\nFor more explanation of how functions fit into fish, see Functions.",
        "args": "function NAME [OPTIONS]; BODY; end"
    },
    "functions": {
        "shortDescription": "print or erase functions",
        "description": "functions prints or erases functions.\n\nThe following options are available:\n\n-a or --all\n       Lists all functions, even those whose name starts with an\n       underscore.\n\n-c or --copy OLDNAME NEWNAME\n       Creates a new function named NEWNAME, using the definition of\n       the OLDNAME function.\n\n-d or --description DESCRIPTION\n       Changes the description of this function.\n\n-e or --erase\n       Causes the specified functions to be erased. This also means\n       that it is prevented from autoloading in the current session.\n       Use funcsave to remove the saved copy.\n\n-D or --details\n       Reports the path name where the specified function is defined\n       or could be autoloaded, stdin if the function was defined\n       interactively or on the command line or by reading standard\n       input, - if the function was created via source, and n/a if\n       the function isn't available. (Functions created via alias\n       will return -, because alias uses source internally.) If the\n       --verbose option is also specified then five lines are\n       written:\n\n       • the pathname as already described,\n\n       • autoloaded, not-autoloaded or n/a,\n\n       • the line number within the file or zero if not applicable,\n\n       • scope-shadowing if the function shadows the vars in the\n         calling function (the normal case if it wasn't defined with\n         --no-scope-shadowing), else no-scope-shadowing, or n/a if\n         the function isn't defined,\n\n       • the function description minimally escaped so it is a single\n         line, or n/a if the function isn't defined or has no\n         description.\n\n       You should not assume that only five lines will be written\n       since we may add additional information to the output in the\n       future.\n\n--no-details\n       Turns off function path reporting, so just the definition will\n       be printed.\n\n-n or --names\n       Lists the names of all defined functions.\n\n-q or --query\n       Tests if the specified functions exist.\n\n-v or --verbose\n       Make some output more verbose.\n\n-H or --handlers\n       Show all event handlers.\n\n-t or --handlers-type TYPE\n       Show all event handlers matching the given TYPE.\n\n-h or --help\n       Displays help about using this command.\n\nThe default behavior of functions, when called with no arguments, is\nto print the names of all defined functions. Unless the -a option is\ngiven, no functions starting with underscores are included in the\noutput.\n\nIf any non-option parameters are given, the definition of the\nspecified functions are printed.\n\nCopying a function using -c copies only the body of the function, and\ndoes not attach any event notifications from the original function.\n\nOnly one function's description can be changed in a single invocation\nof functions -d.\n\nThe exit status of functions is the number of functions specified in\nthe argument list that do not exist, which can be used in concert\nwith the -q option.\n\nEXAMPLES\n\n   functions -n\n   # Displays a list of currently-defined functions\n\n   functions -c foo bar\n   # Copies the 'foo' function to a new function called 'bar'\n\n   functions -e bar\n   # Erases the function ``bar``\n\nSEE MORE\nFor more explanation of how functions fit into fish, see Functions.",
        "args": "functions [-a | --all] [-n | --names]\nfunctions [-D | --details] [-v] FUNCTION\nfunctions -c OLDNAME NEWNAME\nfunctions -d DESCRIPTION FUNCTION\nfunctions [-e | -q] FUNCTION ..."
    },
    "history": {
        "shortDescription": "show and manipulate command history",
        "description": "history is used to search, delete, and otherwise manipulate the\nhistory of interactive commands.\n\nThe following operations (sub-commands) are available:\n\nsearch Returns history items matching the search string. If no search\n       string is provided it returns all history items. This is the\n       default operation if no other operation is specified. You only\n       have to explicitly say history search if you wish to search\n       for one of the subcommands. The --contains search option will\n       be used if you don't specify a different search option.\n       Entries are ordered newest to oldest unless you use the\n       --reverse flag. If stdout is attached to a tty the output will\n       be piped through your pager by the history function. The\n       history builtin simply writes the results to stdout.\n\ndelete Deletes history items. The --contains search option will be\n       used if you don't specify a different search option. If you\n       don't specify --exact a prompt will be displayed before any\n       items are deleted asking you which entries are to be deleted.\n       You can enter the word \"all\" to delete all matching entries.\n       You can enter a single ID (the number in square brackets) to\n       delete just that single entry. You can enter more than one ID,\n       or an ID range separated by a space to delete multiple\n       entries. Just press [enter] to not delete anything. Note that\n       the interactive delete behavior is a feature of the history\n       function. The history builtin only supports --exact\n       --case-sensitive deletion.\n\nmerge  Immediately incorporates history changes from other sessions.\n       Ordinarily fish ignores history changes from sessions started\n       after the current one. This command applies those changes\n       immediately.\n\nsave   Immediately writes all changes to the history file. The shell\n       automatically saves the history file; this option is provided\n       for internal use and should not normally need to be used by\n       the user.\n\nclear  Clears the history file. A prompt is displayed before the\n       history is erased asking you to confirm you really want to\n       clear all history unless builtin history is used.\n\nclear-session\n       Clears the history file from all activity of the current\n       session. Note: If history merge or builtin history merge is\n       run in a session, only the history after this will be erased.\n\nThe following options are available:\n\nThese flags can appear before or immediately after one of the\nsub-commands listed above.\n\n-C or --case-sensitive\n       Does a case-sensitive search. The default is case-insensitive.\n       Note that prior to fish 2.4.0 the default was case-sensitive.\n\n-c or --contains\n       Searches items in the history that contain the specified text\n       string. This is the default for the --search flag. This is not\n       currently supported by the delete subcommand.\n\n-e or --exact\n       Searches or deletes items in the history that exactly match\n       the specified text string. This is the default for the delete\n       subcommand. Note that the match is case-insensitive by\n       default. If you really want an exact match, including letter\n       case, you must use the -C or --case-sensitive flag.\n\n-p or --prefix\n       Searches items in the history that begin with the specified\n       text string. This is not currently supported by the delete\n       subcommand.\n\n-t or --show-time\n       Prepends each history entry with the date and time the entry\n       was recorded. By default it uses the strftime format # %c%n.\n       You can specify another format; e.g., --show-time=\"%Y-%m-%d\n       %H:%M:%S \" or --show-time=\"%a%I%p\". The short option, -t,\n       doesn't accept a strftime format string; it only uses the\n       default format. Any strftime format is allowed, including %s\n       to get the raw UNIX seconds since the epoch.\n\n-z or --null\n       Causes history entries written by the search operations to be\n       terminated by a NUL character rather than a newline. This\n       allows the output to be processed by read -z to correctly\n       handle multiline history entries.\n\n-*NUMBER* -n NUMBER or --max NUMBER\n       Limits the matched history items to the first NUMBER matching\n       entries. This is only valid for history search.\n\n-R or --reverse\n       Causes the history search results to be ordered oldest to\n       newest. Which is the order used by most shells. The default is\n       newest to oldest.\n\n-h or --help\n       Displays help for this command.\n\nEXAMPLE\n\n   history clear\n   # Deletes all history items\n\n   history search --contains \"foo\"\n   # Outputs a list of all previous commands containing the string \"foo\".\n\n   history delete --prefix \"foo\"\n   # Interactively deletes commands which start with \"foo\" from the history.\n   # You can select more than one entry by entering their IDs separated by a space.\n\nCUSTOMIZING THE NAME OF THE HISTORY FILE\nBy default interactive commands are logged to\n$XDG_DATA_HOME/fish/fish_history (typically\n~/.local/share/fish/fish_history).\n\nYou can set the fish_history variable to another name for the current\nshell session. The default value (when the variable is unset) is fish\nwhich corresponds to $XDG_DATA_HOME/fish/fish_history. If you set it\nto e.g. fun, the history would be written to\n$XDG_DATA_HOME/fish/fun_history. An empty string means history will\nnot be stored at all. This is similar to the private session features\nin web browsers.\n\nYou can change fish_history at any time (by using set -x fish_history\n\"session_name\") and it will take effect right away. If you set it to\n\"default\", it will use the default session name (which is \"fish\").\n\nOther shells such as bash and zsh use a variable named HISTFILE for a\nsimilar purpose. Fish uses a different name to avoid conflicts and\nsignal that the behavior is different (session name instead of a file\npath). Also, if you set the var to anything other than fish or\ndefault it will inhibit importing the bash history. That's because\nthe most common use case for this feature is to avoid leaking private\nor sensitive history when giving a presentation.\n\nNOTES\nIf you specify both --prefix and --contains the last flag seen is\nused.\n\nNote that for backwards compatibility each subcommand can also be\nspecified as a long option. For example, rather than history search\nyou can type history --search. Those long options are deprecated and\nwill be removed in a future release.",
        "args": "history [search] [--show-time] [--case-sensitive]\n[--exact | --prefix | --contains] [--max N] [--null] [--reverse]\n                 [SEARCH_STRING ...]\nhistory delete [--case-sensitive]\n               [--exact | --prefix | --contains] SEARCH_STRING ...\nhistory merge\nhistory save\nhistory clear\nhistory clear-session"
    },
    "if": {
        "shortDescription": "conditionally execute a command",
        "description": "if will execute the command CONDITION. If the condition's exit status\nis 0, the commands COMMANDS_TRUE will execute.  If the exit status is\nnot 0 and else is given, COMMANDS_FALSE will be executed.\n\nYou can use and or or in the condition. See the second example below.\n\nThe exit status of the last foreground command to exit can always be\naccessed using the $status variable.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\nThe following code will print foo.txt exists if the file foo.txt\nexists and is a regular file, otherwise it will print bar.txt exists\nif the file bar.txt exists and is a regular file, otherwise it will\nprint foo.txt and bar.txt do not exist.\n\n   if test -f foo.txt\n       echo foo.txt exists\n   else if test -f bar.txt\n       echo bar.txt exists\n   else\n       echo foo.txt and bar.txt do not exist\n   end\n\nThe following code will print \"foo.txt exists and is readable\" if\nfoo.txt is a regular file and readable\n\n   if test -f foo.txt\n      and test -r foo.txt\n      echo \"foo.txt exists and is readable\"\n   end",
        "args": "if CONDITION; COMMANDS_TRUE ...;\n[else if CONDITION2; COMMANDS_TRUE2 ...;]\n[else; COMMANDS_FALSE ...;]\nend"
    },
    "jobs": {
        "shortDescription": "print currently running jobs",
        "description": "jobs prints a list of the currently running jobs and their status.\n\njobs accepts the following options:\n\n-c or --command\n       Prints the command name for each process in jobs.\n\n-g or --group\n       Only prints the group ID of each job.\n\n-l or --last\n       Prints only the last job to be started.\n\n-p or --pid\n       Prints the process ID for each process in all jobs.\n\n-q or --query\n       Prints no output for evaluation of jobs by exit status only.\n       For compatibility with old fish versions this is also --quiet\n       (but this is deprecated).\n\n-h or --help\n       Displays help about using this command.\n\nOn systems that support this feature, jobs will print the CPU usage\nof each job since the last command was executed. The CPU usage is\nexpressed as a percentage of full CPU activity. Note that on\nmultiprocessor systems, the total activity may be more than 100%.\n\nArguments of the form PID or %JOBID restrict the output to jobs with\nthe selected process identifiers or job numbers respectively.\n\nIf the output of jobs is redirected or if it is part of a command\nsubstitution, the column header that is usually printed is omitted,\nmaking it easier to parse.\n\nThe exit status of jobs is 0 if there are running background jobs and\n1 otherwise.\n\nEXAMPLE\njobs outputs a summary of the current jobs, such as two long-running\ntasks in this example:\n\n   Job Group   State   Command\n   2   26012   running nc -l 55232 < /dev/random &\n   1   26011   running python tests/test_11.py &",
        "args": "jobs [OPTIONS] [PID | %JOBID]"
    },
    "math": {
        "shortDescription": "perform mathematics calculations",
        "description": "math performs mathematical calculations.  It supports simple\noperations such as addition, subtraction, and so on, as well as\nfunctions like abs(), sqrt() and ln().\n\nBy default, the output shows up to 6 decimal places.  To change the\nnumber of decimal places, use the --scale option, including --scale=0\nfor integer output.  Trailing zeroes will always be trimmed.\n\nKeep in mind that parameter expansion happens before expressions are\nevaluated.  This can be very useful in order to perform calculations\ninvolving shell variables or the output of command substitutions, but\nit also means that parenthesis (()) and the asterisk (*) glob\ncharacter have to be escaped or quoted.  x can also be used to denote\nmultiplication, but it needs to be followed by whitespace to\ndistinguish it from hexadecimal numbers.\n\nParentheses for functions are optional - math sin pi prints 0.\nHowever, a comma will bind to the inner function, so math pow sin 3,\n5 is an error because it tries to give sin the arguments 3 and 5.\nWhen in doubt, use parentheses.\n\nmath ignores whitespace between arguments and takes its input as\nmultiple arguments (internally joined with a space), so math 2 +2 and\nmath \"2 +    2\" work the same.  math 2 2 is an error.\n\nThe following options are available:\n\n-s N or --scale N\n       Sets the scale of the result.  N must be an integer or the\n       word \"max\" for the maximum scale.  A scale of zero causes\n       results to be truncated, not rounded. Any non-integer\n       component is thrown away.  So 3/2 returns 1 rather than 2\n       which 1.5 would normally round to.  This is for compatibility\n       with bc which was the basis for this command prior to fish\n       3.0.0.  Scale values greater than zero causes the result to be\n       rounded using the usual rules to the specified number of\n       decimal places.\n\n-b BASE or --base BASE\n       Sets the numeric base used for output (math always understands\n       hexadecimal numbers as input).  It currently understands \"hex\"\n       or \"16\" for hexadecimal and \"octal\" or \"8\" for octal and\n       implies a scale of 0 (other scales cause an error), so it will\n       truncate the result down to an integer.  This might change in\n       the future.  Hex numbers will be printed with a 0x prefix.\n       Octal numbers will have a prefix of 0 but aren't understood by\n       math as input.\n\n-h or --help\n       Displays help about using this command.\n\nRETURN VALUES\nIf the expression is successfully evaluated and doesn't\nover/underflow or return NaN the return status is zero (success) else\none.\n\nSYNTAX\nmath knows some operators, constants, functions and can (obviously)\nread numbers.\n\nFor numbers, . is always the radix character regardless of locale -\n2.5, not 2,5.  Scientific notation (10e5) and hexadecimal (0xFF) are\nalso available.\n\nmath allows you to use underscores as visual separators for digit\ngrouping. For example, you can write 1_000_000, 0x_89_AB_CD_EF, and\n1.234_567_e89.\n\nOPERATORS\nmath knows the following operators:\n\n+      for addition\n\n-      for subtraction\n\n* or x for multiplication. * is the glob character and needs to be\n       quoted or escaped, x needs to be followed by whitespace or it\n       looks like 0x hexadecimal notation.\n\n/      for division\n\n^      for exponentiation\n\n%      for modulo\n\n( or ) for grouping. These need to be quoted or escaped because ()\n       denotes a command substitution.\n\nThey are all used in an infix manner - 5 + 2, not + 5 2.\n\nCONSTANTS\nmath knows the following constants:\n\ne      Euler's number\n\npi     π, you know this one.  Half of Tau\n\ntau    Equivalent to 2π, or the number of radians in a circle\n\nUse them without a leading $ - pi - 3 should be about 0.\n\nFUNCTIONS\nmath supports the following functions:\n\nabs    the absolute value, with positive sign\n\nacos   arc cosine\n\nasin   arc sine\n\natan   arc tangent\n\natan2  arc tangent of two variables\n\nbitand, bitor and bitxor\n       perform bitwise operations.  These will throw away any\n       non-integer parts and interpret the rest as an int.\n\n       Note: bitnot and bitnand don't exist. This is because numbers\n       in math don't really have a width in terms of bits, and these\n       operations necessarily care about leading zeroes.\n\n       If you need to negate a specific number you can do it with an\n       xor with a mask, e.g.:\n\n          > math --base=hex bitxor 0x0F, 0xFF\n          0xF0\n\n          > math --base=hex bitxor 0x2, 0x3\n          # Here we mask with 0x3 == 0b111, so our number is 3 bits wide\n          # Only the 1 bit isn't set.\n          0x1\n\nceil   round number up to the nearest integer\n\ncos    the cosine\n\ncosh   hyperbolic cosine\n\nexp    the base-e exponential function\n\nfac    factorial - also known as x! (x * (x - 1) * (x - 2) * ... * 1)\n\nfloor  round number down to the nearest integer\n\nln     the base-e logarithm\n\nlog or log10\n       the base-10 logarithm\n\nlog2   the base-2 logarithm\n\nmax    returns the largest of the given numbers - this takes an\n       arbitrary number of arguments (but at least one)\n\nmin    returns the smallest of the given numbers - this takes an\n       arbitrary number of arguments (but at least one)\n\nncr    \"from n choose r\" combination function - how many subsets of\n       size r can be taken from n (order doesn't matter)\n\nnpr    the number of subsets of size r that can be taken from a set\n       of n elements (including different order)\n\npow(x,y)\n       returns x to the y (and can be written as x ^ y)\n\nround  rounds to the nearest integer, away from 0\n\nsin    the sine function\n\nsinh   the hyperbolic sine\n\nsqrt   the square root - (can also be written as x ^ 0.5)\n\ntan    the tangent\n\ntanh   the hyperbolic tangent\n\nAll of the trigonometric functions use radians (the pi-based scale,\nnot 360°).\n\nEXAMPLES\nmath 1+1 outputs 2.\n\nmath $status - 128 outputs the numerical exit status of the last\ncommand minus 128.\n\nmath 10 / 6 outputs 1.666667.\n\nmath -s0 10.0 / 6.0 outputs 1.\n\nmath -s3 10 / 6 outputs 1.666.\n\nmath \"sin(pi)\" outputs 0.\n\nmath 5 \\* 2 or math \"5 * 2\" or math 5 \"*\" 2 all output 10.\n\nmath 0xFF outputs 255, math 0 x 3 outputs 0 (because it computes 0\nmultiplied by 3).\n\nmath bitand 0xFE, 0x2e outputs 46.\n\nmath \"bitor(9,2)\" outputs 11.\n\nmath --base=hex 192 prints 0xc0.\n\nmath 'ncr(49,6)' prints 13983816 - that's the number of possible\npicks in 6-from-49 lotto.\n\nmath max 5,2,3,1 prints 5.\n\nCOMPATIBILITY NOTES\nFish 1.x and 2.x releases relied on the bc command for handling math\nexpressions. Starting with fish 3.0.0 fish uses the tinyexpr library\nand evaluates the expression without the involvement of any external\ncommands.\n\nYou don't need to use -- before the expression, even if it begins\nwith a minus sign which might otherwise be interpreted as an invalid\noption. If you do insert -- before the expression, it will cause\noption scanning to stop just like for every other command and it\nwon't be part of the expression.",
        "args": "math [(-s | --scale) N] [(-b | --base) BASE] EXPRESSION ..."
    },
    "not": {
        "shortDescription": "negate the exit status of a job",
        "description": "not negates the exit status of another command. If the exit status is\nzero, not returns 1. Otherwise, not returns 0.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\nThe following code reports an error and exits if no file named spoon\ncan be found.\n\n   if not test -f spoon\n       echo There is no spoon\n       exit 1\n   end",
        "args": "not COMMAND [OPTIONS ...]"
    },
    "or": {
        "shortDescription": "conditionally execute a command",
        "description": "or is used to execute a command if the previous command was not\nsuccessful (returned a status of something other than 0).\n\nor statements may be used as part of the condition in an if or while\nblock.\n\nor does not change the current exit status itself, but the command it\nruns most likely will. The exit status of the last foreground command\nto exit can always be accessed using the $status variable.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\nThe following code runs the make command to build a program. If the\nbuild succeeds, the program is installed. If either step fails, make\nclean is run, which removes the files created by the build process.\n\n   make; and make install; or make clean\n\nSEE ALSO\n\n• and command",
        "args": "COMMAND1; or COMMAND2"
    },
    "path": {
        "shortDescription": "manipulate and check paths",
        "description": "path performs operations on paths.\n\nPATH arguments are taken from the command line unless standard input\nis connected to a pipe or a file, in which case they are read from\nstandard input, one PATH per line. It is an error to supply PATH\narguments on both the command line and on standard input.\n\nArguments starting with - are normally interpreted as switches; --\ncauses the following arguments not to be treated as switches even if\nthey begin with -. Switches and required arguments are recognized\nonly on the command line.\n\nWhen a path starts with -, path filter and path normalize will\nprepend ./ on output to avoid it being interpreted as an option\notherwise, so it's safe to pass path's output to other commands that\ncan handle relative paths.\n\nAll subcommands accept a -q or --quiet switch, which suppresses the\nusual output but exits with the documented status. In this case these\ncommands will quit early, without reading all of the available input.\n\nAll subcommands also accept a -Z or --null-out switch, which makes\nthem print output separated with NUL instead of newlines. This is for\nfurther processing, e.g. passing to another path, or xargs -0. This\nis not recommended when the output goes to the terminal or a command\nsubstitution.\n\nAll subcommands also accept a -z or --null-in switch, which makes\nthem accept arguments from stdin separated with NULL-bytes. Since\nUnix paths can't contain NULL, that makes it possible to handle all\npossible paths and read input from e.g. find -print0. If arguments\nare given on the commandline this has no effect. This should mostly\nbe unnecessary since path automatically starts splitting on NULL if\none appears in the first PATH_MAX bytes, PATH_MAX being the operating\nsystem's maximum length for a path plus a NULL byte.\n\nSome subcommands operate on the paths as strings and so work on\nnonexistent paths, while others need to access the paths themselves\nand so filter out nonexistent paths.\n\nThe following subcommands are available.\n\nBASENAME SUBCOMMAND\n\n   path basename [-z | --null-in] [-Z | --null-out] [-q | --quiet] [PATH ...]\n\npath basename returns the last path component of the given path, by\nremoving the directory prefix and removing trailing slashes. In other\nwords, it is the part that is not the dirname. For files you might\ncall it the \"filename\".\n\nIt returns 0 if there was a basename, i.e. if the path wasn't empty\nor just slashes.\n\n   Examples\n\n   > path basename ./foo.mp4\n   foo.mp4\n\n   > path basename ../banana\n   banana\n\n   > path basename /usr/bin/\n   bin\n\n   > path basename /usr/bin/*\n   # This prints all files in /usr/bin/\n   # A selection:\n   cp\n   fish\n   grep\n   rm\n\nDIRNAME SUBCOMMAND\n\n   path dirname [-z | --null-in] [-Z | --null-out] [-q | --quiet] [PATH ...]\n\npath dirname returns the dirname for the given path. This is the part\nbefore the last \"/\", discounting trailing slashes. In other words, it\nis the part that is not the basename (discounting superfluous\nslashes).\n\nIt returns 0 if there was a dirname, i.e. if the path wasn't empty or\njust slashes.\n\n   Examples\n\n   > path dirname ./foo.mp4\n   .\n\n   > path dirname ../banana\n   ..\n\n   > path dirname /usr/bin/\n   /usr\n\nEXTENSION SUBCOMMAND\n\n   path extension [-z | --null-in] [-Z | --null-out] [-q | --quiet] [PATH ...]\n\npath extension returns the extension of the given path. This is the\npart after (and including) the last \".\", unless that \".\" followed a\n\"/\" or the basename is \".\" or \"..\", in which case there is no\nextension and an empty line is printed.\n\nIf the filename ends in a \".\", only a \".\" is printed.\n\nIt returns 0 if there was an extension.\n\n   Examples\n\n   > path extension ./foo.mp4\n   .mp4\n\n   > path extension ../banana\n   # an empty line, status 1\n\n   > path extension ~/.config\n   # an empty line, status 1\n\n   > path extension ~/.config.d\n   .d\n\n   > path extension ~/.config.\n   .\n\n   > set -l path (path change-extension '' ./foo.mp4)\n   > set -l extension (path extension ./foo.mp4)\n   > echo $path$extension\n   # reconstructs the original path again.\n   ./foo.mp4\n\nFILTER SUBCOMMAND\n\n   path filter [-z | --null-in] [-Z | --null-out] [-q | --quiet] \\\n       [-d] [-f] [-l] [-r] [-w] [-x] \\\n       [-v | --invert] [(-t | --type) TYPE] [(-p | --perm) PERMISSION] [PATH ...]\n\npath filter returns all of the given paths that match the given\nchecks. In all cases, the paths need to exist, nonexistent paths are\nalways filtered.\n\nThe available filters are:\n\n• -t or --type with the options: \"dir\", \"file\", \"link\", \"block\",\n  \"char\", \"fifo\" and \"socket\", in which case the path needs to be a\n  directory, file, link, block device, character device, named pipe\n  or socket, respectively.\n\n• -d, -f and -l are short for --type=dir, --type=file and\n  --type=link, respectively. There are no shortcuts for the other\n  types.\n\n• -p or --perm with the options: \"read\", \"write\", and \"exec\", as well\n  as \"suid\", \"sgid\", \"user\" (referring to the path owner) and \"group\"\n  (referring to the path's group), in which case the path needs to\n  have all of the given permissions for the current user.\n\n• -r, -w and -x are short for --perm=read, --perm=write and\n  --perm=exec, respectively. There are no shortcuts for the other\n  permissions.\n\nNote that the path needs to be any of the given types, but have all\nof the given permissions. This is because having a path that is both\nwritable and executable makes sense, but having a path that is both a\ndirectory and a file doesn't. Links will count as the type of the\nlinked-to file, so links to files count as files, links to\ndirectories count as directories.\n\nThe filter options can either be given as multiple options, or\ncomma-separated - path filter -t dir,file or path filter --type dir\n--type file are equivalent.\n\nWith --invert, the meaning of the filtering is inverted - any path\nthat wouldn't pass (including by not existing) passes, and any path\nthat would pass fails.\n\nWhen a path starts with -, path filter will prepend ./ to avoid it\nbeing interpreted as an option otherwise.\n\nIt returns 0 if at least one path passed the filter.\n\npath is is shorthand for path filter -q, i.e. just checking without\nproducing output, see The is subcommand.\n\n   Examples\n\n   > path filter /usr/bin /usr/argagagji\n   # The (hopefully) nonexistent argagagji is filtered implicitly:\n   /usr/bin\n\n   > path filter --type file /usr/bin /usr/bin/fish\n   # Only fish is a file\n   /usr/bin/fish\n\n   > path filter --type file,dir --perm exec,write /usr/bin/fish /home/<USER>/home/<USER>/usr/bin/fish /home/<USER>"-f\" is \"--type=file\", \"-d\" is \"--type=dir\",\n   # \"-x\" is short for \"--perm=exec\" and \"-w\" short for \"--perm=write\"!\n   /home/<USER>/*\n   # Prints all possible commands - the first entry of each name is what fish would execute!\n\nIS SUBCOMMAND\n\n   path is [-z | --null-in] [-Z | --null-out] [-q | --quiet] \\\n       [-d] [-f] [-l] [-r] [-w] [-x] \\\n       [-v | --invert] [(-t | --type) TYPE] [(-p | --perm) PERMISSION] [PATH ...]\n\npath is is short for path filter -q. It returns true if any of the\ngiven files passes the filter, but does not produce any output.\n\n--quiet can still be passed for compatibility but is redundant. The\noptions are the same as for path filter.\n\n   Examples\n\n   > path is /usr/bin /usr/argagagji\n   # /usr/bin exists, so this returns a status of 0 (true). It prints nothing.\n   > path is /usr/argagagji\n   # /usr/argagagji does not, so this returns a status of 1 (false). It also prints nothing.\n   > path is -fx /bin/sh\n   # /bin/sh is usually an executable file, so this returns true.\n\nMTIME SUBCOMMAND\n\n   path mtime [-z | --null-in] [-Z | --null-out] [-q | --quiet] [-R | --relative] [PATH ...]\n\npath mtime returns the last modification time (\"mtime\" in unix\njargon) of the given paths, in seconds since the unix epoch (the\nbeginning of the 1st of January 1970).\n\nWith --relative (or -R), it prints the number of seconds since the\nmodification time. It only reads the current time once at start, so\nin case multiple paths are given the times are all relative to the\nstart of path mtime -R running.\n\nIf you want to know if a file is newer or older than another file,\nconsider using test -nt instead. See the test documentation.\n\nIt returns 0 if reading mtime for any path succeeded.\n\n   Examples\n\n   > date +%s\n   # This prints the current time as seconds since the epoch\n   1657217847\n\n   > path mtime /etc/\n   1657213796\n\n   > path mtime -R /etc/\n   4078\n   # So /etc/ on this system was last modified a little over an hour ago\n\n   # This is the same as\n   > math (date +%s) - (path mtime /etc/)\n\nNORMALIZE SUBCOMMAND\n\n   path normalize [-z | --null-in] [-Z | --null-out] [-q | --quiet] [PATH ...]\n\npath normalize returns the normalized versions of all paths. That\nmeans it squashes duplicate \"/\" (except for two leading \"//\"),\ncollapses \"../\" with earlier components and removes \".\" components.\n\nUnlike realpath or path resolve, it does not make the paths absolute.\nIt also does not resolve any symlinks. As such it can operate on\nnon-existent paths.\n\nBecause it operates on paths as strings and doesn't resolve symlinks,\nit works sort of like pwd -L and cd. E.g. path normalize link/.. will\nreturn ., just like cd link; cd .. would return to the current\ndirectory. For a physical view of the filesystem, see path resolve.\n\nLeading \"./\" components are usually removed. But when a path starts\nwith -, path normalize will add it instead to avoid confusion with\noptions.\n\nIt returns 0 if any normalization was done, i.e. any given path\nwasn't in canonical form.\n\n   Examples\n\n   > path normalize /usr/bin//../../etc/fish\n   # The \"//\" is squashed and the \"..\" components neutralize the components before\n   /etc/fish\n\n   > path normalize /bin//bash\n   # The \"//\" is squashed, but /bin isn't resolved even if your system links it to /usr/bin.\n   /bin/bash\n\n   > path normalize ./my/subdirs/../sub2\n   my/sub2\n\n   > path normalize -- -/foo\n   ./-/foo\n\nRESOLVE SUBCOMMAND\n\n   path resolve [-z | --null-in] [-Z | --null-out] [-q | --quiet] [PATH ...]\n\npath resolve returns the normalized, physical and absolute versions\nof all paths. That means it resolves symlinks and does what path\nnormalize does: it squashes duplicate \"/\", collapses \"../\" with\nearlier components and removes \".\" components. Then it turns that\npath into the absolute path starting from the filesystem root \"/\".\n\nIt is similar to realpath, as it creates the \"real\", canonical\nversion of the path. However, for paths that can't be resolved, e.g.\nif they don't exist or form a symlink loop, it will resolve as far as\nit can and normalize the rest.\n\nBecause it resolves symlinks, it works sort of like pwd -P. E.g. path\nresolve link/.. will return the parent directory of what the link\npoints to, just like cd link; cd (pwd -P)/.. would go to it. For a\nlogical view of the filesystem, see path normalize.\n\nIt returns 0 if any normalization or resolution was done, i.e. any\ngiven path wasn't in canonical form.\n\n   Examples\n\n   > path resolve /bin//sh\n   # The \"//\" is squashed, and /bin is resolved if your system links it to /usr/bin.\n   # sh here is bash (this is common on linux systems)\n   /usr/bin/bash\n\n   > path resolve /bin/foo///bar/../baz\n   # Assuming /bin exists and is a symlink to /usr/bin, but /bin/foo doesn't.\n   # This resolves the /bin/ and normalizes the nonexistent rest:\n   /usr/bin/foo/baz\n\nCHANGE-EXTENSION SUBCOMMAND\n\n   path change-extension [-z | --null-in] [-Z | --null-out] \\\n       [-q | --quiet] EXTENSION [PATH ...]\n\npath change-extension returns the given paths, with their extension\nchanged to the given new extension. The extension is the part after\n(and including) the last \".\", unless that \".\" followed a \"/\" or the\nbasename is \".\" or \"..\", in which case there is no previous extension\nand the new one is simply added.\n\nIf the extension is empty, any previous extension is stripped, along\nwith the \".\". This is, of course, the inverse of path extension.\n\nOne leading dot on the extension is ignored, so \".mp3\" and \"mp3\" are\ntreated the same.\n\nIt returns 0 if it was given any paths.\n\n   Examples\n\n   > path change-extension mp4 ./foo.wmv\n   ./foo.mp4\n\n   > path change-extension .mp4 ./foo.wmv\n   ./foo.mp4\n\n   > path change-extension '' ../banana\n   ../banana\n   # but status 1, because there was no extension.\n\n   > path change-extension '' ~/.config\n   /home/<USER>/.config\n   # status 1\n\n   > path change-extension '' ~/.config.d\n   /home/<USER>/.config\n   # status 0\n\n   > path change-extension '' ~/.config.\n   /home/<USER>/.config\n   # status 0\n\nSORT SUBCOMMAND\n\n   path sort [-z | --null-in] [-Z | --null-out] \\\n       [-q | --quiet] [-r | --reverse] \\\n       [--key=basename|dirname|path] [PATH ...]\n\npath sort returns the given paths in sorted order. They are sorted in\nthe same order as globs - alphabetically, but with runs of numerical\ndigits compared numerically.\n\nWith --reverse or -r the sort is reversed.\n\nWith --key= only the given part of the path is compared, e.g.\n--key=dirname causes only the dirname to be compared, --key=basename\nonly the basename and --key=path causes the entire path to be\ncompared (this is the default).\n\nWith --unique or -u the sort is deduplicated, meaning only the first\nof a run that have the same key is kept. So if you are sorting by\nbasename, then only the first of each basename is used.\n\nThe sort used is stable, so sorting first by basename and then by\ndirname works and causes the files to be grouped according to\ndirectory.\n\nIt currently returns 0 if it was given any paths.\n\n   Examples\n\n   > path sort 10-foo 2-bar\n   2-bar\n   10-foo\n\n   > path sort --reverse 10-foo 2-bar\n   10-foo\n   2-bar\n\n   > path sort --unique --key=basename $fish_function_path/*.fish\n   # prints a list of all function files fish would use, sorted by name.\n\nCOMBINING PATH\npath is meant to be easy to combine with itself, other tools and\nfish.\n\nThis is why\n\n• path's output is automatically split by fish if it goes into a\n  command substitution, so just doing (path ...) handles all paths,\n  even those containing newlines, correctly\n\n• path has --null-in to handle null-delimited input (typically\n  automatically detected!), and --null-out to pass on null-delimited\n  output\n\nSome examples of combining path:\n\n   # Expand all paths in the current directory, leave only executable files, and print their resolved path\n   path filter -zZ -xf -- * | path resolve -z\n\n   # The same thing, but using find (note -maxdepth needs to come first or find will scream)\n   # (this also depends on your particular version of find)\n   # Note the `-z` is unnecessary for any sensible version of find - if `path` sees a NULL,\n   # it will split on NULL automatically.\n   find . -maxdepth 1 -type f -executable -print0 | path resolve -z\n\n   set -l paths (path filter -p exec $PATH/fish -Z | path resolve)",
        "args": "path basename GENERAL_OPTIONS [PATH ...]\npath dirname GENERAL_OPTIONS  [PATH ...]\npath extension GENERAL_OPTIONS [PATH ...]\npath filter GENERAL_OPTIONS [-v | --invert]\n    [-d] [-f] [-l] [-r] [-w] [-x]\n    [(-t | --type) TYPE] [(-p | --perm) PERMISSION] [PATH ...]\npath is GENERAL_OPTIONS [(-v | --invert)] [(-t | --type) TYPE]\n    [-d] [-f] [-l] [-r] [-w] [-x]\n    [(-p | --perm) PERMISSION] [PATH ...]\npath mtime GENERAL_OPTIONS [(-R | --relative)] [PATH ...]\npath normalize GENERAL_OPTIONS [PATH ...]\npath resolve GENERAL_OPTIONS [PATH ...]\npath change-extension GENERAL_OPTIONS EXTENSION [PATH ...]\npath sort GENERAL_OPTIONS [-r | --reverse]\n    [-u | --unique] [--key=basename|dirname|path] [PATH ...]\n\nGENERAL_OPTIONS\n    [-z | --null-in] [-Z | --null-out] [-q | --quiet]"
    },
    "printf": {
        "shortDescription": "Display formatted text",
        "description": "The `printf` command formats and prints text according to a specified format string. Unlike `echo`, `printf` does not append a newline unless explicitly included in the format.",
        "args": "FORMAT [ARGUMENT...]"
    },
    "pwd": {
        "shortDescription": "output the current working directory",
        "description": "NOTE: This page documents the fish builtin pwd.  To see the\ndocumentation on the pwd command you might have, use command man pwd.\n\npwd outputs (prints) the current working directory.\n\nThe following options are available:\n\n-L or --logical\n       Output the logical working directory, without resolving\n       symlinks (default behavior).\n\n-P or --physical\n       Output the physical working directory, with symlinks resolved.\n\n-h or --help\n       Displays help about using this command.\n\nSEE ALSO\nNavigate directories using the directory history or the directory\nstack",
        "args": "pwd [-P | --physical]\npwd [-L | --logical]"
    },
    "random": {
        "shortDescription": "generate random number",
        "description": "random generates a pseudo-random integer from a uniform distribution.\nThe range (inclusive) depends on the arguments.\n\nNo arguments indicate a range of 0 to 32767 (inclusive).\n\nIf one argument is specified, the internal engine will be seeded with\nthe argument for future invocations of random and no output will be\nproduced.\n\nTwo arguments indicate a range from START to END (both START and END\nincluded).\n\nThree arguments indicate a range from START to END with a spacing of\nSTEP between possible outputs.\n\nrandom choice will select one random item from the succeeding\narguments.\n\nThe -h or --help option displays help about using this command.\n\nNote that seeding the engine will NOT give the same result across\ndifferent systems.\n\nYou should not consider random cryptographically secure, or even\nstatistically accurate.\n\nEXAMPLE\nThe following code will count down from a random even number between\n10 and 20 to 1:\n\n   for i in (seq (random 10 2 20) -1 1)\n       echo $i\n   end\n\nAnd this will open a random picture from any of the subdirectories:\n\n   open (random choice **.jpg)\n\nOr, to only get even numbers from 2 to 20:\n\n   random 2 2 20\n\nOr odd numbers from 1 to 3:\n\n   random 1 2 3 # or 1 2 4",
        "args": "random\nrandom SEED\nrandom START END\nrandom START STEP END\nrandom choice [ITEMS ...]"
    },
    "read": {
        "shortDescription": "read line of input into variables",
        "description": "read reads from standard input and either writes the result back to\nstandard output (for use in command substitution), or stores the\nresult in one or more shell variables. By default, read reads a\nsingle line and splits it into variables on spaces or tabs.\nAlternatively, a null character or a maximum number of characters can\nbe used to terminate the input, and other delimiters can be given.\nUnlike other shells, there is no default variable (such as REPLY) for\nstoring the result - instead, it is printed on standard output.\n\nThe following options are available:\n\n-c CMD or --command CMD\n       Sets the initial string in the interactive mode command buffer\n       to CMD.\n\n-d or --delimiter DELIMITER\n       Splits on DELIMITER. DELIMITER will be used as an entire\n       string to split on, not a set of characters.\n\n-g or --global\n       Makes the variables global.\n\n-s or --silent\n       Masks characters written to the terminal, replacing them with\n       asterisks. This is useful for reading things like passwords or\n       other sensitive information.\n\n-f or --function\n       Scopes the variable to the currently executing function. It is\n       erased when the function ends.\n\n-l or --local\n       Scopes the variable to the currently executing block. It is\n       erased when the block ends. Outside of a block, this is the\n       same as --function.\n\n-n or --nchars NCHARS\n       Makes read return after reading NCHARS characters or the end\n       of the line, whichever comes first.\n\n-p or --prompt PROMPT_CMD\n       Uses the output of the shell command PROMPT_CMD as the prompt\n       for the interactive mode. The default prompt command is\n       set_color green; echo read; set_color normal; echo \"> \"\n\n-P or --prompt-str PROMPT_STR\n       Uses the PROMPT_STR as the prompt for the interactive mode. It\n       is equivalent to echo $PROMPT_STR and is provided solely to\n       avoid the need to frame the prompt as a command. All special\n       characters in the string are automatically escaped before\n       being passed to the echo command.\n\n-R or --right-prompt RIGHT_PROMPT_CMD\n       Uses the output of the shell command RIGHT_PROMPT_CMD as the\n       right prompt for the interactive mode. There is no default\n       right prompt command.\n\n-S or --shell\n       Enables syntax highlighting, tab completions and command\n       termination suitable for entering shellscript code in the\n       interactive mode. NOTE: Prior to fish 3.0, the short opt for\n       --shell was -s, but it has been changed for compatibility with\n       bash's -s short opt for --silent.\n\n-t -or --tokenize\n       Causes read to split the input into variables by the shell's\n       tokenization rules. This means it will honor quotes and\n       escaping. This option is of course incompatible with other\n       options to control splitting like --delimiter and does not\n       honor IFS (like fish's tokenizer). It saves the tokens in the\n       manner they'd be passed to commands on the commandline, so\n       e.g. a\\ b is stored as a b. Note that currently it leaves\n       command substitutions intact along with the parentheses.\n\n-u or --unexport\n       Prevents the variables from being exported to child processes\n       (default behaviour).\n\n-U or --universal\n       Causes the specified shell variable to be made universal.\n\n-x or --export\n       Exports the variables to child processes.\n\n-a or --list\n       Stores the result as a list in a single variable. This option\n       is also available as --array for backwards compatibility.\n\n-z or --null\n       Marks the end of the line with the NUL character, instead of\n       newline. This also disables interactive mode.\n\n-L or --line\n       Reads each line into successive variables, and stops after\n       each variable has been filled. This cannot be combined with\n       the --delimiter option.\n\nWithout the --line option, read reads a single line of input from\nstandard input, breaks it into tokens, and then assigns one token to\neach variable specified in VARIABLES. If there are more tokens than\nvariables, the complete remainder is assigned to the last variable.\n\nIf no option to determine how to split like --delimiter, --line or\n--tokenize is given, the variable IFS is used as a list of characters\nto split on. Relying on the use of IFS is deprecated and this\nbehaviour will be removed in future versions. The default value of\nIFS contains space, tab and newline characters. As a special case, if\nIFS is set to the empty string, each character of the input is\nconsidered a separate token.\n\nWith the --line option, read reads a line of input from standard\ninput into each provided variable, stopping when each variable has\nbeen filled. The line is not tokenized.\n\nIf no variable names are provided, read enters a special case that\nsimply provides redirection from standard input to standard output,\nuseful for command substitution. For instance, the fish shell command\nbelow can be used to read data that should be provided via a command\nline argument from the console instead of hardcoding it in the\ncommand itself, allowing the command to both be reused as-is in\nvarious contexts with different input values and preventing possibly\nsensitive text from being included in the shell history:\n\n   mysql -uuser -p(read)\n\nWhen running in this mode, read does not split the input in any way\nand text is redirected to standard output without any further\nprocessing or manipulation.\n\nIf -a or --array is provided, only one variable name is allowed and\nthe tokens are stored as a list in this variable.\n\nSee the documentation for set for more details on the scoping rules\nfor variables.\n\nWhen read reaches the end-of-file (EOF) instead of the terminator,\nthe exit status is set to 1.  Otherwise, it is set to 0.\n\nIn order to protect the shell from consuming too many system\nresources, read will only consume a maximum of 100 MiB (104857600\nbytes); if the terminator is not reached before this limit then\nVARIABLE is set to empty and the exit status is set to 122. This\nlimit can be altered with the fish_read_limit variable. If set to 0\n(zero), the limit is removed.\n\nEXAMPLE\nread has a few separate uses.\n\nThe following code stores the value 'hello' in the shell variable\nfoo.\n\n   echo hello|read foo\n\nThe while command is a neat way to handle command output\nline-by-line:\n\n   printf '%s\\n' line1 line2 line3 line4 | while read -l foo\n                     echo \"This is another line: $foo\"\n                 end\n\nDelimiters given via \"-d\" are taken as one string:\n\n   echo a==b==c | read -d == -l a b c\n   echo $a # a\n   echo $b # b\n   echo $c # c\n\n--tokenize honors quotes and escaping like the shell's argument\npassing:\n\n   echo 'a\\ b' | read -t first second\n   echo $first # outputs \"a b\", $second is empty\n\n   echo 'a\"foo bar\"b (command echo wurst)*\" \"{a,b}' | read -lt -l a b c\n   echo $a # outputs 'afoo barb' (without the quotes)\n   echo $b # outputs '(command echo wurst)* {a,b}' (without the quotes)\n   echo $c # nothing\n\nFor an example on interactive use, see Querying for user input.",
        "args": "read [OPTIONS] [VARIABLE ...]"
    },
    "realpath": {
        "shortDescription": "Resolve and print the absolute path",
        "description": "Convert each provided path to its absolute, canonical form by resolving symbolic links and relative path components.",
        "args": "PATH..."
    },
    "return": {
        "shortDescription": "stop the current inner function",
        "description": "return halts a currently running function.  The exit status is set to\nN if it is given.  If return is invoked outside of a function or dot\nscript it is equivalent to exit.\n\nIt is often added inside of a conditional block such as an if\nstatement or a switch statement to conditionally stop the executing\nfunction and return to the caller; it can also be used to specify the\nexit status of a function.\n\nIf at the top level of a script, it exits with the given status, like\nexit.  If at the top level in an interactive session, it will set\nstatus, but not exit the shell.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\nAn implementation of the false command as a fish function:\n\n   function false\n       return 1\n   end",
        "args": "return [N]"
    },
    "set": {
        "shortDescription": "display and change shell variables",
        "description": "set manipulates shell variables.\n\nIf both NAME and VALUE are provided, set assigns any values to\nvariable NAME.  Variables in fish are lists, multiple values are\nallowed.  One or more variable INDEX can be specified including\nranges (not for all options.)\n\nIf no VALUE is given, the variable will be set to the empty list.\n\nIf set is ran without arguments, it prints the names and values of\nall shell variables in sorted order.  Passing scope or export flags\nallows filtering this to only matching variables, so set --local\nwould only show local variables.\n\nWith --erase and optionally a scope flag set will erase the matching\nvariable (or the variable of that name in the smallest possible\nscope).\n\nWith --show, set will describe the given variable names, explaining\nhow they have been defined - in which scope with which values and\noptions.\n\nThe following options control variable scope:\n\n-U or --universal\n       Sets a universal variable.  The variable will be immediately\n       available to all the user's fish instances on the machine, and\n       will be persisted across restarts of the shell.\n\n-f or --function\n       Sets a variable scoped to the executing function.  It is\n       erased when the function ends.\n\n-l or --local\n       Sets a locally-scoped variable in this block.  It is erased\n       when the block ends.  Outside of a block, this is the same as\n       --function.\n\n-g or --global\n       Sets a globally-scoped variable.  Global variables are\n       available to all functions running in the same shell.  They\n       can be modified or erased.\n\nThese options modify how variables operate:\n\n--export or -x\n       Causes the specified shell variable to be exported to child\n       processes (making it an \"environment variable\").\n\n--unexport or -u\n       Causes the specified shell variable to NOT be exported to\n       child processes.\n\n--path Treat specified variable as a path variable; variable will be\n       split on colons (:) and will be displayed joined by colons\n       when quoted (echo \"$PATH\") or exported.\n\n--unpath\n       Causes variable to no longer be treated as a path variable.\n       Note: variables ending in \"PATH\" are automatically path\n       variables.\n\nFurther options:\n\n-a or --append NAME VALUE ...\n       Appends VALUES to the current set of values for variable NAME.\n       Can be used with --prepend to both append and prepend at the\n       same time.  This cannot be used when assigning to a variable\n       slice.\n\n-p or --prepend NAME VALUE ...\n       Prepends VALUES to the current set of values for variable\n       NAME.  This can be used with --append to both append and\n       prepend at the same time.  This cannot be used when assigning\n       to a variable slice.\n\n-e or --erase NAME*[*INDEX]\n       Causes the specified shell variables to be erased.  Supports\n       erasing from multiple scopes at once.  Individual items in a\n       variable at INDEX in brackets can be specified.\n\n-q or --query NAME*[*INDEX]\n       Test if the specified variable names are defined.  If an INDEX\n       is provided, check for items at that slot.  Does not output\n       anything, but the shell status is set to the number of\n       variables specified that were not defined, up to a maximum of\n       255.  If no variable was given, it also returns 255.\n\n-n or --names\n       List only the names of all defined variables, not their value.\n       The names are guaranteed to be sorted.\n\n-S or --show\n       Shows information about the given variables.  If no variable\n       names are given then all variables are shown in sorted order.\n       It shows the scopes the given variables are set in, along with\n       the values in each and whether or not it is exported.  No\n       other flags can be used with this option.\n\n-L or --long\n       Do not abbreviate long values when printing set variables.\n\n-h or --help\n       Displays help about using this command.\n\nIf a variable is set to more than one value, the variable will be a\nlist with the specified elements.  If a variable is set to zero\nelements, it will become a list with zero elements.\n\nIf the variable name is one or more list elements, such as PATH[1 3\n7], only those list elements specified will be changed.  If you\nspecify a negative index when expanding or assigning to a list\nvariable, the index will be calculated from the end of the list.  For\nexample, the index -1 means the last index of a list.\n\nThe scoping rules when creating or updating a variable are:\n\n• Variables may be explicitly set as universal, global, function, or\n  local.  Variables with the same name but in a different scope will\n  not be changed.\n\n• If the scope of a variable is not explicitly set but a variable by\n  that name has been previously defined, the scope of the existing\n  variable is used.  If the variable is already defined in multiple\n  scopes, the variable with the narrowest scope will be updated.\n\n• If a variable's scope is not explicitly set and there is no\n  existing variable by that name, the variable will be local to the\n  currently executing function.  Note that this is different from\n  using the -l or --local flag, in which case the variable will be\n  local to the most-inner currently executing block, while without\n  them the variable will be local to the function as a whole.  If no\n  function is executing, the variable will be set in the global\n  scope.\n\nThe exporting rules when creating or updating a variable are\nidentical to the scoping rules for variables:\n\n• Variables may be explicitly set to either exported or not exported.\n  When an exported variable goes out of scope, it is unexported.\n\n• If a variable is not explicitly set to be exported or not exported,\n  but has been previously defined, the previous exporting rule for\n  the variable is kept.\n\n• If a variable is not explicitly set to be either exported or\n  unexported and has never before been defined, the variable will not\n  be exported.\n\nIn query mode, the scope to be examined can be specified.  Whether\nthe variable has to be a path variable or exported can also be\nspecified.\n\nIn erase mode, if variable indices are specified, only the specified\nslices of the list variable will be erased.\n\nset requires all options to come before any other arguments.  For\nexample, set flags -l will have the effect of setting the value of\nthe variable flags to '-l', not making the variable local.\n\nEXIT STATUS\nIn assignment mode, set does not modify the exit status, but passes\nalong whatever status was set, including by command substitutions.\nThis allows capturing the output and exit status of a subcommand,\nlike in if set output (command).\n\nIn query mode, the exit status is the number of variables that were\nnot found.\n\nIn erase mode, set exits with a zero exit status in case of success,\nwith a non-zero exit status if the commandline was invalid, if any of\nthe variables did not exist or was a special read-only variable.\n\nEXAMPLES\nPrint all global, exported variables:\n\n   > set -gx\n\nSet the value of the variable $foo to be 'hi'.:\n\n   > set foo hi\n\nAppend the value \"there\" to the variable $foo:\n\n   > set -a foo there\n\nRemove $smurf from the scope:\n\n   > set -e smurf\n\nRemove $smurf from the global and universal scopes:\n\n   > set -e -Ug smurf\n\nChange the fourth element of the $PATH list to ~/bin:\n\n   > set PATH[4] ~/bin\n\nOutputs the path to Python if type -p returns true:\n\n   if set python_path (type -p python)\n       echo \"Python is at $python_path\"\n   end\n\nSetting a variable doesn't modify $status; a command substitution\nstill will, though:\n\n   > echo $status\n   0\n   > false\n   > set foo bar\n   > echo $status\n   1\n   > true\n   > set foo banana (false)\n   > echo $status\n   1\n\nVAR=VALUE command sets a variable for just one command, like other\nshells.  This runs fish with a temporary home directory:\n\n   > HOME=(mktemp -d) fish\n\n(which is essentially the same as):\n\n   > begin; set -lx HOME (mktemp -d); fish; end\n\nNOTES\n\n• Fish versions prior to 3.0 supported the syntax set PATH[1] PATH[4]\n  /bin /sbin, which worked like set PATH[1 4] /bin /sbin.",
        "args": "set\nset (-f | --function) (-l | local) (-g | --global) (-U | --universal)\nset [-Uflg] NAME [VALUE ...]\nset [-Uflg] NAME[[INDEX ...]] [VALUE ...]\nset (-a | --append) [-flgU] NAME VALUE ...\nset (-q | --query) (-e | --erase) [-flgU] [NAME][[INDEX]] ...]\nset (-S | --show) [NAME ...]"
    },
    "set_color": {
        "shortDescription": "set the terminal color",
        "description": "set_color is used to control the color and styling of text in the\nterminal. VALUE describes that styling. VALUE can be a reserved color\nname like red or an RGB color value given as 3 or 6 hexadecimal\ndigits (\"F27\" or \"FF2277\"). A special keyword normal resets text\nformatting to terminal defaults.\n\nValid colors include:\n\n   • black, red, green, yellow, blue, magenta, cyan, white\n\n   • brblack, brred, brgreen, bryellow, brblue, brmagenta, brcyan,\n     brwhite\n\nThe br- (as in 'bright') forms are full-brightness variants of the 8\nstandard-brightness colors on many terminals. brblack has higher\nbrightness than black - towards gray.\n\nAn RGB value with three or six hex digits, such as A0FF33 or f2f can\nbe used. Fish will choose the closest supported color. A three digit\nvalue is equivalent to specifying each digit twice; e.g., set_color\n2BC is the same as set_color 22BBCC. Hexadecimal RGB values can be in\nlower or uppercase. Depending on the capabilities of your terminal\n(and the level of support set_color has for it) the actual color may\nbe approximated by a nearby matching reserved color name or set_color\nmay not have an effect on color.\n\nA second color may be given as a desired fallback color. e.g.\nset_color 124212 brblue will instruct set_color to use brblue if a\nterminal is not capable of the exact shade of grey desired. This is\nvery useful when an 8 or 16 color terminal might otherwise not use a\ncolor.\n\nThe following options are available:\n\n-b or --background COLOR\n       Sets the background color.\n\n-c or --print-colors\n       Prints the given colors or a colored list of the 16 named\n       colors.\n\n-o or --bold\n       Sets bold mode.\n\n-d or --dim\n       Sets dim mode.\n\n-i or --italics\n       Sets italics mode.\n\n-r or --reverse\n       Sets reverse mode.\n\n-u or --underline\n       Sets underlined mode.\n\n-h or --help\n       Displays help about using this command.\n\nUsing the normal keyword will reset foreground, background, and all\nformatting back to default.\n\nNOTES\n\n1. Using the normal keyword will reset both background and foreground\n   colors to whatever is the default for the terminal.\n\n2. Setting the background color only affects subsequently written\n   characters. Fish provides no way to set the background color for\n   the entire terminal window. Configuring the window background\n   color (and other attributes such as its opacity) has to be done\n   using whatever mechanisms the terminal provides. Look for a config\n   option.\n\n3. Some terminals use the --bold escape sequence to switch to a\n   brighter color set rather than increasing the weight of text.\n\n4. set_color works by printing sequences of characters to standard\n   output. If used in command substitution or a pipe, these\n   characters will also be captured. This may or may not be\n   desirable. Checking the exit status of isatty stdout before using\n   set_color can be useful to decide not to colorize output in a\n   script.\n\nEXAMPLES\n\n   set_color red; echo \"Roses are red\"\n   set_color blue; echo \"Violets are blue\"\n   set_color 62A; echo \"Eggplants are dark purple\"\n   set_color normal; echo \"Normal is nice\" # Resets the background too\n\nTERMINAL CAPABILITY DETECTION\nFish uses some heuristics to determine what colors a terminal\nsupports to avoid sending sequences that it won't understand.\n\nIn particular it will:\n\n• Enable 256 colors if TERM contains \"xterm\", except for known\n  exceptions (like MacOS 10.6 Terminal.app)\n\n• Enable 24-bit (\"true-color\") even if the $TERM entry only reports\n  256 colors. This includes modern xterm, VTE-based terminals like\n  Gnome Terminal, Konsole and iTerm2.\n\n• Detect support for italics, dim, reverse and other modes.\n\nIf terminfo reports 256 color support for a terminal, 256 color\nsupport will always be enabled.\n\nTo force true-color support on or off, set fish_term24bit to \"1\" for\non and 0 for off - set -g fish_term24bit 1.\n\nTo debug color palette problems, tput colors may be useful to see the\nnumber of colors in terminfo for a terminal. Fish launched as fish -d\nterm_support will include diagnostic messages that indicate the color\nsupport mode in use.\n\nThe set_color command uses the terminfo database to look up how to\nchange terminal colors on whatever terminal is in use. Some systems\nhave old and incomplete terminfo databases, and lack color\ninformation for terminals that support it. Fish assumes that all\nterminals can use the [ANSI\nX3.64](https://en.wikipedia.org/wiki/ANSI_escape_code) escape\nsequences if the terminfo definition indicates a color below 16 is\nnot supported.",
        "args": "set_color [OPTIONS] VALUE"
    },
    "source": {
        "shortDescription": "evaluate contents of file",
        "description": "source evaluates the commands of the specified FILE in the current\nshell as a new block of code. This is different from starting a new\nprocess to perform the commands (i.e. fish < FILE) since the commands\nwill be evaluated by the current shell, which means that changes in\nshell variables will affect the current shell. If additional\narguments are specified after the file name, they will be inserted\ninto the argv variable. The argv variable will not include the name\nof the sourced file.\n\nfish will search the working directory to resolve relative paths but\nwill not search PATH .\n\nIf no file is specified and stdin is not the terminal, or if the file\nname - is used, stdin will be read.\n\nThe exit status of source is the exit status of the last job to\nexecute. If something goes wrong while opening or reading the file,\nsource exits with a non-zero status.\n\n. (a single period) is an alias for the source command. The use of .\nis deprecated in favour of source, and . will be removed in a future\nversion of fish.\n\nsource creates a new local scope; set --local within a sourced block\nwill not affect variables in the enclosing scope.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\n\n   source ~/.config/fish/config.fish\n   # Causes fish to re-read its initialization file.\n\nCAVEATS\nIn fish versions prior to 2.3.0, the argv variable would have a\nsingle element (the name of the sourced file) if no arguments are\npresent. Otherwise, it would contain arguments without the name of\nthe sourced file. That behavior was very confusing and unlike other\nshells such as bash and zsh.",
        "args": "source FILE [ARGUMENTS ...]\nSOMECOMMAND | source"
    },
    "status": {
        "shortDescription": "query fish runtime information",
        "description": "With no arguments, status displays a summary of the current login and\njob control status of the shell.\n\nThe following operations (subcommands) are available:\n\nis-command-substitution, -c or --is-command-substitution\n       Returns 0 if fish is currently executing a command\n       substitution.\n\nis-block, -b or --is-block\n       Returns 0 if fish is currently executing a block of code.\n\nis-breakpoint\n       Returns 0 if fish is currently showing a prompt in the context\n       of a breakpoint command. See also the fish_breakpoint_prompt\n       function.\n\nis-interactive, -i or --is-interactive\n       Returns 0 if fish is interactive - that is, connected to a\n       keyboard.\n\nis-login, -l or --is-login\n       Returns 0 if fish is a login shell - that is, if fish should\n       perform login tasks such as setting up PATH.\n\nis-full-job-control or --is-full-job-control\n       Returns 0 if full job control is enabled.\n\nis-interactive-job-control or --is-interactive-job-control\n       Returns 0 if interactive job control is enabled.\n\nis-no-job-control or --is-no-job-control\n       Returns 0 if no job control is enabled.\n\ncurrent-command\n       Prints the name of the currently-running function or command,\n       like the deprecated  variable.\n\ncurrent-commandline\n       Prints the entirety of the currently-running commandline,\n       inclusive of all jobs and operators.\n\nfilename, current-filename, -f or --current-filename\n       Prints the filename of the currently-running script. If the\n       current script was called via a symlink, this will return the\n       symlink. If the current script was received by piping into\n       source, then this will return -.\n\nbasename\n       Prints just the filename of the running script, without any\n       path components before.\n\ndirname\n       Prints just the path to the running script, without the actual\n       filename itself. This can be relative to PWD (including just\n       \".\"), depending on how the script was called. This is the same\n       as passing the filename to dirname(3). It's useful if you want\n       to use other files in the current script's directory or\n       similar.\n\nfish-path\n       Prints the absolute path to the currently executing instance\n       of fish. This is a best-effort attempt and the exact output is\n       down to what the platform gives fish. In some cases you might\n       only get \"fish\".\n\nfunction or current-function\n       Prints the name of the currently called function if able, when\n       missing displays \"Not a function\" (or equivalent translated\n       string).\n\nline-number, current-line-number, -n or --current-line-number\n       Prints the line number of the currently running script.\n\nstack-trace, print-stack-trace, -t or --print-stack-trace\n       Prints a stack trace of all function calls on the call stack.\n\njob-control, -j or --job-control CONTROL_TYPE\n       Sets the job control type to CONTROL_TYPE, which can be none,\n       full, or interactive.\n\nfeatures\n       Lists all available feature flags.\n\ntest-feature FEATURE\n       Returns 0 when FEATURE is enabled, 1 if it is disabled, and 2\n       if it is not recognized.\n\nNOTES\nFor backwards compatibility most subcommands can also be specified as\na long or short option. For example, rather than status is-login you\ncan type status --is-login. The flag forms are deprecated and may be\nremoved in a future release (but not before fish 4.0).\n\nYou can only specify one subcommand per invocation even if you use\nthe flag form of the subcommand.",
        "args": "status\nstatus is-login\nstatus is-interactive\nstatus is-block\nstatus is-breakpoint\nstatus is-command-substitution\nstatus is-no-job-control\nstatus is-full-job-control\nstatus is-interactive-job-control\nstatus current-command\nstatus current-commandline\nstatus filename\nstatus basename\nstatus dirname\nstatus fish-path\nstatus function\nstatus line-number\nstatus stack-trace\nstatus job-control CONTROL_TYPE\nstatus features\nstatus test-feature FEATURE"
    },
    "string": {
        "shortDescription": "manipulate strings",
        "description": "string performs operations on strings.\n\nSTRING arguments are taken from the command line unless standard\ninput is connected to a pipe or a file, in which case they are read\nfrom standard input, one STRING per line. It is an error to supply\nSTRING arguments on the command line and on standard input.\n\nArguments beginning with - are normally interpreted as switches; --\ncauses the following arguments not to be treated as switches even if\nthey begin with -. Switches and required arguments are recognized\nonly on the command line.\n\nMost subcommands accept a -q or --quiet switch, which suppresses the\nusual output but exits with the documented status. In this case these\ncommands will quit early, without reading all of the available input.\n\nThe following subcommands are available.\n\nCOLLECT SUBCOMMAND\nstring collect [-a | --allow-empty] [-N | --no-trim-newlines] [STRING ...]\n\nstring collect collects its input into a single output argument,\nwithout splitting the output when used in a command substitution.\nThis is useful when trying to collect multiline output from another\ncommand into a variable. Exit status: 0 if any output argument is\nnon-empty, or 1 otherwise.\n\nA command like echo (cmd | string collect) is mostly equivalent to a\nquoted command substitution (echo \"$(cmd)\"). The main difference is\nthat the former evaluates to zero or one elements whereas the quoted\ncommand substitution always evaluates to one element due to string\ninterpolation.\n\nIf invoked with multiple arguments instead of input, string collect\npreserves each argument separately, where the number of output\narguments is equal to the number of arguments given to string\ncollect.\n\nAny trailing newlines on the input are trimmed, just as with \"$(cmd)\"\nsubstitution. Use --no-trim-newlines to disable this behavior, which\nmay be useful when running a command such as set contents (cat\nfilename | string collect -N).\n\nWith --allow-empty, string collect always prints one (empty)\nargument. This can be used to prevent an argument from disappearing.\n\n   Examples\n\n   > echo \"zero $(echo one\\ntwo\\nthree) four\"\n   zero one\n   two\n   three four\n\n   > echo \\\"(echo one\\ntwo\\nthree | string collect)\\\"\n   \"one\n   two\n   three\"\n\n   > echo \\\"(echo one\\ntwo\\nthree | string collect -N)\\\"\n   \"one\n   two\n   three\n   \"\n\n   > echo foo(true | string collect --allow-empty)bar\n   foobar\n\nESCAPE AND UNESCAPE SUBCOMMANDS\nstring escape [-n | --no-quoted] [--style=] [STRING ...]\nstring unescape [--style=] [STRING ...]\n\nstring escape escapes each STRING in one of three ways. The first is\n--style=script. This is the default. It alters the string such that\nit can be passed back to eval to produce the original argument again.\nBy default, all special characters are escaped, and quotes are used\nto simplify the output when possible. If -n or --no-quoted is given,\nthe simplifying quoted format is not used. Exit status: 0 if at least\none string was escaped, or 1 otherwise.\n\n--style=var ensures the string can be used as a variable name by hex\nencoding any non-alphanumeric characters. The string is first\nconverted to UTF-8 before being encoded.\n\n--style=url ensures the string can be used as a URL by hex encoding\nany character which is not legal in a URL. The string is first\nconverted to UTF-8 before being encoded.\n\n--style=regex escapes an input string for literal matching within a\nregex expression. The string is first converted to UTF-8 before being\nencoded.\n\nstring unescape performs the inverse of the string escape command. If\nthe string to be unescaped is not properly formatted it is ignored.\nFor example, doing string unescape --style=var (string escape\n--style=var $str) will return the original string. There is no\nsupport for unescaping --style=regex.\n\n   Examples\n\n   > echo \\x07 | string escape\n   \\cg\n\n   > string escape --style=var 'a1 b2'\\u6161\n   a1_20_b2_E6_85_A1\n\nJOIN AND JOIN0 SUBCOMMANDS\nstring join [-q | --quiet] SEP [STRING ...]\nstring join0 [-q | --quiet] [STRING ...]\n\nstring join joins its STRING arguments into a single string separated\nby SEP, which can be an empty string. Exit status: 0 if at least one\njoin was performed, or 1 otherwise. If -n or --no-empty is specified,\nempty strings are excluded from consideration (e.g. string join -n +\na b \"\" c would expand to a+b+c not a+b++c).\n\nstring join0 joins its STRING arguments into a single string\nseparated by the zero byte (NUL), and adds a trailing NUL. This is\nmost useful in conjunction with tools that accept NUL-delimited\ninput, such as sort -z. Exit status: 0 if at least one join was\nperformed, or 1 otherwise.\n\nBecause Unix uses NUL as the string terminator, passing the output of\nstring join0 as an argument to a command (via a command substitution)\nwon't actually work. Fish will pass the correct bytes along, but the\ncommand won't be able to tell where the argument ends. This is a\nlimitation of Unix' argument passing.\n\n   Examples\n\n   > seq 3 | string join ...\n   1...2...3\n\n   # Give a list of NUL-separated filenames to du (this is a GNU extension)\n   > string join0 file1 file2 file\\nwith\\nmultiple\\nlines | du --files0-from=-\n\n   # Just put the strings together without a separator\n   > string join '' a b c\n   abc\n\nLENGTH SUBCOMMAND\nstring length [-q | --quiet] [-V | --visible] [STRING ...]\n\nstring length reports the length of each string argument in\ncharacters. Exit status: 0 if at least one non-empty STRING was\ngiven, or 1 otherwise.\n\nWith -V or --visible, it uses the visible width of the arguments.\nThat means it will discount escape sequences fish knows about,\naccount for $fish_emoji_width and $fish_ambiguous_width. It will also\ncount each line (separated by \\n) on its own, and with a carriage\nreturn (\\r) count only the widest stretch on a line. The intent is to\nmeasure the number of columns the STRING would occupy in the current\nterminal.\n\n   Examples\n\n   > string length 'hello, world'\n   12\n\n   > set str foo\n   > string length -q $str; echo $status\n   0\n   # Equivalent to test -n \"$str\"\n\n   > string length --visible (set_color red)foobar\n   # the set_color is discounted, so this is the width of \"foobar\"\n   6\n\n   > string length --visible 🐟🐟🐟🐟\n   # depending on $fish_emoji_width, this is either 4 or 8\n   # in new terminals it should be\n   8\n\n   > string length --visible abcdef\\r123\n   # this displays as \"123def\", so the width is 6\n   6\n\n   > string length --visible a\\nbc\n   # counts \"a\" and \"bc\" as separate lines, so it prints width for each\n   1\n   2\n\nLOWER SUBCOMMAND\nstring lower [-q | --quiet] [STRING ...]\n\nstring lower converts each string argument to lowercase. Exit status:\n0 if at least one string was converted to lowercase, else 1. This\nmeans that in conjunction with the -q flag you can readily test\nwhether a string is already lowercase.\n\nMATCH SUBCOMMAND\nstring match [-a | --all] [-e | --entire] [-i | --ignore-case]\n             [-g | --groups-only] [-r | --regex] [-n | --index]\n             [-q | --quiet] [-v | --invert]\n             PATTERN [STRING ...]\n\nstring match tests each STRING against PATTERN and prints matching\nsubstrings. Only the first match for each STRING is reported unless\n-a or --all is given, in which case all matches are reported.\n\nIf you specify the -e or --entire then each matching string is\nprinted including any prefix or suffix not matched by the pattern\n(equivalent to grep without the -o flag). You can, obviously, achieve\nthe same result by prepending and appending * or .* depending on\nwhether or not you have specified the --regex flag. The --entire flag\nis simply a way to avoid having to complicate the pattern in that\nfashion and make the intent of the string match clearer. Without\n--entire and --regex, a PATTERN will need to match the entire STRING\nbefore it will be reported.\n\nMatching can be made case-insensitive with --ignore-case or -i.\n\nIf --groups-only or -g is given, only the capturing groups will be\nreported - meaning the full match will be skipped. This is\nincompatible with --entire and --invert, and requires --regex. It is\nuseful as a simple cutting tool instead of string replace, so you can\nsimply choose \"this part\" of a string.\n\nIf --index or -n is given, each match is reported as a 1-based start\nposition and a length. By default, PATTERN is interpreted as a glob\npattern matched against each entire STRING argument. A glob pattern\nis only considered a valid match if it matches the entire STRING.\n\nIf --regex or -r is given, PATTERN is interpreted as a\nPerl-compatible regular expression, which does not have to match the\nentire STRING. For a regular expression containing capturing groups,\nmultiple items will be reported for each match, one for the entire\nmatch and one for each capturing group. With this, only the matching\npart of the STRING will be reported, unless --entire is given.\n\nWhen matching via regular expressions, string match automatically\nsets variables for all named capturing groups ((?<name>expression)).\nIt will create a variable with the name of the group, in the default\nscope, for each named capturing group, and set it to the value of the\ncapturing group in the first matched argument. If a named capture\ngroup matched an empty string, the variable will be set to the empty\nstring (like set var \"\"). If it did not match, the variable will be\nset to nothing (like set var).  When --regex is used with --all, this\nbehavior changes. Each named variable will contain a list of matches,\nwith the first match contained in the first element, the second match\nin the second, and so on. If the group was empty or did not match,\nthe corresponding element will be an empty string.\n\nIf --invert or -v is used the selected lines will be only those which\ndo not match the given glob pattern or regular expression.\n\nExit status: 0 if at least one match was found, or 1 otherwise.\n\n   Match Glob Examples\n\n   > string match '?' a\n   a\n\n   > string match 'a*b' axxb\n   axxb\n\n   > string match -i 'a??B' Axxb\n   Axxb\n\n   > string match -- '-*' -h foo --version bar\n   # To match things that look like options, we need a `--`\n   # to tell string its options end there.\n   -h\n   --version\n\n   > echo 'ok?' | string match '*\\?'\n   ok?\n\n   # Note that only the second STRING will match here.\n   > string match 'foo' 'foo1' 'foo' 'foo2'\n   foo\n\n   > string match -e 'foo' 'foo1' 'foo' 'foo2'\n   foo1\n   foo\n   foo2\n\n   > string match 'foo?' 'foo1' 'foo' 'foo2'\n   foo1\n   foo2\n\n   Match Regex Examples\n\n   > string match -r 'cat|dog|fish' 'nice dog'\n   dog\n\n   > string match -r -v \"c.*[12]\" {cat,dog}(seq 1 4)\n   dog1\n   dog2\n   cat3\n   dog3\n   cat4\n   dog4\n\n   > string match -r -- '-.*' -h foo --version bar\n   # To match things that look like options, we need a `--`\n   # to tell string its options end there.\n   -h\n   --version\n\n   > string match -r '(\\d\\d?):(\\d\\d):(\\d\\d)' 2:34:56\n   2:34:56\n   2\n   34\n   56\n\n   > string match -r '^(\\w{2,4})\\1$' papa mud murmur\n   papa\n   pa\n   murmur\n   mur\n\n   > string match -r -a -n at ratatat\n   2 2\n   4 2\n   6 2\n\n   > string match -r -i '0x[0-9a-f]{1,8}' 'int magic = 0xBadC0de;'\n   0xBadC0de\n\n   > echo $version\n   3.1.2-1575-ga2ff32d90\n   > string match -rq '(?<major>\\d+).(?<minor>\\d+).(?<revision>\\d+)' -- $version\n   > echo \"You are using fish $major!\"\n   You are using fish 3!\n\n   > string match -raq ' *(?<sentence>[^.!?]+)(?<punctuation>[.!?])?' \"hello, friend. goodbye\"\n   > printf \"%s\\n\" -- $sentence\n   hello, friend\n   goodbye\n   > printf \"%s\\n\" -- $punctuation\n   .\n\n   > string match -rq '(?<word>hello)' 'hi'\n   > count $word\n   0\n\nPAD AND SHORTEN SUBCOMMANDS\nstring pad [-r | --right] [(-c | --char) CHAR] [(-w | --width) INTEGER]\n           [STRING ...]\n\nstring pad extends each STRING to the given visible width by adding\nCHAR to the left. That means the width of all visible characters\nadded together, excluding escape sequences and accounting for\nfish_emoji_width and fish_ambiguous_width. It is the amount of\ncolumns in a terminal the STRING occupies.\n\nThe escape sequences reflect what fish knows about, and how it\ncomputes its output. Your terminal might support more escapes, or not\nsupport escape sequences that fish knows about.\n\nIf -r or --right is given, add the padding after a string.\n\nIf -c or --char is given, pad with CHAR instead of whitespace.\n\nThe output is padded to the maximum width of all input strings. If -w\nor --width is given, use at least that.\n\n   > string pad -w 10 abc abcdef\n          abc\n       abcdef\n\n   > string pad --right --char=🐟 \"fish are pretty\" \"rich. \"\n   fish are pretty\n   rich.  🐟🐟🐟🐟\n\n   > string pad -w$COLUMNS (date)\n   # Prints the current time on the right edge of the screen.\n\nSEE ALSO\n\n• The printf command can do simple padding, for example printf %10s\\n\n  works like string pad -w10.\n\n• string length with the --visible option can be used to show what\n  fish thinks the width is.\nstring shorten [(-c | --char) CHARS] [(-m | --max) INTEGER]\n               [-N | --no-newline] [-l | --left] [-q | --quiet] [STRING ...]\n\nstring shorten truncates each STRING to the given visible width and\nadds an ellipsis to indicate it. \"Visible width\" means the width of\nall visible characters added together, excluding escape sequences and\naccounting for fish_emoji_width and fish_ambiguous_width. It is the\namount of columns in a terminal the STRING occupies.\n\nThe escape sequences reflect what fish knows about, and how it\ncomputes its output. Your terminal might support more escapes, or not\nsupport escape sequences that fish knows about.\n\nIf -m or --max is given, truncate at the given width. Otherwise, the\nlowest non-zero width of all input strings is used. A max of 0 means\nno shortening takes place, all STRINGs are printed as-is.\n\nIf -N or --no-newline is given, only the first line (or last line\nwith --left) of each STRING is used, and an ellipsis is added if it\nwas multiline. This only works for STRINGs being given as arguments,\nmultiple lines given on stdin will be interpreted as separate STRINGs\ninstead.\n\nIf -c or --char is given, add CHAR instead of an ellipsis. This can\nalso be empty or more than one character.\n\nIf -l or --left is given, remove text from the left on instead, so\nthis prints the longest suffix of the string that fits. With\n--no-newline, this will take from the last line instead of the first.\n\nIf -q or --quiet is given, string shorten only runs for the return\nvalue - if anything would be shortened, it returns 0, else 1.\n\nThe default ellipsis is …. If fish thinks your system is incapable\nbecause of your locale, it will use ... instead.\n\nThe return value is 0 if any shortening occured, 1 otherwise.\n\n   > string shorten foo foobar\n   # No width was given, we infer, and \"foo\" is the shortest.\n   foo\n   fo…\n\n   > string shorten --char=\"...\" foo foobar\n   # The target width is 3 because of \"foo\",\n   # and our ellipsis is 3 too, so we can't really show anything.\n   # This is the default ellipsis if your locale doesn't allow \"…\".\n   foo\n   ...\n\n   > string shorten --char=\"\" --max 4 abcdef 123456\n   # Leaving the char empty makes us not add an ellipsis\n   # So this truncates at 4 columns:\n   abcd\n   1234\n\n   > touch \"a multiline\"\\n\"file\"\n   > for file in *; string shorten -N -- $file; end\n   # Shorten the multiline file so we only show one line per file:\n   a multiline…\n\n   > ss -p | string shorten -m$COLUMNS -c \"\"\n   # `ss` from Linux' iproute2 shows socket information, but prints extremely long lines.\n   # This shortens input so it fits on the screen without overflowing lines.\n\n   > git branch | string match -rg '^\\* (.*)' | string shorten -m20\n   # Take the current git branch and shorten it at 20 columns.\n   # Here the branch is \"builtin-path-with-expand\"\n   builtin-path-with-e…\n\n   > git branch | string match -rg '^\\* (.*)' | string shorten -m20 --left\n   # Taking 20 columns from the right instead:\n   …in-path-with-expand\n\nSEE ALSO\n\n• string's pad subcommand does the inverse of this command, adding\n  padding to a specific width instead.\n\n• The printf command can do simple padding, for example printf %10s\\n\n  works like string pad -w10.\n\n• string length with the --visible option can be used to show what\n  fish thinks the width is.\n\nREPEAT SUBCOMMAND\nstring repeat [(-n | --count) COUNT] [(-m | --max) MAX] [-N | --no-newline]\n              [-q | --quiet] [STRING ...]\n\nstring repeat repeats the STRING -n or --count times. The -m or --max\noption will limit the number of outputted characters (excluding the\nnewline). This option can be used by itself or in conjunction with\n--count. If both --count and --max are present, max char will be\noutputed unless the final repeated string size is less than max, in\nthat case, the string will repeat until count has been reached. Both\n--count and --max will accept a number greater than or equal to zero,\nin the case of zero, nothing will be outputed. If -N or --no-newline\nis given, the output won't contain a newline character at the end.\nExit status: 0 if yielded string is not empty, 1 otherwise.\n\n   Examples\n   Repeat Examples\n\n   > string repeat -n 2 'foo '\n   foo foo\n\n   > echo foo | string repeat -n 2\n   foofoo\n\n   > string repeat -n 2 -m 5 'foo'\n   foofo\n\n   > string repeat -m 5 'foo'\n   foofo\n\nREPLACE SUBCOMMAND\nstring replace [-a | --all] [-f | --filter] [-i | --ignore-case]\n               [-r | --regex] [-q | --quiet] PATTERN REPLACEMENT [STRING ...]\n\nstring replace is similar to string match but replaces\nnon-overlapping matching substrings with a replacement string and\nprints the result. By default, PATTERN is treated as a literal\nsubstring to be matched.\n\nIf -r or --regex is given, PATTERN is interpreted as a\nPerl-compatible regular expression, and REPLACEMENT can contain\nC-style escape sequences like t as well as references to capturing\ngroups by number or name as $n or ${n}.\n\nIf you specify the -f or --filter flag then each input string is\nprinted only if a replacement was done. This is useful where you\nwould otherwise use this idiom: a_cmd | string match pattern | string\nreplace pattern new_pattern. You can instead just write a_cmd |\nstring replace --filter pattern new_pattern.\n\nExit status: 0 if at least one replacement was performed, or 1\notherwise.\n\n   Replace Literal Examples\n\n   > string replace is was 'blue is my favorite'\n   blue was my favorite\n\n   > string replace 3rd last 1st 2nd 3rd\n   1st\n   2nd\n   last\n\n   > string replace -a ' '  'spaces to underscores'\n   spaces_to_underscores\n\n   Replace Regex Examples\n\n   > string replace -r -a '[^\\d.]+' ' ' '0 one two 3.14 four 5x'\n   0 3.14 5\n\n   > string replace -r '(\\w+)\\s+(\\w+)' '$2 $1 $$' 'left right'\n   right left $\n\n   > string replace -r '\\s*newline\\s*' '\\n' 'put a newline here'\n   put a\n   here\n\nSPLIT AND SPLIT0 SUBCOMMANDS\nstring split [(-f | --fields) FIELDS] [(-m | --max) MAX] [-n | --no-empty]\n             [-q | --quiet] [-r | --right] SEP [STRING ...]\nstring split0 [(-f | --fields) FIELDS] [(-m | --max) MAX] [-n | --no-empty]\n              [-q | --quiet] [-r | --right] [STRING ...]\n\nstring split splits each STRING on the separator SEP, which can be an\nempty string. If -m or --max is specified, at most MAX splits are\ndone on each STRING. If -r or --right is given, splitting is\nperformed right-to-left. This is useful in combination with -m or\n--max. With -n or --no-empty, empty results are excluded from\nconsideration (e.g. hello\\n\\nworld would expand to two strings and\nnot three). Exit status: 0 if at least one split was performed, or 1\notherwise.\n\nUse -f or --fields to print out specific fields. FIELDS is a\ncomma-separated string of field numbers and/or spans. Each field is\none-indexed, and will be printed on separate lines. If a given field\ndoes not exist, then the command exits with status 1 and does not\nprint anything, unless --allow-empty is used.\n\nSee also the --delimiter option of the read command.\n\nstring split0 splits each STRING on the zero byte (NUL). Options are\nthe same as string split except that no separator is given.\n\nsplit0 has the important property that its output is not further\nsplit when used in a command substitution, allowing for the command\nsubstitution to produce elements containing newlines. This is most\nuseful when used with Unix tools that produce zero bytes, such as\nfind -print0 or sort -z. See split0 examples below.\n\n   Examples\n\n   > string split . example.com\n   example\n   com\n\n   > string split -r -m1 / /usr/local/bin/fish\n   /usr/local/bin\n   fish\n\n   > string split '' abc\n   a\n   b\n   c\n\n   > string split --allow-empty -f1,3-4,5 '' abcd\n   a\n   c\n   d\n\n   NUL Delimited Examples\n\n   > # Count files in a directory, without being confused by newlines.\n   > count (find . -print0 | string split0)\n   42\n\n   > # Sort a list of elements which may contain newlines\n   > set foo beta alpha\\ngamma\n   > set foo (string join0 $foo | sort -z | string split0)\n   > string escape $foo[1]\n   alpha\\ngamma\n\nSUB SUBCOMMAND\nstring sub [(-s | --start) START] [(-e | --end) END] [(-l | --length) LENGTH]\n           [-q | --quiet] [STRING ...]\n\nstring sub prints a substring of each string argument. The start/end\nof the substring can be specified with -s/-e or --start/--end\nfollowed by a 1-based index value. Positive index values are relative\nto the start of the string and negative index values are relative to\nthe end of the string. The default start value is 1. The length of\nthe substring can be specified with -l or --length. If the length or\nend is not specified, the substring continues to the end of each\nSTRING. Exit status: 0 if at least one substring operation was\nperformed, 1 otherwise. --length is mutually exclusive with --end.\n\n   Examples\n\n   > string sub --length 2 abcde\n   ab\n\n   > string sub -s 2 -l 2 abcde\n   bc\n\n   > string sub --start=-2 abcde\n   de\n\n   > string sub --end=3 abcde\n   abc\n\n   > string sub -e -1 abcde\n   abcd\n\n   > string sub -s 2 -e -1 abcde\n   bcd\n\n   > string sub -s -3 -e -2 abcde\n   c\n\nTRIM SUBCOMMAND\nstring trim [-l | --left] [-r | --right] [(-c | --chars) CHARS]\n            [-q | --quiet] [STRING ...]\n\nstring trim removes leading and trailing whitespace from each STRING.\nIf -l or --left is given, only leading whitespace is removed. If -r\nor --right is given, only trailing whitespace is trimmed. The -c or\n--chars switch causes the characters in CHARS to be removed instead\nof whitespace. Exit status: 0 if at least one character was trimmed,\nor 1 otherwise.\n\n   Examples\n\n   > string trim ' abc  '\n   abc\n\n   > string trim --right --chars=yz xyzzy zany\n   x\n   zan\n\nUPPER SUBCOMMAND\nstring upper [-q | --quiet] [STRING ...]\n\nstring upper converts each string argument to uppercase. Exit status:\n0 if at least one string was converted to uppercase, else 1. This\nmeans that in conjunction with the -q flag you can readily test\nwhether a string is already uppercase.\n\nREGULAR EXPRESSIONS\nBoth the match and replace subcommand support regular expressions\nwhen used with the -r or --regex option. The dialect is that of\nPCRE2.\n\nIn general, special characters are special by default, so a+ matches\none or more \"a\"s, while a\\+ matches an \"a\" and then a \"+\". (a+)\nmatches one or more \"a\"s in a capturing group ((?:XXXX) denotes a\nnon-capturing group). For the replacement parameter of replace, $n\nrefers to the n-th group of the match. In the match parameter, \\n\n(e.g. \\1) refers back to groups.\n\nSome features include repetitions:\n\n• * refers to 0 or more repetitions of the previous expression\n\n• + 1 or more\n\n• ? 0 or 1.\n\n• {n} to exactly n (where n is a number)\n\n• {n,m} at least n, no more than m.\n\n• {n,} n or more\n\nCharacter classes, some of the more important:\n\n• . any character except newline\n\n• \\d a decimal digit and \\D, not a decimal digit\n\n• \\s whitespace and \\S, not whitespace\n\n• \\w a \"word\" character and \\W, a \"non-word\" character\n\n• [...] (where \"...\" is some characters) is a character set\n\n• [^...] is the inverse of the given character set\n\n• [x-y] is the range of characters from x-y\n\n• [[:xxx:]] is a named character set\n\n• [[:^xxx:]] is the inverse of a named character set\n\n• [[:alnum:]]  : \"alphanumeric\"\n\n• [[:alpha:]]  : \"alphabetic\"\n\n• [[:ascii:]]  : \"0-127\"\n\n• [[:blank:]]  : \"space or tab\"\n\n• [[:cntrl:]]  : \"control character\"\n\n• [[:digit:]]  : \"decimal digit\"\n\n• [[:graph:]]  : \"printing, excluding space\"\n\n• [[:lower:]]  : \"lower case letter\"\n\n• [[:print:]]  : \"printing, including space\"\n\n• [[:punct:]]  : \"printing, excluding alphanumeric\"\n\n• [[:space:]]  : \"white space\"\n\n• [[:upper:]]  : \"upper case letter\"\n\n• [[:word:]]   : \"same as w\"\n\n• [[:xdigit:]] : \"hexadecimal digit\"\n\nGroups:\n\n• (...) is a capturing group\n\n• (?:...) is a non-capturing group\n\n• \\n is a backreference (where n is the number of the group, starting\n  with 1)\n\n• $n is a reference from the replacement expression to a group in the\n  match expression.\n\nAnd some other things:\n\n• \\b denotes a word boundary, \\B is not a word boundary.\n\n• ^ is the start of the string or line, $ the end.\n\n• | is \"alternation\", i.e. the \"or\".\n\nCOMPARISON TO OTHER TOOLS\nMost operations string supports can also be done by external tools.\nSome of these include grep, sed and cut.\n\nIf you are familiar with these, it is useful to know how string\ndiffers from them.\n\nIn contrast to these classics, string reads input either from stdin\nor as arguments. string also does not deal with files, so it requires\nredirections to be used with them.\n\nIn contrast to grep, string's match defaults to glob-mode, while\nreplace defaults to literal matching. If set to regex-mode, they use\nPCRE regular expressions, which is comparable to grep's -P option.\nmatch defaults to printing just the match, which is like grep with -o\n(use --entire to enable grep-like behavior).\n\nLike sed's s/old/new/ command, string replace still prints strings\nthat don't match. sed's -n in combination with a /p modifier or\ncommand is like string replace -f.\n\nstring split somedelimiter is a replacement for tr somedelimiter \\n.",
        "args": "string collect [-a | --allow-empty] [-N | --no-trim-newlines] [STRING ...]\nstring escape [-n | --no-quoted] [--style=] [STRING ...]\nstring join [-q | --quiet] [-n | --no-empty] SEP [STRING ...]\nstring join0 [-q | --quiet] [STRING ...]\nstring length [-q | --quiet] [STRING ...]\nstring lower [-q | --quiet] [STRING ...]\nstring match [-a | --all] [-e | --entire] [-i | --ignore-case]\n             [-g | --groups-only] [-r | --regex] [-n | --index]\n             [-q | --quiet] [-v | --invert]\n             PATTERN [STRING ...]\nstring pad [-r | --right] [(-c | --char) CHAR] [(-w | --width) INTEGER]\n           [STRING ...]\nstring repeat [(-n | --count) COUNT] [(-m | --max) MAX] [-N | --no-newline]\n              [-q | --quiet] [STRING ...]\nstring replace [-a | --all] [-f | --filter] [-i | --ignore-case]\n               [-r | --regex] [-q | --quiet] PATTERN REPLACE [STRING ...]\nstring shorten [(-c | --char) CHARS] [(-m | --max) INTEGER]\n               [-N | --no-newline] [-l | --left] [-q | --quiet] [STRING ...]\nstring split [(-f | --fields) FIELDS] [(-m | --max) MAX] [-n | --no-empty]\n             [-q | --quiet] [-r | --right] SEP [STRING ...]\nstring split0 [(-f | --fields) FIELDS] [(-m | --max) MAX] [-n | --no-empty]\n              [-q | --quiet] [-r | --right] [STRING ...]\nstring sub [(-s | --start) START] [(-e | --end) END] [(-l | --length) LENGTH]\n           [-q | --quiet] [STRING ...]\nstring trim [-l | --left] [-r | --right] [(-c | --chars) CHARS]\n            [-q | --quiet] [STRING ...]\nstring unescape [--style=] [STRING ...]\nstring upper [-q | --quiet] [STRING ...]"
    },
    "switch": {
        "shortDescription": "conditionally execute a block of commands",
        "description": "switch performs one of several blocks of commands, depending on\nwhether a specified value equals one of several globbed values. case\nis used together with the switch statement in order to determine\nwhich block should be executed.\n\nEach case command is given one or more parameters. The first case\ncommand with a parameter that matches the string specified in the\nswitch command will be evaluated. case parameters may contain globs.\nThese need to be escaped or quoted in order to avoid regular glob\nexpansion using filenames.\n\nNote that fish does not fall through on case statements. Only the\nfirst matching case is executed.\n\nNote that break cannot be used to exit a case/switch block early like\nin other languages. It can only be used in loops.\n\nNote that command substitutions in a case statement will be evaluated\neven if its body is not taken. All substitutions, including command\nsubstitutions, must be performed before the value can be compared\nagainst the parameter.\n\nEXAMPLE\nIf the variable $animal contains the name of an animal, the following\ncode would attempt to classify it:\n\n   switch $animal\n       case cat\n           echo evil\n       case wolf dog human moose dolphin whale\n           echo mammal\n       case duck goose albatross\n           echo bird\n       case shark trout stingray\n           echo fish\n       case '*'\n           echo I have no idea what a $animal is\n   end\n\nIf the above code was run with $animal set to whale, the output would\nbe mammal.",
        "args": "switch VALUE; [case [GLOB ...]; [COMMANDS ...]; ...] end"
    },
    "test": {
        "shortDescription": "Evaluate conditional expressions",
        "description": "The `test` command evaluates conditional expressions and sets the exit status to 0 if the expression is true, and 1 if it is false. It supports various operators to evaluate expressions related to strings, numbers, and file attributes.",
        "args": "EXPRESSION"
    },
    "time": {
        "shortDescription": "measure how long a command or block takes",
        "description": "NOTE: This page documents the fish keyword time.  To see the\ndocumentation on the time command you might have, use command man\ntime.\n\ntime causes fish to measure how long a command takes and print the\nresults afterwards. The command can be a simple fish command or a\nblock. The results can not currently be redirected.\n\nFor checking timing after a command has completed, check\n$CMD_DURATION.\n\nYour system most likely also has a time command. To use that use\nsomething like command time, as in command time sleep 10. Because\nit's not inside fish, it won't have access to fish functions and\nwon't be able to time blocks and such.\n\nHOW TO INTERPRET THE OUTPUT\nTime outputs a few different values. Let's look at an example:\n\n   > time string repeat -n 10000000 y\\n | command grep y >/dev/null\n   _______________________________________________________\n   Executed in  805.98 millis    fish           external\n      usr time  798.88 millis  763.88 millis   34.99 millis\n      sys time  141.22 millis   40.20 millis  101.02 millis\n\nThe time after \"Executed in\" is what is known as the \"wall-clock\ntime\". It is simply a measure of how long it took from the start of\nthe command until it finished. Typically it is reasonably close to\nCMD_DURATION, except for a slight skew because the two are taken at\nslightly different times.\n\nThe other times are all measures of CPU time. That means they measure\nhow long the CPU was used in this part, and they count multiple cores\nseparately. So a program with four threads using all CPU for a second\nwill have a time of 4 seconds.\n\nThe \"usr\" time is how much CPU time was spent inside the program\nitself, the \"sys\" time is how long was spent in the kernel on behalf\nof that program.\n\nThe \"fish\" time is how much CPU was spent in fish, the \"external\"\ntime how much was spent in external commands.\n\nSo in this example, since string is a builtin, everything that string\nrepeat did is accounted to fish. Any time it spends doing syscalls\nlike write() is accounted for in the fish/sys time.\n\nAnd grep here is explicitly invoked as an external command, so its\ntimes will be counted in the \"external\" column.\n\nNote that, as in this example, the CPU times can add up to more than\nthe execution time. This is because things can be done in parallel -\ngrep can match while string repeat writes.\n\nEXAMPLE\n(for obvious reasons exact results will vary on your system)\n\n   > time sleep 1s\n\n   _______________________________________________________\n   Executed in    1,01 secs   fish           external\n      usr time    2,32 millis    0,00 micros    2,32 millis\n      sys time    0,88 millis  877,00 micros    0,00 millis\n\n   > time for i in 1 2 3; sleep 1s; end\n\n   _______________________________________________________\n   Executed in    3,01 secs   fish           external\n      usr time    9,16 millis    2,94 millis    6,23 millis\n      sys time    0,23 millis    0,00 millis    0,23 millis\n\nInline variable assignments need to follow the time keyword:\n\n   > time a_moment=1.5m sleep $a_moment\n\n   _______________________________________________________\n   Executed in   90.00 secs      fish           external\n      usr time    4.62 millis    4.62 millis    0.00 millis\n      sys time    2.35 millis    0.41 millis    1.95 millis",
        "args": "time COMMAND"
    },
    "true": {
        "shortDescription": "Return a successful result",
        "description": "The `true` command always returns a successful (zero) exit status. It is often used in scripts and conditional statements where an unconditional success result is needed."
    },
    "type": {
        "shortDescription": "locate a command and describe its type",
        "description": "With no options, type indicates how each NAME would be interpreted if\nused as a command name.\n\nThe following options are available:\n\n-a or --all\n       Prints all of possible definitions of the specified names.\n\n-s or --short\n       Suppresses function expansion when used with no options or\n       with -a/--all.\n\n-f or --no-functions\n       Suppresses function and builtin lookup.\n\n-t or --type\n       Prints function, builtin, or file if NAME is a shell function,\n       builtin, or disk file, respectively.\n\n-p or --path\n       Prints the path to NAME if NAME resolves to an executable file\n       in PATH, the path to the script containing the definition of\n       the function NAME if NAME resolves to a function loaded from a\n       file on disk (i.e. not interactively defined at the prompt),\n       or nothing otherwise.\n\n-P or --force-path\n       Returns the path to the executable file NAME, presuming NAME\n       is found in the PATH environment variable, or nothing\n       otherwise. --force-path explicitly resolves only the path to\n       executable files in  PATH, regardless of whether NAME is\n       shadowed by a function or builtin with the same name.\n\n-q or --query\n       Suppresses all output; this is useful when testing the exit\n       status. For compatibility with old fish versions this is also\n       --quiet.\n\n-h or --help\n       Displays help about using this command.\n\nThe -q, -p, -t and -P flags (and their long flag aliases) are\nmutually exclusive. Only one can be specified at a time.\n\ntype returns 0 if at least one entry was found, 1 otherwise, and 2\nfor invalid options or option combinations.\n\nEXAMPLE\n\n   > type fg\n   fg is a builtin",
        "args": "type [OPTIONS] NAME [...]"
    },
    "ulimit": {
        "shortDescription": "set or get resource usage limits",
        "description": "ulimit sets or outputs the resource usage limits of the shell and any\nprocesses spawned by it. If a new limit value is omitted, the current\nvalue of the limit of the resource is printed; otherwise, the\nspecified limit is set to the new value.\n\nUse one of the following switches to specify which resource limit to\nset or report:\n\n-b or --socket-buffers\n       The maximum size of socket buffers.\n\n-c or --core-size\n       The maximum size of core files created. By setting this limit\n       to zero, core dumps can be disabled.\n\n-d or --data-size\n       The maximum size of a process' data segment.\n\n-e or --nice\n       Controls the maximum nice value; on Linux, this value is\n       subtracted from 20 to give the effective value.\n\n-f or --file-size\n       The maximum size of files created by a process.\n\n-i or --pending-signals\n       The maximum number of signals that may be queued.\n\n-l or --lock-size\n       The maximum size that may be locked into memory.\n\n-m or --resident-set-size\n       The maximum resident set size.\n\n-n or --file-descriptor-count\n       The maximum number of open file descriptors.\n\n-q or --queue-size\n       The maximum size of data in POSIX message queues.\n\n-r or --realtime-priority\n       The maximum realtime scheduling priority.\n\n-s or --stack-size\n       The maximum stack size.\n\n-t or --cpu-time\n       The maximum amount of CPU time in seconds.\n\n-u or --process-count\n       The maximum number of processes available to the current user.\n\n-w or --swap-size\n       The maximum swap space available to the current user.\n\n-v or --virtual-memory-size\n       The maximum amount of virtual memory available to the shell.\n\n-y or --realtime-maxtime\n       The maximum contiguous realtime CPU time in microseconds.\n\n-K or --kernel-queues\n       The maximum number of kqueues (kernel queues) for the current\n       user.\n\n-P or --ptys\n       The maximum number of pseudo-terminals for the current user.\n\n-T or --threads\n       The maximum number of simultaneous threads for the current\n       user.\n\nNote that not all these limits are available in all operating\nsystems; consult the documentation for setrlimit in your operating\nsystem.\n\nThe value of limit can be a number in the unit specified for the\nresource or one of the special values hard, soft, or unlimited, which\nstand for the current hard limit, the current soft limit, and no\nlimit, respectively.\n\nIf limit is given, it is the new value of the specified resource. If\nno option is given, then -f is assumed. Values are in kilobytes,\nexcept for -t, which is in seconds and -n and -u, which are unscaled\nvalues. The exit status is 0 unless an invalid option or argument is\nsupplied, or an error occurs while setting a new limit.\n\nulimit also accepts the following options that determine what type of\nlimit to set:\n\n-H or --hard\n       Sets hard resource limit.\n\n-S or --soft\n       Sets soft resource limit.\n\nA hard limit can only be decreased. Once it is set it cannot be\nincreased; a soft limit may be increased up to the value of the hard\nlimit. If neither -H nor -S is specified, both the soft and hard\nlimits are updated when assigning a new limit value, and the soft\nlimit is used when reporting the current value.\n\nThe following additional options are also understood by ulimit:\n\n-a or --all\n       Prints all current limits.\n\n-h or --help\n       Displays help about using this command.\n\nThe fish implementation of ulimit should behave identically to the\nimplementation in bash, except for these differences:\n\n• Fish ulimit supports GNU-style long options for all switches.\n\n• Fish ulimit does not support the -p option for getting the pipe\n  size. The bash implementation consists of a compile-time check that\n  empirically guesses this number by writing to a pipe and waiting\n  for SIGPIPE. Fish does not do this because this method of\n  determining pipe size is unreliable. Depending on bash version,\n  there may also be further additional limits to set in bash that do\n  not exist in fish.\n\n• Fish ulimit does not support getting or setting multiple limits in\n  one command, except reporting all values using the -a switch.\n\nEXAMPLE\nulimit -Hs 64 sets the hard stack size limit to 64 kB.",
        "args": "ulimit [OPTIONS] [LIMIT]"
    },
    "wait": {
        "shortDescription": "wait for jobs to complete",
        "description": "wait waits for child jobs to complete.\n\nIf a PID is specified, the command waits for the job that the process\nwith that process ID belongs to.\n\nIf a PROCESS_NAME is specified, the command waits for the jobs that\nthe matched processes belong to.\n\nIf neither a pid nor a process name is specified, the command waits\nfor all background jobs.\n\nIf the -n or --any flag is provided, the command returns as soon as\nthe first job completes. If it is not provided, it returns after all\njobs complete.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\n\n   sleep 10 &\n   wait $last_pid\n\nspawns sleep in the background, and then waits until it finishes.\n\n   for i in (seq 1 5); sleep 10 &; end\n   wait\n\nspawns five jobs in the background, and then waits until all of them\nfinishes.\n\n   for i in (seq 1 5); sleep 10 &; end\n   hoge &\n   wait sleep\n\nspawns five jobs and hoge in the background, and then waits until all\nsleeps finish, and doesn't wait for hoge finishing.",
        "args": "wait [-n | --any] [PID | PROCESS_NAME] ..."
    },
    "while": {
        "shortDescription": "perform a set of commands multiple times",
        "description": "while repeatedly executes CONDITION, and if the exit status is 0,\nthen executes COMMANDS.\n\nThe exit status of the while loop is the exit status of the last\niteration of the COMMANDS executed, or 0 if none were executed. (This\nmatches other shells and is POSIX-compatible.)\n\nYou can use and or or for complex conditions. Even more complex\ncontrol can be achieved with while true containing a break.\n\nThe -h or --help option displays help about using this command.\n\nEXAMPLE\n\n   while test -f foo.txt; or test -f bar.txt ; echo file exists; sleep 10; end\n   # outputs 'file exists' at 10 second intervals,\n   # as long as the file foo.txt or bar.txt exists.",
        "args": "while CONDITION; COMMANDS; end"
    }
};
//# sourceMappingURL=fishBuiltinsCache.js.map