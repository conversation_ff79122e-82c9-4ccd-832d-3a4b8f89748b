{"version": 3, "sources": ["modes/htmlFolding.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAKhG,4CAkCC;AArCD,mDAA2G;AAGpG,KAAK,UAAU,gBAAgB,CAAC,aAA4B,EAAE,QAAsB,EAAE,SAA6B,EAAE,kBAA4C;IACvK,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAG,qBAAK,CAAC,MAAM,CAAC,wBAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1F,IAAI,MAAM,GAAmB,EAAE,CAAC;IAChC,IAAI,QAAQ,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,GAAI,MAAM,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC5D,CAAC;IAED,gCAAgC;IAChC,MAAM,aAAa,GAAuC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9E,MAAM,gBAAgB,GAAG,KAAK,EAAE,IAAkB,EAAE,EAAE;QACrD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3B,IAAI,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5B,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACrD,aAAa,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,MAAM,CAAC;YACtC,CAAC;YACD,OAAO,MAAM,CAAC;QACf,CAAC;QACD,OAAO,EAAE,CAAC;IACX,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,aAAa,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAClE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;QACpC,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;QAC5B,IAAI,IAAI,IAAI,IAAI,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;YAC5D,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3G,CAAC;IACF,CAAC;IACD,IAAI,SAAS,IAAI,MAAM,CAAC,MAAM,GAAG,SAAS,EAAE,CAAC;QAC5C,MAAM,GAAG,WAAW,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACzC,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,WAAW,CAAC,MAAsB,EAAE,SAAiB;IAC7D,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;QAC/B,IAAI,IAAI,GAAG,EAAE,CAAC,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;QACvC,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YAChB,IAAI,GAAG,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;QAChC,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC,CAAC,CAAC;IAEH,yDAAyD;IACzD,oEAAoE;IACpE,IAAI,GAAG,GAA6B,SAAS,CAAC;IAC9C,MAAM,QAAQ,GAAmB,EAAE,CAAC;IACpC,MAAM,aAAa,GAAa,EAAE,CAAC;IACnC,MAAM,kBAAkB,GAAa,EAAE,CAAC;IAExC,MAAM,eAAe,GAAG,CAAC,KAAa,EAAE,KAAa,EAAE,EAAE;QACxD,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;QAC7B,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YAChB,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAClE,CAAC;IACF,CAAC,CAAC;IAEF,sCAAsC;IACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACxB,IAAI,CAAC,GAAG,EAAE,CAAC;YACV,GAAG,GAAG,KAAK,CAAC;YACZ,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACP,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,EAAE,CAAC;gBACrC,IAAI,KAAK,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;oBAClC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACnB,GAAG,GAAG,KAAK,CAAC;oBACZ,eAAe,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC;qBAAM,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;oBAC1C,GAAG,CAAC;wBACH,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;oBACtB,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,EAAE;oBAC/C,IAAI,GAAG,EAAE,CAAC;wBACT,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACpB,CAAC;oBACD,GAAG,GAAG,KAAK,CAAC;oBACZ,eAAe,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACrC,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC;IACD,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACpD,MAAM,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC,EAAE,CAAC;YACP,IAAI,CAAC,GAAG,OAAO,GAAG,SAAS,EAAE,CAAC;gBAC7B,QAAQ,GAAG,CAAC,CAAC;gBACb,MAAM;YACP,CAAC;YACD,OAAO,IAAI,CAAC,CAAC;QACd,CAAC;IACF,CAAC;IACD,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC/B,IAAI,KAAK,GAAG,QAAQ,IAAI,CAAC,KAAK,KAAK,QAAQ,IAAI,OAAO,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;gBACvE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC", "file": "htmlFolding.js", "sourceRoot": "../../src/"}