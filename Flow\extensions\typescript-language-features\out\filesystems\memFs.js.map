{"version": 3, "sources": ["filesystems/memFs.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+BAAyC;AACzC,+CAAiC;AAGjC,MAAa,KAAK;IAQjB,YACkB,EAAU,EACV,MAAc;QADd,OAAE,GAAF,EAAE,CAAQ;QACV,WAAM,GAAN,MAAM,CAAQ;QARf,SAAI,GAAG,IAAI,gBAAgB,CAC3C,IAAI,GAAG,EAAE,EACT,CAAC,EACD,CAAC,CACD,CAAC;QAwIF,yBAAyB;QAER,aAAQ,GAAG,IAAI,MAAM,CAAC,YAAY,EAA4B,CAAC;QAEvE,oBAAe,GAA2C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACtE,aAAQ,GAAG,IAAI,GAAwB,CAAC;IAxIrD,CAAC;IAEL,IAAI,CAAC,GAAe;QACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAC7C,CAAC;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAED,aAAa,CAAC,GAAe;QAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,IAAI,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC;QAEjE,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAC7C,CAAC;QACD,IAAI,CAAC,CAAC,KAAK,YAAY,gBAAgB,CAAC,EAAE,CAAC;YAC1C,MAAM,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC;QAClD,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IACpF,CAAC;IAED,QAAQ,CAAC,GAAe;QACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC;QAE5D,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,CAAC,KAAK,YAAY,WAAW,CAAC,EAAE,CAAC;YACrC,MAAM,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,SAAS,CAAC,GAAe,EAAE,OAAmB,EAAE,EAAE,MAAM,EAAE,SAAS,EAA2C;QAC7G,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC;QAE7D,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAEhC,MAAM,QAAQ,GAAG,IAAA,eAAQ,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,WAAW,GAAG,GAAG,CAAC,QAAQ,CAAC;QAEjC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;QAC/B,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,IAAA,eAAQ,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,IAAI,MAAM,EAAE,CAAC;gBACZ,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAChE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACP,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;YAC7C,CAAC;QACF,CAAC;aAAM,CAAC;YACP,IAAI,KAAK,YAAY,gBAAgB,EAAE,CAAC;gBACvC,MAAM,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACf,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;gBACnB,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;gBACrB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACP,MAAM,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;YAClF,CAAC;QACF,CAAC;IACF,CAAC;IAED,MAAM,CAAC,OAAmB,EAAE,OAAmB,EAAE,QAAgC;QAChF,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,GAAe;QACrB,IAAI,CAAC;YACJ,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YAChC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAA,eAAQ,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC;IAED,eAAe,CAAC,GAAe;QAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,IAAI,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC;QAEnE,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;QAC9B,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAA,eAAQ,EAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,gBAAgB,CAAC,IAAI,GAAG,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACjF,CAAC;IAEO,QAAQ,CAAC,GAAe;QAC/B,6CAA6C;QAC7C,6CAA6C;QAC7C,IAAI,IAAI,GAAY,IAAI,CAAC,IAAI,CAAC;QAC9B,KAAK,MAAM,SAAS,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChB,6DAA6D;gBAC7D,oBAAoB;gBACpB,SAAS;YACV,CAAC;YAED,IAAI,CAAC,CAAC,IAAI,YAAY,gBAAgB,CAAC,EAAE,CAAC;gBACzC,4CAA4C;gBAC5C,OAAO;YACR,CAAC;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC1C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACX,aAAa;gBACb,OAAO;YACR,CAAC;YAED,IAAI,GAAG,IAAI,CAAC;QACb,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,SAAS,CAAC,GAAe;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,cAAO,EAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACjE,IAAI,CAAC,GAAG,EAAE,CAAC;YACV,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAC7C,CAAC;QACD,IAAI,CAAC,CAAC,GAAG,YAAY,gBAAgB,CAAC,EAAE,CAAC;YACxC,MAAM,MAAM,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC;QAClD,CAAC;QACD,OAAO,GAAG,CAAC;IACZ,CAAC;IASD,KAAK,CAAC,QAAoB;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC7C,CAAC;QACD,MAAM,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;YACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,OAAO,EAAE,CAAC;gBACb,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACnB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACrC,CAAC;YACF,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC;CACD;AApKD,sBAoKC;AAED,MAAM,WAAW;IAGhB,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,YACQ,IAAgB,EACP,KAAa,EACtB,KAAa;QAFb,SAAI,GAAJ,IAAI,CAAY;QACP,UAAK,GAAL,KAAK,CAAQ;QACtB,UAAK,GAAL,KAAK,CAAQ;QATZ,SAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAUjC,CAAC;CACL;AAED,MAAM,gBAAgB;IAGrB,IAAI,IAAI;QACP,OAAO,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,GAAW,EAAE,KAAc,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACjG,CAAC;IAED,YACiB,QAA8B,EAC9B,KAAa,EACb,KAAa;QAFb,aAAQ,GAAR,QAAQ,CAAsB;QAC9B,UAAK,GAAL,KAAK,CAAQ;QACb,UAAK,GAAL,KAAK,CAAQ;QATrB,SAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;IAUtC,CAAC;CACL", "file": "memFs.js", "sourceRoot": "../../src/"}