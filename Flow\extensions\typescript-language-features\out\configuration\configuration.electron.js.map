{"version": 3, "sources": ["configuration/configuration.electron.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,uCAAyB;AACzB,2CAA6B;AAC7B,+CAAiC;AACjC,6DAA+C;AAC/C,uCAAyB;AACzB,mDAAmE;AACnE,wEAA8E;AAE9E,MAAa,oCAAqC,SAAQ,gDAAgC;IAEjF,eAAe,CAAC,YAAoB;QAC3C,MAAM,YAAY,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,KAAK,MAAM,UAAU,IAAI,YAAY,EAAE,CAAC;YACvC,IAAI,YAAY,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;gBACzC,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;YACvE,CAAC;QACF,CAAC;QACD,OAAO,YAAY,CAAC;IACrB,CAAC;IAES,cAAc,CAAC,aAA4C;QACpE,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACzD,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAES,aAAa,CAAC,aAA4C;QACnE,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;QACzD,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QACrD,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAES,iBAAiB,CAAC,aAA4C;QACvE,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,CAAC;IACvE,CAAC;IAEO,uBAAuB,CAAC,aAA4C;QAC3E,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QACtE,IAAI,OAAO,EAAE,cAAc,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,QAAQ,EAAE,CAAC;YAC3E,IAAI,OAAO,CAAC,cAAc,KAAK,MAAM,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC/D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBACjC,MAAM,aAAa,GAAG,oDAA6B,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;gBACvF,OAAO,aAAa,IAAI,IAAI,CAAC;YAC9B,CAAC;YACD,OAAO,SAAS,CAAC;QAClB,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAES,kBAAkB,CAAC,aAA4C;QACxE,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,aAAa,CAAC,CAAC,CAAC;IACxE,CAAC;IAEO,wBAAwB,CAAC,aAA4C;QAC5E,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;QACtE,IAAI,OAAO,EAAE,WAAW,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YACrE,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE,CAAC;gBACpC,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAChC,OAAO,SAAS,CAAC;YAClB,CAAC;QACF,CAAC;QACD,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,YAAY;QACnB,IAAI,CAAC;YACJ,MAAM,GAAG,GAAG,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,+BAA+B,CAAC,EAAE;gBACvF,WAAW,EAAE,IAAI;gBACjB,OAAO,EAAE,IAAI;gBACb,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM;gBACtD,QAAQ,EAAE,OAAO;aACjB,CAAC,CAAC;YACH,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,wDAAwD,CAAC,CAAC,CAAC;YAC1G,OAAO,IAAI,CAAC;QACb,CAAC;IACF,CAAC;IAEO,YAAY,CAAC,QAAuB;QAC3C,IAAI,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACpF,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,0GAA0G,EAAE,QAAQ,CAAC,CAAC,CAAC;YACtK,OAAO,IAAI,CAAC;QACb,CAAC;QACD,OAAO,QAAQ,CAAC;IACjB,CAAC;CACD;AAxFD,oFAwFC", "file": "configuration.electron.js", "sourceRoot": "../../src/"}