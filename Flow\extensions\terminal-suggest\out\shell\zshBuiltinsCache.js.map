{"version": 3, "sources": ["shell/zshBuiltinsCache.ts"], "names": [], "mappings": ";AACA;;;gGAGgG;;;AAEnF,QAAA,mCAAmC,GAAG;IACjD,GAAG,EAAE;QACH,kBAAkB,EAAE,eAAe;QACnC,aAAa,EAAE,6/BAA6/B;QAC5gC,MAAM,EAAE,oBAAoB;KAC7B;IACD,GAAG,EAAE;QACH,kBAAkB,EAAE,WAAW;QAC/B,aAAa,EAAE,6JAA6J;QAC5K,MAAM,EAAE,eAAe;KACxB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,+yEAA+yE;QAC9zE,MAAM,EAAE,2CAA2C;KACpD;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,qBAAqB;QACzC,aAAa,EAAE,ilIAAilI;QAChmI,MAAM,EAAE,mDAAmD;KAC5D;IACD,IAAI,EAAE;QACJ,kBAAkB,EAAE,6BAA6B;QACjD,aAAa,EAAE,wGAAwG;QACvH,MAAM,EAAE,gBAAgB;KACzB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,yBAAyB;QAC7C,aAAa,EAAE,oDAAoD;QACnE,MAAM,EAAE,SAAS;KAClB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,2LAA2L;QAC1M,MAAM,EAAE,aAAa;KACtB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,oBAAoB;QACxC,aAAa,EAAE,uDAAuD;QACtE,MAAM,EAAE,2BAA2B;KACpC;IACD,KAAK,EAAE;QACL,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,yBAAyB;QACxC,MAAM,EAAE,KAAK;KACd;IACD,KAAK,EAAE;QACL,kBAAkB,EAAE,oCAAoC;QACxD,aAAa,EAAE,8DAA8D;QAC7E,MAAM,EAAE,KAAK;KACd;IACD,IAAI,EAAE;QACJ,kBAAkB,EAAE,8BAA8B;QAClD,aAAa,EAAE,8oFAA8oF;QAC7pF,MAAM,EAAE,8CAA8C;KACvD;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,8BAA8B;QAClD,aAAa,EAAE,uBAAuB;QACtC,MAAM,EAAE,OAAO;KAChB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,mCAAmC;QACvD,aAAa,EAAE,gEAAgE;QAC/E,MAAM,EAAE,OAAO;KAChB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,mBAAmB;QACvC,aAAa,EAAE,wgBAAwgB;QACvhB,MAAM,EAAE,iCAAiC;KAC1C;IACD,eAAe,EAAE;QACf,kBAAkB,EAAE,oBAAoB;QACxC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,eAAe;KACxB;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,eAAe;QACnC,aAAa,EAAE,kEAAkE;QACjF,MAAM,EAAE,UAAU;KACnB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,kEAAkE;QACjF,MAAM,EAAE,SAAS;KAClB;IACD,cAAc,EAAE;QACd,kBAAkB,EAAE,mBAAmB;QACvC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,cAAc;KACvB;IACD,WAAW,EAAE;QACX,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,WAAW;KACpB;IACD,YAAY,EAAE;QACZ,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,YAAY;KACrB;IACD,WAAW,EAAE;QACX,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,WAAW;KACpB;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,eAAe;QACnC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,UAAU;KACnB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,cAAc;QAClC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,SAAS;KAClB;IACD,YAAY,EAAE;QACZ,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,YAAY;KACrB;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,gCAAgC;QACpD,aAAa,EAAE,mOAAmO;QAClP,MAAM,EAAE,gBAAgB;KACzB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,4CAA4C;QAChE,aAAa,EAAE,4BAA4B;QAC3C,MAAM,EAAE,SAAS;KAClB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,+BAA+B;QACnD,aAAa,EAAE,orBAAorB;QACnsB,MAAM,EAAE,yBAAyB;KAClC;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,+oHAA+oH;QAC9pH,MAAM,EAAE,8BAA8B;KACvC;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,2BAA2B;QAC/C,aAAa,EAAE,+mBAA+mB;QAC9nB,MAAM,EAAE,oBAAoB;KAC7B;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,0BAA0B;QAC9C,aAAa,EAAE,wiDAAwiD;QACvjD,MAAM,EAAE,2BAA2B;KACpC;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,4BAA4B;QAChD,aAAa,EAAE,kEAAkE;QACjF,MAAM,EAAE,QAAQ;KACjB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,oBAAoB;QACxC,aAAa,EAAE,mEAAmE;QAClF,MAAM,EAAE,QAAQ;KACjB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,s7LAAs7L;QACr8L,MAAM,EAAE,qDAAqD;KAC9D;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,uBAAuB;QAC3C,aAAa,EAAE,kqCAAkqC;QACjrC,MAAM,EAAE,6BAA6B;KACtC;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,4BAA4B;QAChD,aAAa,EAAE,kUAAkU;QACjV,MAAM,EAAE,kBAAkB;KAC3B;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,4BAA4B;QAChD,aAAa,EAAE,05BAA05B;QACz6B,MAAM,EAAE,mDAAmD;KAC5D;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,4YAA4Y;QAC3Z,MAAM,EAAE,YAAY;KACrB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,uBAAuB;QAC3C,aAAa,EAAE,sOAAsO;QACrP,MAAM,EAAE,6BAA6B;KACtC;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,yBAAyB;QAC7C,aAAa,EAAE,gDAAgD;QAC/D,MAAM,EAAE,mBAAmB;KAC5B;IACD,IAAI,EAAE;QACJ,kBAAkB,EAAE,aAAa;QACjC,aAAa,EAAE,6rLAA6rL;QAC5sL,MAAM,EAAE,OAAO;KAChB;IACD,IAAI,EAAE;QACJ,kBAAkB,EAAE,6BAA6B;QACjD,aAAa,EAAE,qHAAqH;QACpI,MAAM,EAAE,gBAAgB;KACzB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,2BAA2B;QAC/C,aAAa,EAAE,iHAAiH;QAChI,MAAM,EAAE,oEAAoE;KAC7E;IACD,WAAW,EAAE;QACX,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,ssKAAssK;QACrtK,MAAM,EAAE,qDAAqD;KAC9D;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,8DAA8D;QAC7E,MAAM,EAAE,QAAQ;KACjB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,sBAAsB;QAC1C,aAAa,EAAE,sHAAsH;QACrI,MAAM,EAAE,4BAA4B;KACrC;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,6BAA6B;QACjD,aAAa,EAAE,oyDAAoyD;QACnzD,MAAM,EAAE,oCAAoC;KAC7C;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,4BAA4B;QAChD,aAAa,EAAE,41EAA41E;QAC32E,MAAM,EAAE,uCAAuC;KAChD;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,0BAA0B;QACzC,MAAM,EAAE,SAAS;KAClB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,oBAAoB;QACxC,aAAa,EAAE,mGAAmG;QAClH,MAAM,EAAE,qEAAqE;KAC9E;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,mhDAAmhD;QACliD,MAAM,EAAE,6BAA6B;KACtC;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,4BAA4B;QAChD,aAAa,EAAE,21CAA21C;QAC12C,MAAM,EAAE,2DAA2D;KACpE;IACD,KAAK,EAAE;QACL,kBAAkB,EAAE,gCAAgC;QACpD,aAAa,EAAE,6QAA6Q;QAC5R,MAAM,EAAE,aAAa;KACtB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,gCAAgC;QACpD,aAAa,EAAE,y4GAAy4G;QACx5G,MAAM,EAAE,0CAA0C;KACnD;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,yBAAyB;QAC7C,aAAa,EAAE,uNAAuN;QACtO,MAAM,EAAE,uEAAuE;KAChF;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,qEAAqE;QACpF,MAAM,EAAE,cAAc;KACvB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,4BAA4B;QAChD,aAAa,EAAE,6DAA6D;QAC5E,MAAM,EAAE,uBAAuB;KAChC;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,6BAA6B;QACjD,aAAa,EAAE,2vBAA2vB;QAC1wB,MAAM,EAAE,wBAAwB;KACjC;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,8pKAA8pK;QAC7qK,MAAM,EAAE,iEAAiE;KAC1E;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,uBAAuB;QAC3C,aAAa,EAAE,+6DAA+6D;QAC97D,MAAM,EAAE,uCAAuC;KAChD;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,s8BAAs8B;QACr9B,MAAM,EAAE,sjBAAsjB;KAC/jB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,gCAAgC;QACpD,aAAa,EAAE,oCAAoC;QACnD,MAAM,EAAE,oBAAoB;KAC7B;IACD,KAAK,EAAE;QACL,kBAAkB,EAAE,yBAAyB;QAC7C,aAAa,EAAE,iPAAiP;QAChQ,MAAM,EAAE,cAAc;KACvB;IACD,GAAG,EAAE;QACH,kBAAkB,EAAE,oBAAoB;QACxC,aAAa,EAAE,4BAA4B;QAC3C,MAAM,EAAE,GAAG;KACZ;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,olKAAolK;QACnmK,MAAM,EAAE,kEAAkE;KAC3E;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,6BAA6B;QACjD,aAAa,EAAE,oGAAoG;QACnH,MAAM,EAAE,UAAU;KACnB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,8BAA8B;QAClD,aAAa,EAAE,4BAA4B;QAC3C,MAAM,EAAE,QAAQ;KACjB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,o7BAAo7B;QACn8B,MAAM,EAAE,cAAc;KACvB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,mBAAmB;QACvC,aAAa,EAAE,gEAAgE;QAC/E,MAAM,EAAE,OAAO;KAChB;IACD,KAAK,EAAE;QACL,kBAAkB,EAAE,mBAAmB;QACvC,aAAa,EAAE,wuEAAwuE;QACvvE,MAAM,EAAE,uEAAuE;KAChF;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,8DAA8D;QAC7E,MAAM,EAAE,QAAQ;KACjB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,mBAAmB;QACvC,aAAa,EAAE,k4CAAk4C;QACj5C,MAAM,EAAE,kEAAkE;KAC3E;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,6BAA6B;QACjD,aAAa,EAAE,8XAA8X;QAC7Y,MAAM,EAAE,iCAAiC;KAC1C;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,eAAe;QACnC,aAAa,EAAE,gJAAgJ;QAC/J,MAAM,EAAE,yBAAyB;KAClC;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,qBAAqB;QACzC,aAAa,EAAE,+DAA+D;QAC9E,MAAM,EAAE,MAAM;KACf;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,mBAAmB;QACvC,aAAa,EAAE,qLAAqL;QACpM,MAAM,EAAE,gBAAgB;KACzB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,mCAAmC;QACvD,aAAa,EAAE,ojCAAojC;QACnkC,MAAM,EAAE,kBAAkB;KAC3B;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,qBAAqB;QACzC,aAAa,EAAE,qGAAqG;QACpH,MAAM,EAAE,OAAO;KAChB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,qBAAqB;QACzC,aAAa,EAAE,k5GAAk5G;QACj6G,MAAM,EAAE,0BAA0B;KACnC;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,yBAAyB;QAC7C,aAAa,EAAE,gDAAgD;QAC/D,MAAM,EAAE,kBAAkB;KAC3B;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,6BAA6B;QACjD,aAAa,EAAE,umCAAumC;QACtnC,MAAM,EAAE,gBAAgB;KACzB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,oBAAoB;QACxC,aAAa,EAAE,oCAAoC;QACnD,MAAM,EAAE,4BAA4B;KACrC;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,4CAA4C;QAChE,aAAa,EAAE,01kBAA01kB;QACz2kB,MAAM,EAAE,gDAAgD;KACzD;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,gCAAgC;QACpD,aAAa,EAAE,8kGAA8kG;QAC7lG,MAAM,EAAE,yEAAyE;KAClF;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,+aAA+a;QAC9b,MAAM,EAAE,uBAAuB;KAChC;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,ybAAyb;QACxc,MAAM,EAAE,2BAA2B;KACpC;IACD,YAAY,EAAE;QACZ,kBAAkB,EAAE,4BAA4B;QAChD,aAAa,EAAE,8BAA8B;QAC7C,MAAM,EAAE,YAAY;KACrB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,gCAAgC;QACpD,aAAa,EAAE,0sBAA0sB;QACztB,MAAM,EAAE,4BAA4B;KACrC;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,ueAAue;QACtf,MAAM,EAAE,8BAA8B;KACvC;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,0CAA0C;QAC9D,aAAa,EAAE,00BAA00B;QACz1B,MAAM,EAAE,yBAAyB;KAClC;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,qBAAqB;QACzC,aAAa,EAAE,2YAA2Y;QAC1Z,MAAM,EAAE,6DAA6D;KACtE;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,sBAAsB;QAC1C,aAAa,EAAE,oDAAoD;QACnE,MAAM,EAAE,OAAO;KAChB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,oBAAoB;QACxC,aAAa,EAAE,60CAA60C;QAC51C,MAAM,EAAE,kBAAkB;KAC3B;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,6xEAA6xE;QAC5yE,MAAM,EAAE,2CAA2C;KACpD;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,qCAAqC;QACpD,MAAM,EAAE,sCAAsC;KAC/C;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,kBAAkB;QACtC,aAAa,EAAE,oCAAoC;QACnD,MAAM,EAAE,uCAAuC;KAChD;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,mBAAmB;QACvC,aAAa,EAAE,2oLAA2oL;QAC1pL,MAAM,EAAE,gHAAgH;KACzH;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,gEAAgE;QAC/E,MAAM,EAAE,SAAS;KAClB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,gBAAgB;QACpC,aAAa,EAAE,+DAA+D;QAC9E,MAAM,EAAE,MAAM;KACf;IACD,KAAK,EAAE;QACL,kBAAkB,EAAE,iBAAiB;QACrC,aAAa,EAAE,oDAAoD;QACnE,MAAM,EAAE,KAAK;KACd;IACD,UAAU,EAAE;QACV,kBAAkB,EAAE,eAAe;QACnC,aAAa,EAAE,49cAA49c;QAC3+c,MAAM,EAAE,4IAA4I;KACrJ;IACD,YAAY,EAAE;QACZ,kBAAkB,EAAE,eAAe;QACnC,aAAa,EAAE,gEAAgE;QAC/E,MAAM,EAAE,YAAY;KACrB;IACD,OAAO,EAAE;QACP,kBAAkB,EAAE,cAAc;QAClC,aAAa,EAAE,gEAAgE;QAC/E,MAAM,EAAE,OAAO;KAChB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,qBAAqB;QACzC,aAAa,EAAE,+DAA+D;QAC9E,MAAM,EAAE,MAAM;KACf;IACD,aAAa,EAAE;QACb,kBAAkB,EAAE,aAAa;QACjC,aAAa,EAAE,gEAAgE;QAC/E,MAAM,EAAE,aAAa;KACtB;IACD,SAAS,EAAE;QACT,kBAAkB,EAAE,sBAAsB;QAC1C,aAAa,EAAE,qEAAqE;QACpF,MAAM,EAAE,SAAS;KAClB;IACD,QAAQ,EAAE;QACR,kBAAkB,EAAE,eAAe;QACnC,aAAa,EAAE,gEAAgE;QAC/E,MAAM,EAAE,QAAQ;KACjB;IACD,MAAM,EAAE;QACN,kBAAkB,EAAE,wBAAwB;QAC5C,aAAa,EAAE,kEAAkE;QACjF,MAAM,EAAE,MAAM;KACf;CACO,CAAC", "file": "zshBuiltinsCache.js", "sourceRoot": "../../src/"}