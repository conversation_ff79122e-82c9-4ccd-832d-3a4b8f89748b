{"version": 3, "sources": ["languageFeatures/fileConfigurationManager.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgRhG,4DAWC;AAzRD,2CAA6B;AAC7B,+CAAiC;AACjC,0EAA4D;AAC5D,8DAAoE;AACpE,yCAAsC;AAGtC,8CAA8C;AAC9C,8CAA0C;AAC1C,sDAAmD;AAcnD,SAAS,0BAA0B,CAAC,CAAoB,EAAE,CAAoB;IAC7E,OAAO,IAAA,gBAAM,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB,CAAC;AAED,MAAqB,wBAAyB,SAAQ,oBAAU;IAG/D,YACkB,MAAgC,EACjD,2BAAoC;QAEpC,KAAK,EAAE,CAAC;QAHS,WAAM,GAAN,MAAM,CAA0B;QAIjD,IAAI,CAAC,aAAa,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,EAAE,2BAA2B,EAAE,CAAC,CAAC;QACjF,MAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,YAAY,CAAC,EAAE;YACtD,oEAAoE;YACpE,qEAAqE;YACrE,mEAAmE;YACnE,WAAW;YACX,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAC7C,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;IAEM,KAAK,CAAC,8BAA8B,CAC1C,QAA6B,EAC7B,KAA+B;QAE/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC9D,IAAI,iBAAiB,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;QAC5E,CAAC;IACF,CAAC;IAEO,oBAAoB,CAAC,QAA6B;QACzD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3H,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,OAAO;YACN,OAAO,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxF,YAAY,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS;SACxG,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,0BAA0B,CACtC,QAA6B,EAC7B,OAA0B,EAC1B,KAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO;QACR,CAAC;QAED,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC9D,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC3D,IAAI,aAAa,EAAE,CAAC;YACnB,MAAM,kBAAkB,GAAG,MAAM,aAAa,CAAC;YAC/C,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;gBACnC,OAAO;YACR,CAAC;YAED,IAAI,kBAAkB,IAAI,0BAA0B,CAAC,kBAAkB,EAAE,cAAc,CAAC,EAAE,CAAC;gBAC1F,OAAO;YACR,CAAC;QACF,CAAC;QAED,MAAM,IAAI,GAAG,CAAC,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC;gBACJ,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,IAAI,EAAE,GAAG,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC;gBAC5F,OAAO,QAAQ,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC;YAClE,CAAC;YAAC,MAAM,CAAC;gBACR,OAAO,SAAS,CAAC;YAClB,CAAC;QACF,CAAC,CAAC,EAAE,CAAC;QAEL,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAE3C,MAAM,IAAI,CAAC;IACZ,CAAC;IAEM,KAAK,CAAC,kCAAkC,CAC9C,QAA6B,EAC7B,KAA+B;QAE/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC9D,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACxB,OAAO;QACR,CAAC;QAED,MAAM,IAAI,GAAoC;YAC7C,IAAI,EAAE,SAAS,CAAC,UAAU;YAC1B,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,iBAAiB,CAAC;SACnD,CAAC;QACF,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;IAEM,KAAK;QACX,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IAEO,cAAc,CACrB,QAA6B,EAC7B,OAA0B;QAE1B,OAAO;YACN,aAAa,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC;YACvD,WAAW,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;SAC1C,CAAC;IACH,CAAC;IAEO,gBAAgB,CACvB,QAA6B,EAC7B,OAA0B;QAE1B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAC/C,IAAA,kCAAoB,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,EAC1E,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEf,OAAO;YACN,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,UAAU,EAAE,OAAO,CAAC,OAAO;YAC3B,mBAAmB,EAAE,OAAO,CAAC,YAAY;YACzC,+EAA+E;YAC/E,gBAAgB,EAAE,IAAI;YACtB,8BAA8B,EAAE,MAAM,CAAC,GAAG,CAAU,gCAAgC,CAAC;YACrF,2BAA2B,EAAE,MAAM,CAAC,GAAG,CAAU,6BAA6B,CAAC;YAC/E,wCAAwC,EAAE,MAAM,CAAC,GAAG,CAAU,0CAA0C,CAAC;YACzG,wCAAwC,EAAE,MAAM,CAAC,GAAG,CAAU,0CAA0C,CAAC;YACzG,+CAA+C,EAAE,MAAM,CAAC,GAAG,CAAU,iDAAiD,CAAC;YACvH,oDAAoD,EAAE,MAAM,CAAC,GAAG,CAAU,sDAAsD,CAAC;YACjI,oCAAoC,EAAE,MAAM,CAAC,GAAG,CAAU,sCAAsC,CAAC;YACjG,0DAA0D,EAAE,MAAM,CAAC,GAAG,CAAU,4DAA4D,CAAC;YAC7I,uDAAuD,EAAE,MAAM,CAAC,GAAG,CAAU,yDAAyD,CAAC;YACvI,qDAAqD,EAAE,MAAM,CAAC,GAAG,CAAU,uDAAuD,CAAC;YACnI,kDAAkD,EAAE,MAAM,CAAC,GAAG,CAAU,oDAAoD,CAAC;YAC7H,2DAA2D,EAAE,MAAM,CAAC,GAAG,CAAU,6DAA6D,CAAC;YAC/I,0DAA0D,EAAE,MAAM,CAAC,GAAG,CAAU,4DAA4D,CAAC;YAC7I,6BAA6B,EAAE,MAAM,CAAC,GAAG,CAAU,+BAA+B,CAAC;YACnF,mCAAmC,EAAE,MAAM,CAAC,GAAG,CAAU,qCAAqC,CAAC;YAC/F,uCAAuC,EAAE,MAAM,CAAC,GAAG,CAAU,yCAAyC,CAAC;YACvG,UAAU,EAAE,MAAM,CAAC,GAAG,CAA4B,YAAY,CAAC;YAC/D,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAU,kBAAkB,CAAC;SACzD,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,QAA6B;QACnD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAC/C,IAAA,kCAAoB,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,EAC5D,QAAQ,CAAC,CAAC;QAEX,MAAM,iBAAiB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAC1D,IAAA,kCAAoB,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,wBAAwB,EACpF,QAAQ,CAAC,CAAC;QAEX,MAAM,WAAW,GAA0B;YAC1C,GAAG,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;YACzB,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC;YAChE,+BAA+B,EAAE,kCAAkC,CAAC,iBAAiB,CAAC;YACtF,2BAA2B,EAAE,wCAAwC,CAAC,iBAAiB,CAAC;YACxF,2BAA2B,EAAE,8BAA8B,CAAC,iBAAiB,CAAC;YAC9E,0BAA0B,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC,IAAI;YACpE,mCAAmC,EAAE,iBAAiB,CAAC,GAAG,CAAU,2BAA2B,EAAE,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAU,sBAAsB,EAAE,IAAI,CAAC;YACvL,uBAAuB,EAAE,IAAI;YAC7B,wCAAwC,EAAE,MAAM,CAAC,GAAG,CAAU,kDAAkD,EAAE,IAAI,CAAC;YACvH,kCAAkC,EAAE,IAAI;YACxC,2BAA2B,EAAE,MAAM,CAAC,GAAG,CAAU,+BAA+B,EAAE,IAAI,CAAC;YACvF,qCAAqC,EAAE,MAAM,CAAC,GAAG,CAAU,+CAA+C,EAAE,IAAI,CAAC;YACjH,iCAAiC,EAAE,IAAI;YACvC,yCAAyC,EAAE,MAAM,CAAC,GAAG,CAAU,qCAAqC,EAAE,IAAI,CAAC;YAC3G,iDAAiD,EAAE,MAAM,CAAC,GAAG,CAAU,6CAA6C,EAAE,IAAI,CAAC;YAC3H,6BAA6B,EAAE,IAAI,CAAC,0CAA0C,CAAC,iBAAiB,EAAE,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;YACzJ,iCAAiC,EAAE,iBAAiB,CAAC,GAAG,CAAW,mCAAmC,CAAC;YACvG,yBAAyB,EAAE,iBAAiB,CAAC,GAAG,CAAU,2BAA2B,EAAE,KAAK,CAAC;YAC7F,kCAAkC,EAAE,IAAI;YACxC,0BAA0B,EAAE,IAAI;YAChC,oBAAoB,EAAE,IAAI;YAC1B,2BAA2B,EAAE,IAAI;YACjC,qBAAqB,EAAE,IAAI;YAC3B,kCAAkC,EAAE,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC;YAC9E,GAAG,wBAAwB,CAAC,MAAM,CAAC;YACnC,GAAG,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,CAAC;SACxD,CAAC;QAEF,OAAO,WAAW,CAAC;IACpB,CAAC;IAEO,uBAAuB,CAAC,MAAqC;QACpE,QAAQ,MAAM,CAAC,GAAG,CAAS,YAAY,CAAC,EAAE,CAAC;YAC1C,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,OAAO,CAAC,CAAC,OAAO,MAAM,CAAC;QACxB,CAAC;IACF,CAAC;IAEO,0CAA0C,CAAC,MAAqC,EAAE,eAAuC;QAChI,OAAO,eAAe,IAAI,MAAM,CAAC,GAAG,CAAW,+BAA+B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE;YACxF,0EAA0E;YAC1E,MAAM,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9C,2FAA2F;YAC3F,0CAA0C;YAC1C,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAG,CAAC,IAAI,CAAC;gBAC1D,CAAC,CAAC,EAAE;gBACJ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,eAAe,CAAE,CAAC,CAAC,IAAI,CAAC;YAC/D,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9B,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;oBACvC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC;wBAChF,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACJ,CAAC;IAEO,6BAA6B,CAAC,MAAqC;QAC1E,MAAM,wBAAwB,GAAG,MAAM,CAAC,GAAG,CAAwB,kCAAkC,CAAC,CAAC;QACvG,MAAM,8BAA8B,GAAG,MAAM,CAAC,GAAG,CAA+C,iCAAiC,CAAC,CAAC;QACnI,OAAO;YACN,yBAAyB;YACzB,wBAAwB,EAAE,sBAAsB,CAAC,MAAM,CAAC,GAAG,CAAuC,2BAA2B,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC;YAC/I,yBAAyB,EAAE,8BAA8B,KAAK,iBAAiB,CAAC,CAAC,CAAC,IAAI;gBACrF,CAAC,CAAC,8BAA8B,KAAK,eAAe,CAAC,CAAC,CAAC,KAAK;oBAC3D,CAAC,CAAC,MAAM;YACV,wBAAwB;YAExB,4EAA4E;YAC5E,GAAG,CAAC,wBAAwB,KAAK,SAAS,CAAC,CAAC,CAAC;gBAC5C,wBAAwB,EAAE,8BAA8B,KAAK,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAC,GAAG,CAAwC,2BAA2B,EAAE,KAAK,CAAC,EAAE,SAAS,CAAC;gBACrN,8BAA8B,EAAE,MAAM,CAAC,GAAG,CAAU,iCAAiC,CAAC;gBACtF,qBAAqB,EAAE,MAAM,CAAC,GAAG,CAAS,wBAAwB,CAAC;gBACnE,+BAA+B,EAAE,MAAM,CAAC,GAAG,CAAU,kCAAkC,CAAC;aACxF,CAAC,CAAC,CAAC,EAAE,CAAC;SACP,CAAC;IACH,CAAC;CACD;AAnOD,2CAmOC;AAED,SAAS,sBAAsB,CAAiB,KAAQ,EAAE,GAAM;IAC/D,OAAO,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAsB,CAAC;AAC3D,CAAC;AAED,MAAa,qBAAqB;;AAAlC,sDAQC;AAPgB,mEAA6C,GAAG,2DAA2D,CAAC;AAC5G,2CAAqB,GAAG,mCAAmC,CAAC;AAC5D,0CAAoB,GAAG,kCAAkC,CAAC;AAC1D,8DAAwC,GAAG,sDAAsD,CAAC;AAClG,qDAA+B,GAAG,6CAA6C,CAAC;AAChF,oDAA8B,GAAG,4CAA4C,CAAC;AAC9E,6CAAuB,GAAG,qCAAqC,CAAC;AAGjF,SAAgB,wBAAwB,CAAC,MAAqC;IAC7E,OAAO;QACN,8BAA8B,EAAE,oCAAoC,CAAC,MAAM,CAAC;QAC5E,qDAAqD,EAAE,CAAC,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC,6CAA6C,EAAE,IAAI,CAAC;QACtJ,sCAAsC,EAAE,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC,qBAAqB,EAAE,KAAK,CAAC;QAC/G,6BAA6B,EAAE,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC,oBAAoB,EAAE,KAAK,CAAC;QACrG,gDAAgD,EAAE,CAAC,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC,wCAAwC,EAAE,IAAI,CAAC;QAC5I,wCAAwC,EAAE,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC,+BAA+B,EAAE,KAAK,CAAC;QAC3H,uCAAuC,EAAE,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC,8BAA8B,EAAE,KAAK,CAAC;QACzH,gCAAgC,EAAE,MAAM,CAAC,GAAG,CAAU,qBAAqB,CAAC,uBAAuB,EAAE,KAAK,CAAC;KAClG,CAAC;AACZ,CAAC;AAED,SAAS,oCAAoC,CAAC,MAAqC;IAClF,QAAQ,MAAM,CAAC,GAAG,CAAS,mCAAmC,CAAC,EAAE,CAAC;QACjE,KAAK,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC;QAC3B,KAAK,UAAU,CAAC,CAAC,OAAO,UAAU,CAAC;QACnC,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC;QACzB,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;IAC3B,CAAC;AACF,CAAC;AAED,SAAS,kCAAkC,CAAC,MAAqC;IAChF,QAAQ,MAAM,CAAC,GAAG,CAAS,uBAAuB,CAAC,EAAE,CAAC;QACrD,KAAK,kBAAkB,CAAC,CAAC,OAAO,kBAAkB,CAAC;QACnD,KAAK,UAAU,CAAC,CAAC,OAAO,UAAU,CAAC;QACnC,KAAK,cAAc,CAAC,CAAC,OAAO,cAAc,CAAC;QAC3C,OAAO,CAAC,CAAC,OAAO,SAAS,CAAC;IAC3B,CAAC;AACF,CAAC;AAED,SAAS,wCAAwC,CAAC,MAAqC;IACtF,QAAQ,MAAM,CAAC,GAAG,CAAS,6BAA6B,CAAC,EAAE,CAAC;QAC3D,KAAK,SAAS,CAAC,CAAC,OAAO,SAAS,CAAC;QACjC,KAAK,OAAO,CAAC,CAAC,OAAO,OAAO,CAAC;QAC7B,KAAK,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC;QACvB,OAAO,CAAC,CAAC,OAAO,MAAM,CAAC;IACxB,CAAC;AACF,CAAC;AAED,SAAS,8BAA8B,CAAC,MAAqC;IAC5E,QAAQ,MAAM,CAAC,GAAG,CAAS,6BAA6B,CAAC,EAAE,CAAC;QAC3D,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;QAC/B,KAAK,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC;QAC3B,OAAO,CAAC,CAAC,OAAO,MAAM,CAAC;IACxB,CAAC;AACF,CAAC", "file": "fileConfigurationManager.js", "sourceRoot": "../../src/"}