{"version": 3, "sources": ["modes/embeddedSupport.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAsBhG,gDAiEC;AArFD,mDAA4F;AAe/E,QAAA,cAAc,GAAG,IAAI,CAAC;AAKnC,SAAgB,kBAAkB,CAAC,eAAgC,EAAE,QAAsB;IAC1F,MAAM,OAAO,GAAqB,EAAE,CAAC;IACrC,MAAM,OAAO,GAAG,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;IAClE,IAAI,WAAW,GAAW,EAAE,CAAC;IAC7B,IAAI,iBAAiB,GAAkB,IAAI,CAAC;IAC5C,IAAI,kBAAkB,GAAuB,SAAS,CAAC;IACvD,MAAM,eAAe,GAAa,EAAE,CAAC;IAErC,IAAI,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAC3B,OAAO,KAAK,KAAK,yBAAS,CAAC,GAAG,EAAE,CAAC;QAChC,QAAQ,KAAK,EAAE,CAAC;YACf,KAAK,yBAAS,CAAC,QAAQ;gBACtB,WAAW,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;gBACrC,iBAAiB,GAAG,IAAI,CAAC;gBACzB,kBAAkB,GAAG,YAAY,CAAC;gBAClC,MAAM;YACP,KAAK,yBAAS,CAAC,MAAM;gBACpB,OAAO,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBACjG,MAAM;YACP,KAAK,yBAAS,CAAC,MAAM;gBACpB,OAAO,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,kBAAkB,EAAE,KAAK,EAAE,OAAO,CAAC,cAAc,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBAC9G,MAAM;YACP,KAAK,yBAAS,CAAC,aAAa;gBAC3B,iBAAiB,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC3C,MAAM;YACP,KAAK,yBAAS,CAAC,cAAc;gBAC5B,IAAI,iBAAiB,KAAK,KAAK,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;oBAC3E,IAAI,KAAK,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;oBACnC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;wBAC3C,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC3C,CAAC;oBACD,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7B,CAAC;qBAAM,IAAI,iBAAiB,KAAK,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;oBACnF,IAAI,oEAAoE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;wBACvG,kBAAkB,GAAG,YAAY,CAAC;oBACnC,CAAC;yBAAM,IAAI,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC;wBACpE,kBAAkB,GAAG,YAAY,CAAC;oBACnC,CAAC;yBAAM,CAAC;wBACP,kBAAkB,GAAG,SAAS,CAAC;oBAChC,CAAC;gBACF,CAAC;qBAAM,CAAC;oBACP,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,iBAAkB,CAAC,CAAC;oBACrE,IAAI,mBAAmB,EAAE,CAAC;wBACzB,IAAI,KAAK,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;wBACrC,IAAI,GAAG,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;wBAChC,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;wBAC5C,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,GAAG,EAAE,CAAC;4BAC7C,KAAK,EAAE,CAAC;4BACR,GAAG,EAAE,CAAC;wBACP,CAAC;wBACD,OAAO,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,mBAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC;oBACrF,CAAC;gBACF,CAAC;gBACD,iBAAiB,GAAG,IAAI,CAAC;gBACzB,MAAM;QACR,CAAC;QACD,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IACxB,CAAC;IACD,OAAO;QACN,iBAAiB,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,iBAAiB,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;QAChF,mBAAmB,EAAE,CAAC,UAAkB,EAAE,qBAA8B,EAAE,EAAE,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,qBAAqB,CAAC;QACtJ,qBAAqB,EAAE,CAAC,QAAkB,EAAE,EAAE,CAAC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;QACjG,sBAAsB,EAAE,GAAG,EAAE,CAAC,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC;QACvE,kBAAkB,EAAE,GAAG,EAAE,CAAC,eAAe;KACzC,CAAC;AACH,CAAC;AAGD,SAAS,iBAAiB,CAAC,QAAsB,EAAE,OAAyB,EAAE,KAAY;IACzF,MAAM,MAAM,GAAoB,EAAE,CAAC;IACnC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,wBAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7D,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;IACnF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC9B,IAAI,MAAM,CAAC,GAAG,GAAG,aAAa,IAAI,MAAM,CAAC,KAAK,GAAG,SAAS,EAAE,CAAC;YAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YACpD,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,UAAU;oBACjB,GAAG,EAAE,QAAQ;oBACb,UAAU,EAAE,MAAM;iBAClB,CAAC,CAAC;YACJ,CAAC;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;YAC5C,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACxC,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,QAAQ;oBACf,GAAG,EAAE,MAAM;oBACX,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,cAAc,EAAE,MAAM,CAAC,cAAc;iBACrC,CAAC,CAAC;YACJ,CAAC;YACD,aAAa,GAAG,GAAG,CAAC;YACpB,UAAU,GAAG,MAAM,CAAC;QACrB,CAAC;IACF,CAAC;IACD,IAAI,aAAa,GAAG,SAAS,EAAE,CAAC;QAC/B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAClE,MAAM,CAAC,IAAI,CAAC;YACX,KAAK,EAAE,UAAU;YACjB,GAAG,EAAE,MAAM;YACX,UAAU,EAAE,MAAM;SAClB,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,sBAAsB,CAAC,SAAuB,EAAE,OAAyB;IACjF,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC9B,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACnE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC/B,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,MAAM,CAAC;YACf,CAAC;QACF,CAAC;IACF,CAAC;IACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpB,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAAC,QAAsB,EAAE,OAAyB,EAAE,QAAkB;IACnG,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC3C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC9B,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,EAAE,CAAC;YAC5B,IAAI,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC1B,OAAO,MAAM,CAAC,UAAU,CAAC;YAC1B,CAAC;QACF,CAAC;aAAM,CAAC;YACP,MAAM;QACP,CAAC;IACF,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,mBAAmB,CAAC,QAAsB,EAAE,QAA0B,EAAE,UAAkB,EAAE,qBAA8B;IAClI,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;IACtC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,UAAU,GAAG,EAAE,CAAC;IACpB,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;QAC1B,IAAI,CAAC,CAAC,UAAU,KAAK,UAAU,IAAI,CAAC,CAAC,qBAAqB,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC;YAClF,MAAM,GAAG,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YACrG,MAAM,IAAI,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACjE,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;YACnB,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;IACF,CAAC;IACD,MAAM,GAAG,wBAAwB,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,EAAE,CAAC,CAAC;IACrG,OAAO,4BAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAChF,CAAC;AAED,SAAS,SAAS,CAAC,CAAiB;IACnC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;QACtB,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,KAAK,CAAC,CAAC,OAAO,sBAAc,GAAG,GAAG,CAAC;QACzC,CAAC;IACF,CAAC;IACD,OAAO,EAAE,CAAC;AACX,CAAC;AACD,SAAS,SAAS,CAAC,CAAiB;IACnC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;QACtB,QAAQ,CAAC,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,KAAK,CAAC,CAAC,OAAO,GAAG,CAAC;YACvB,KAAK,YAAY,CAAC,CAAC,OAAO,GAAG,CAAC;QAC/B,CAAC;IACF,CAAC;IACD,OAAO,EAAE,CAAC;AACX,CAAC;AACD,SAAS,aAAa,CAAC,CAAiB,EAAE,OAAe;IACxD,IAAI,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,UAAU,KAAK,YAAY,EAAE,CAAC;QACxD,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC7D,CAAC;IACD,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;QAC5B,MAAM,WAAW,GAAG,iBAAiB,CAAC;QACtC,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE;YACxD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YACjD,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3C,OAAO,GAAG,MAAM,GAAG,CAAC;YACrB,CAAC;YACD,OAAO,IAAI,MAAM,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,OAAO,CAAC;AAChB,CAAC;AAED,SAAS,wBAAwB,CAAC,MAAc,EAAE,KAAa,EAAE,GAAW,EAAE,UAAkB,EAAE,MAAc,EAAE,KAAa;IAC9H,MAAM,IAAI,MAAM,CAAC;IACjB,IAAI,aAAa,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,+DAA+D;IACnG,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAClC,MAAM,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC;YAChC,4CAA4C;YAC5C,aAAa,GAAG,CAAC,CAAC;YAClB,MAAM,IAAI,EAAE,CAAC;QACd,CAAC;aAAM,CAAC;YACP,aAAa,EAAE,CAAC;QACjB,CAAC;IACF,CAAC;IACD,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3D,MAAM,IAAI,KAAK,CAAC;IAChB,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAC,MAAc,EAAE,GAAW,EAAE,CAAS;IACrD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;QACd,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACX,MAAM,IAAI,GAAG,CAAC;QACf,CAAC;QACD,CAAC,KAAK,CAAC,CAAC;QACR,GAAG,IAAI,GAAG,CAAC;IACZ,CAAC;IACD,OAAO,MAAM,CAAC;AACf,CAAC;AAED,SAAS,oBAAoB,CAAC,aAAqB;IAClD,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC1D,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC;IACb,CAAC;IACD,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;AACxC,CAAC", "file": "embeddedSupport.js", "sourceRoot": "../../src/"}