{"version": 3, "sources": ["modes/selectionRanges.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAKhG,gDAgBC;AAnBD,mDAA+F;AAC/F,kDAA2D;AAEpD,KAAK,UAAU,kBAAkB,CAAC,aAA4B,EAAE,QAAsB,EAAE,SAAqB;IACnH,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAC,QAAQ,EAAC,EAAE;QACjD,MAAM,SAAS,GAAG,MAAM,QAAS,CAAC,iBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACzE,MAAM,IAAI,GAAG,aAAa,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACjE,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACpC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAC/D,IAAI,GAAG,GAAG,KAAK,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,IAAI,IAAA,iCAAqB,EAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/E,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;YAClB,CAAC;YACD,GAAG,CAAC,MAAM,GAAG,SAAS,CAAC;YACvB,OAAO,KAAK,CAAC;QACd,CAAC;QACD,OAAO,SAAS,IAAI,8BAAc,CAAC,MAAM,CAAC,qBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC,CAAC;AACL,CAAC", "file": "selectionRanges.js", "sourceRoot": "../../src/"}