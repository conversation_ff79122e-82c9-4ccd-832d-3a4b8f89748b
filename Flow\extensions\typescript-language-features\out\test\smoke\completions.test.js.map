{"version": 3, "sources": ["test/smoke/completions.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,iBAAe;AACf,+CAAiC;AACjC,sEAA2F;AAC3F,oDAAqJ;AACrJ,iDAAiD;AAEjD,MAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAE7D,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;AAEzD,KAAK,CAAC,IAAI,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACzC,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAsB;QACzD,CAAC,kBAAM,CAAC,mBAAmB,CAAC,EAAE,QAAQ;QACtC,CAAC,kBAAM,CAAC,+BAA+B,CAAC,EAAE,KAAK;QAC/C,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,QAAQ;QAC7B,CAAC,kBAAM,CAAC,kBAAkB,CAAC,EAAE,MAAM;QACnC,CAAC,kBAAM,CAAC,gBAAgB,CAAC,EAAE,OAAO;QAClC,CAAC,kBAAM,CAAC,oBAAoB,CAAC,EAAE,QAAQ;QACvC,CAAC,kBAAM,CAAC,oBAAoB,CAAC,EAAE,QAAQ;KACvC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAwB,EAAE,CAAC;IAC7C,IAAI,SAAS,GAA2B,EAAE,CAAC;IAE3C,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,2DAA2D;QAC3D,MAAM,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,qCAAqC,CAAE,CAAC,QAAQ,EAAE,CAAC;QAExF,qCAAqC;QACrC,SAAS,GAAG,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,cAAc,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QACnB,IAAA,oBAAU,EAAC,YAAY,CAAC,CAAC;QAEzB,iBAAiB;QACjB,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QAE/C,OAAO,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kCAAkC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QACvC,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,qBAAqB,EACrB,OAAO,CACP,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,qBAAqB,EACrB,SAAS,CACT,EACD,WAAW,MAAM,EAAE,CACnB,CAAC;QACH,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;QAC9E,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,qBAAqB,EACrB,OAAO,CACP,CAAC;YAEF,MAAM,IAAA,wCAAmB,EAAC,eAAe,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;YAE9D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,qBAAqB,EACrB,UAAU,CACV,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iEAAiE,EAAE,KAAK,IAAI,EAAE;QAClF,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,uBAAuB,EACvB,OAAO,CACP,CAAC;YAEF,MAAM,IAAA,wCAAmB,EAAC,eAAe,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;YAE9D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,uBAAuB,EACvB,WAAW,CACX,EAAE,WAAW,MAAM,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2EAA2E,EAAE,KAAK,IAAI,EAAE;QAC5F,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,iCAAiC,EACjC,MAAM,CACN,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,iCAAiC,EACjC,kBAAkB,CAClB,EAAE,WAAW,MAAM,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;QACxE,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI;YAC9B,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;YAC1B,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE;SAC3B,EAAE,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,kCAAkC,EAClC,MAAM,CACN,CAAC;YAEF,MAAM,IAAA,wCAAmB,EAAC,eAAe,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;YAE/D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,kCAAkC,EAClC,oBAAoB,MAAM,EAAE,CAC5B,CAAC,CAAC;YAEJ,IAAA,oBAAU,EAAC,YAAY,CAAC,CAAC;YACzB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kCAAkC,CAAC,CAAC;QAC1E,CAAC;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;QAC7E,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,+CAA+C;YAC/C,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,+BAA+B,EAC/B,MAAM,CACN,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,+BAA+B,EAC/B,KAAK,CACL,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;QACzF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,uBAAuB,EACvB,sCAAsC,EACtC,oCAAoC,EACpC,GAAG,EACH,0CAA0C,EAC1C,gBAAgB,CAChB,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,uBAAuB,EACvB,sCAAsC,EACtC,oCAAoC,EACpC,GAAG,EACH,0CAA0C,EAC1C,cAAc,CACd,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+EAA+E,EAAE,KAAK,IAAI,EAAE;QAChG,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,+BAA+B,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAExF,wBAAwB;QACxB,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,8BAA8B,EAC9B,UAAU,CACV,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,8BAA8B,EAC9B,iBAAiB,CACjB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI,CAAC,4EAA4E,EAAE,KAAK,IAAI,EAAE;QAClG,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,+BAA+B,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAExF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,8BAA8B,EAC9B,UAAU,CACV,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,8BAA8B,EAC9B,iBAAiB,CACjB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,qJAAqJ,EAAE,KAAK,IAAI,EAAE;QACtK,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,+BAA+B,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAExF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,8BAA8B,EAC9B,mBAAmB,CACnB,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,8BAA8B,EAC9B,iBAAiB,CACjB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI,CAAC,kJAAkJ,EAAE,KAAK,IAAI,EAAE;QACxK,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,+BAA+B,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;QAExF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,8BAA8B,EAC9B,mBAAmB,CACnB,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,8BAA8B,EAC9B,iBAAiB,CACjB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;QAC5E,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,wBAAwB,EACxB,WAAW,EACX,WAAW,EACX,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,wBAAwB,EACxB,WAAW,EACX,iBAAiB,EACjB,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oFAAoF,EAAE,KAAK,IAAI,EAAE;QACrG,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,mBAAmB,EACnB,WAAW,EACX,WAAW,EACX,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,mBAAmB,EACnB,WAAW,EACX,qBAAqB,EACrB,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uFAAuF,EAAE,KAAK,IAAI,EAAE;QACxG,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,mBAAmB,EACnB,WAAW,EACX,gBAAgB,EAChB,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,mBAAmB,EACnB,WAAW,EACX,qBAAqB,EACrB,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kEAAkE,EAAE,KAAK,IAAI,EAAE;QACnF,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEvE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,kBAAkB,EAClB,OAAO,CACP,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,kBAAkB,EAClB,MAAM,CACN,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oEAAoE,EAAE,KAAK,IAAI,EAAE;QACrF,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,kBAAkB,EAClB,OAAO,CACP,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,kBAAkB,EAClB,KAAK,CACL,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kFAAkF,EAAE,KAAK,IAAI,EAAE;QACnG,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEvE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,aAAa,EACb,YAAY,EACZ,WAAW,EACX,WAAW,EACX,KAAK,EACL,GAAG,CACH,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,aAAa,EACb,YAAY,EACZ,WAAW,EACX,eAAe,EACf,KAAK,EACL,GAAG,CACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2FAA2F,EAAE,KAAK,IAAI,EAAE;QAC5G,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,aAAa,EACb,YAAY,EACZ,WAAW,EACX,WAAW,EACX,KAAK,EACL,GAAG,CACH,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,aAAa,EACb,YAAY,EACZ,WAAW,EACX,cAAc,EACd,KAAK,EACL,GAAG,CACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6EAA6E,EAAE,KAAK,IAAI,EAAE;QAC9F,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEvE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,6BAA6B,EAC7B,eAAe,CACf,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,6BAA6B,EAC7B,gBAAgB,CAChB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,kEAAkE;IAClE,IAAI,CAAC,IAAI,CAAC,2EAA2E,EAAE,KAAK,IAAI,EAAE;QACjG,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,6BAA6B,EAC7B,eAAe,CACf,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,6BAA6B,EAC7B,aAAa,CACb,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,aAAa,EACb,WAAW,EACX,cAAc,EACd,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,aAAa,EACb,WAAW,EACX,eAAe,EACf,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;QACzE,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,aAAa,EACb,WAAW,EACX,SAAS,EACT,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,aAAa,EACb,WAAW,EACX,eAAe,EACf,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2EAA2E,EAAE,KAAK,IAAI,EAAE;QAC5F,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,aAAa,EACb,WAAW,EACX,gBAAgB,EAChB,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,aAAa,EACb,WAAW,EACX,eAAe,EACf,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2FAA2F,EAAE,KAAK,IAAI,EAAE;QAC5G,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,aAAa,EACb,WAAW,EACX,WAAW,EACX,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,aAAa,EACb,WAAW,EACX,eAAe,EACf,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,wFAAwF,EAAE,KAAK,IAAI,EAAE;QACzG,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;QAEvE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,wCAAwC,EACxC,iBAAiB,EACjB,oBAAoB,EACpB,KAAK,EACL,GAAG,CACH,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,wCAAwC,EACxC,iBAAiB,EACjB,2BAA2B,EAC3B,KAAK,EACL,GAAG,CACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0FAA0F,EAAE,KAAK,IAAI,EAAE;QAC3G,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,wCAAwC,EACxC,iBAAiB,EACjB,oBAAoB,EACpB,KAAK,EACL,GAAG,CACH,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,wCAAwC,EACxC,iBAAiB,EACjB,0BAA0B,EAC1B,KAAK,EACL,GAAG,CACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,IAAI,CAAC,iFAAiF,EAAE,KAAK,IAAI,EAAE;QACvG,MAAM,IAAA,2BAAe,EAAC,eAAe,EAAE,kBAAM,CAAC,UAAU,EAAE,WAAW,EAAE,KAAK,EAAC,MAAM,EAAC,EAAE;YACrF,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,yCAAyC,EACzC,iBAAiB,EACjB,mBAAmB,EACnB,KAAK,EACL,GAAG,CACH,CAAC;YAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;YAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,wCAAwC,EACxC,iBAAiB,EACjB,8BAA8B,EAC9B,KAAK,EACL,GAAG,CACH,EACD,WAAW,MAAM,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,IAAA,wBAAY,EAAC,eAAe,EAAE,EAAE,CAAC,kBAAM,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;QAExE,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAgB,EAAC,eAAe,EACpD,WAAW,EACX,WAAW,EACX,WAAW,EACX,gBAAgB,EAChB,KAAK,EACL,GAAG,CACH,CAAC;QAEF,MAAM,IAAA,0CAAqB,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAE3D,IAAA,gCAAoB,EAAC,MAAM,EAC1B,IAAA,qBAAS,EACR,WAAW,EACX,WAAW,EACX,WAAW,EACX,cAAc,EACd,KAAK,EACL,GAAG,CACH,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "completions.test.js", "sourceRoot": "../../../src/"}