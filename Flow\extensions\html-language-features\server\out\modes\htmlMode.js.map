{"version": 3, "sources": ["modes/htmlMode.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAUhG,kCAiFC;AAzFD,8DAA8D;AAQ9D,SAAgB,WAAW,CAAC,mBAAwC,EAAE,SAAoB;IACzF,MAAM,aAAa,GAAG,IAAA,0CAAqB,EAAe,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/H,OAAO;QACN,KAAK;YACJ,OAAO,MAAM,CAAC;QACf,CAAC;QACD,KAAK,CAAC,iBAAiB,CAAC,QAAsB,EAAE,QAAkB;YACjE,OAAO,mBAAmB,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC;QACD,UAAU,CAAC,QAAsB,EAAE,QAAkB,EAAE,eAAgC,EAAE,QAAQ,GAAG,SAAS,CAAC,QAAQ;YACrH,MAAM,YAAY,GAAG,QAAQ,EAAE,IAAI,CAAC;YACpC,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;YACjD,OAAO,CAAC,yBAAyB,GAAG,YAAY,EAAE,eAAe,KAAK,IAAI,CAAC;YAC3E,OAAO,CAAC,qBAAqB,GAAG,YAAY,EAAE,UAAU,EAAE,qBAAqB,IAAI,cAAc,CAAC;YAElG,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,MAAM,cAAc,GAAG,mBAAmB,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;YACnH,OAAO,cAAc,CAAC;QACvB,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,QAAsB,EAAE,QAAkB,EAAE,QAAmB;YAC5E,OAAO,mBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5G,CAAC;QACD,KAAK,CAAC,qBAAqB,CAAC,QAAsB,EAAE,QAAkB;YACrE,OAAO,mBAAmB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpG,CAAC;QACD,KAAK,CAAC,iBAAiB,CAAC,QAAsB,EAAE,eAAgC;YAC/E,OAAO,mBAAmB,CAAC,iBAAiB,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;QACzE,CAAC;QACD,KAAK,CAAC,mBAAmB,CAAC,QAAsB;YAC/C,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvF,CAAC;QACD,KAAK,CAAC,MAAM,CAAC,QAAsB,EAAE,KAAY,EAAE,YAA+B,EAAE,QAAQ,GAAG,SAAS,CAAC,QAAQ;YAChH,MAAM,cAAc,GAA4B,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;YAClF,IAAI,cAAc,CAAC,kBAAkB,EAAE,CAAC;gBACvC,cAAc,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,GAAG,SAAS,CAAC;YACnF,CAAC;iBAAM,CAAC;gBACP,cAAc,CAAC,kBAAkB,GAAG,QAAQ,CAAC;YAC9C,CAAC;YACD,KAAK,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;YACpC,OAAO,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC;QACpE,CAAC;QACD,KAAK,CAAC,gBAAgB,CAAC,QAAsB;YAC5C,OAAO,mBAAmB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC;QACD,KAAK,CAAC,YAAY,CAAC,QAAsB,EAAE,QAAkB,EAAE,IAA+B,EAAE,QAAQ,GAAG,SAAS,CAAC,QAAQ;YAC5H,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;gBAC1B,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;oBACnD,MAAM,YAAY,GAAG,QAAQ,EAAE,IAAI,CAAC;oBACpC,MAAM,OAAO,GAAG,KAAK,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC;oBACjD,OAAO,CAAC,qBAAqB,GAAG,YAAY,EAAE,UAAU,EAAE,qBAAqB,IAAI,cAAc,CAAC;oBAElG,OAAO,mBAAmB,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;gBACtG,CAAC;YACF,CAAC;iBAAM,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;gBACjC,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC3D,OAAO,mBAAmB,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC3F,CAAC;YACF,CAAC;YACD,OAAO,IAAI,CAAC;QACb,CAAC;QACD,KAAK,CAAC,QAAQ,CAAC,QAAsB,EAAE,QAAkB,EAAE,OAAe;YACzE,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,OAAO,mBAAmB,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAChF,CAAC;QACD,KAAK,CAAC,iBAAiB,CAAC,QAAsB;YAC7C,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;QACD,KAAK,CAAC,uBAAuB,CAAC,QAAsB,EAAE,QAAkB;YACvE,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,OAAO,mBAAmB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QACtF,CAAC;QACD,KAAK,CAAC,eAAe,CAAC,QAAsB,EAAE,QAAkB;YAC/D,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,OAAO,mBAAmB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QACtF,CAAC;QACD,OAAO;YACN,aAAa,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;KACD,CAAC;AACH,CAAC;AAED,SAAS,KAAK,CAAC,GAAQ,EAAE,GAAQ;IAChC,IAAI,GAAG,EAAE,CAAC;QACT,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;YACvB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC;QACF,CAAC;IACF,CAAC;IACD,OAAO,GAAG,CAAC;AACZ,CAAC", "file": "htmlMode.js", "sourceRoot": "../../src/"}