{"version": 3, "sources": ["test/formatting.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;gGAGgG;AAChG,iBAAe;AACf,2CAA6B;AAC7B,uCAAyB;AAEzB,+CAAiC;AACjC,0DAAsH;AAEtH,oDAA6C;AAC7C,2CAA+C;AAE/C,KAAK,CAAC,0BAA0B,EAAE,GAAG,EAAE;IAEtC,KAAK,UAAU,YAAY,CAAC,KAAa,EAAE,QAAgB,EAAE,OAAa,EAAE,aAAiC,EAAE,OAAgB;QAC9H,MAAM,SAAS,GAAG;YACjB,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;SAC7C,CAAC;QACF,MAAM,aAAa,GAAG,IAAA,gCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,kCAAkB,CAAC,MAAM,EAAE,IAAA,sBAAa,GAAE,CAAC,CAAC;QAE/H,IAAI,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,cAAc,CAAC;QACnB,IAAI,gBAAgB,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7B,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;YAE/E,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACpC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;QAC5E,CAAC;aAAM,CAAC;YACP,gBAAgB,GAAG,CAAC,CAAC;YACrB,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC;QAC/B,CAAC;QACD,MAAM,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC,uBAAuB,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QAChF,MAAM,KAAK,GAAG,qBAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC;QACvG,IAAI,CAAC,aAAa,EAAE,CAAC;YACpB,aAAa,GAAG,iCAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAM,EAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QAEvH,MAAM,MAAM,GAAG,4BAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACzD,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,UAAU,uBAAuB,CAAC,WAAmB,EAAE,YAAoB,EAAE,OAAa,EAAE,aAAiC;QACjI,MAAM,KAAK,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACrJ,MAAM,QAAQ,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC3J,MAAM,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC;IAC3E,CAAC;IAED,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QAC5B,MAAM,YAAY,CAAC,wCAAwC,EAAE,sDAAsD,CAAC,CAAC;QACrH,MAAM,YAAY,CAAC,0CAA0C,EAAE,sDAAsD,CAAC,CAAC;QACvH,MAAM,YAAY,CAAC,0CAA0C,EAAE,8CAA8C,CAAC,CAAC;IAChH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QACjC,MAAM,YAAY,CAAC,6CAA6C,EAAE,2DAA2D,CAAC,CAAC;QAC/H,MAAM,YAAY,CAAC,qDAAqD,EAAE,qEAAqE,CAAC,CAAC;QACjJ,MAAM,YAAY,CAAC,yDAAyD,EAAE,+EAA+E,CAAC,CAAC;QAC/J,MAAM,YAAY,CAAC,6DAA6D,EAAE,+EAA+E,CAAC,CAAC;QACnK,MAAM,YAAY,CAAC,iFAAiF,EAAE,uGAAuG,CAAC,CAAC;QAC/M,MAAM,YAAY,CAAC,+DAA+D,EAAE,qEAAqE,CAAC,CAAC;IAC5J,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QAC5C,uBAAuB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;QACpD,uBAAuB,CAAC,YAAY,EAAE,oBAAoB,EAAE,SAAS,EAAE,iCAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1G,uBAAuB,CAAC,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,iCAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACvG,uBAAuB,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QACjC,MAAM,YAAY,CAAC,mEAAmE,EAAE,+EAA+E,CAAC,CAAC;IAC1K,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QAC1C,MAAM,YAAY,CAAC,yGAAyG,EAAE,gJAAgJ,CAAC,CAAC;IACjR,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QAChC,MAAM,YAAY,CAAC,oEAAoE,EAAE,sGAAsG,CAAC,CAAC;IAClM,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QACjC,MAAM,OAAO,GAAsB,iCAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACrE,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAElC,MAAM,YAAY,CAAC,wCAAwC,EAAE,wDAAwD,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QACpI,MAAM,YAAY,CAAC,0CAA0C,EAAE,8CAA8C,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QAC5H,MAAM,YAAY,CAAC,0CAA0C,EAAE,oDAAoD,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;QAClI,MAAM,YAAY,CAAC,yDAAyD,EAAE,iFAAiF,EAAE,EAAE,EAAE,OAAO,CAAC,CAAC;IAC/K,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,eAAe,EAAE,KAAK,IAAI,EAAE;QAChC,MAAM,YAAY,CAAC,+DAA+D,EAAE,iEAAiE,CAAC,CAAC;QACvJ,MAAM,YAAY,CAAC,2EAA2E,EAAE,+EAA+E,CAAC,CAAC;IAClL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;QACvC,MAAM,YAAY,CAAC,iEAAiE,EAAE,uEAAuE,CAAC,CAAC;IAChK,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QAC5B,MAAM,YAAY,CAAC,sCAAsC,EAAE,sCAAsC,CAAC,CAAC;IACpG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE;QAC5B,MAAM,YAAY,CACjB;YACC,QAAQ;YACR,QAAQ;YACR,SAAS;YACT,EAAE;YACF,QAAQ;YACR,EAAE;YACF,cAAc;YACd,2BAA2B;YAC3B,yBAAyB;YACzB,aAAa;YACb,EAAE;YACF,mDAAmD;YACnD,aAAa;YACb,eAAe;YACf,EAAE;YACF,EAAE;YACF,EAAE;YACF,iBAAiB;YACjB,EAAE;YACF,SAAS;SACT,CAAC,IAAI,CAAC,IAAI,CAAC,EACZ;YACC,QAAQ;YACR,EAAE;YACF,QAAQ;YACR,SAAS;YACT,EAAE;YACF,QAAQ;YACR,EAAE;YACF,YAAY;YACZ,uBAAuB;YACvB,qBAAqB;YACrB,WAAW;YACX,EAAE;YACF,iDAAiD;YACjD,SAAS;YACT,aAAa;YACb,EAAE;YACF,EAAE;YACF,EAAE;YACF,SAAS;YACT,EAAE;YACF,SAAS;SACT,CAAC,IAAI,CAAC,IAAI,CAAC,CACZ,CAAC;IACH,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QACzB,MAAM,OAAO,GAAG;YACf,IAAI,EAAE;gBACL,MAAM,EAAE;oBACP,kBAAkB,EAAE,UAAU;iBAC9B;aACD;SACD,CAAC;QAEF,MAAM,OAAO,GAAG;YACf,QAAQ;YACR,EAAE;YACF,QAAQ;YACR,kDAAkD;YAClD,eAAe;YACf,SAAS;YACT,EAAE;YACF,SAAS;SACT,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,MAAM,QAAQ,GAAG;YAChB,QAAQ;YACR,EAAE;YACF,QAAQ;YACR,gDAAgD;YAChD,eAAe;YACf,SAAS;YACT,EAAE;YACF,SAAS;SACT,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEb,MAAM,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;AAEJ,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;uBAcmB", "file": "formatting.test.js", "sourceRoot": "../../src/"}