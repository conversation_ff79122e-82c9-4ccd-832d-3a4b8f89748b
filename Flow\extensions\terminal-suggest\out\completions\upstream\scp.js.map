{"version": 3, "sources": ["completions/upstream/scp.ts"], "names": [], "mappings": ";;AAAA,+BAAgD;AAEhD,MAAM,cAAc,GAAa;IAChC,IAAI,EAAE,KAAK;IACX,WAAW,EAAE,wDAAwD;IACrE,IAAI,EAAE;QACL;YACC,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,yDAAyD;YACtE,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE;gBACX,gBAAU;gBACV,iBAAW;gBACX,EAAE,QAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE;aACjD;SACD;QACD;YACC,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,yDAAyD;YACtE,UAAU,EAAE;gBACX,gBAAU;gBACV,iBAAW;gBACX,EAAE,QAAQ,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE;aACjD;SACD;KACD;IACD,OAAO,EAAE;QACR;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE;;;;gDAIgC;SAC7C;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,uCAAuC;SACpD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,uCAAuC;SACpD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,+GAA+G;SAChH;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,mEAAmE;SACpE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,wEAAwE;SACzE;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,sGAAsG;YACvG,IAAI,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,mCAAmC;aAChD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,wGAAwG;YACzG,IAAI,EAAE;gBACL,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,yBAAyB;aACtC;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,wIAAwI;YACzI,IAAI,EAAE;gBACL,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,yBAAyB;aACtC;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE;;;;;oCAKoB;YACjC,IAAI,EAAE;gBACL,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,iBAAiB;aAC9B;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE,gDAAgD;YAC7D,IAAI,EAAE;gBACL,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,2BAA2B;aACxC;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE;;;;cAIF;YACX,IAAI,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE;oBACZ,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,WAAW,EAAE;oBACrB,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,iCAAiC,EAAE;oBAC3C,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAClB,EAAE,IAAI,EAAE,SAAS,EAAE;oBACnB,EAAE,IAAI,EAAE,qBAAqB,EAAE;oBAC/B,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,kBAAkB,EAAE;oBAC5B,EAAE,IAAI,EAAE,oBAAoB,EAAE;oBAC9B,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,2BAA2B,EAAE;oBACrC,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,yBAAyB,EAAE;oBACnC,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,UAAU,EAAE;oBACpB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,OAAO,EAAE;oBACjB,EAAE,IAAI,EAAE,8BAA8B,EAAE;oBACxC,EAAE,IAAI,EAAE,uBAAuB,EAAE;oBACjC,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,UAAU,EAAE;oBACpB,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,kCAAkC,EAAE;oBAC5C,EAAE,IAAI,EAAE,yBAAyB,EAAE;oBACnC,EAAE,IAAI,EAAE,wBAAwB,EAAE;oBAClC,EAAE,IAAI,EAAE,oBAAoB,EAAE;oBAC9B,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBAC1B,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,0BAA0B,EAAE;oBACpC,EAAE,IAAI,EAAE,UAAU,EAAE;oBACpB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,sBAAsB,EAAE;oBAChC,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,yBAAyB,EAAE;oBACnC,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,SAAS,EAAE;oBACnB,EAAE,IAAI,EAAE,qBAAqB,EAAE;oBAC/B,EAAE,IAAI,EAAE,qBAAqB,EAAE;oBAC/B,EAAE,IAAI,EAAE,uBAAuB,EAAE;oBACjC,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAClB,EAAE,IAAI,EAAE,cAAc,EAAE;oBACxB,EAAE,IAAI,EAAE,mBAAmB,EAAE;oBAC7B,EAAE,IAAI,EAAE,MAAM,EAAE;oBAChB,EAAE,IAAI,EAAE,oBAAoB,EAAE;oBAC9B,EAAE,IAAI,EAAE,kBAAkB,EAAE;oBAC5B,EAAE,IAAI,EAAE,eAAe,EAAE;oBACzB,EAAE,IAAI,EAAE,eAAe,EAAE;iBACzB;aACD;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE;;wDAEwC;YACrD,IAAI,EAAE;gBACL,IAAI,EAAE,MAAM;aACZ;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,8EAA8E;SAC/E;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,gGAAgG;SACjG;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,8GAA8G;SAC/G;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,kGAAkG;YACnG,IAAI,EAAE;gBACL,IAAI,EAAE,SAAS;aACf;SACD;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EAAE;;;;;;;;UAQN;SACP;QACD;YACC,IAAI,EAAE,IAAI;YACV,WAAW,EACV,4KAA4K;SAC7K;KACD;CACD,CAAC;AAEF,kBAAe,cAAc,CAAC", "file": "scp.js", "sourceRoot": "../../../src/"}