{"version": 3, "sources": ["languageFeatures/hover.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEhG,4BAWC;AAlFD,+CAAiC;AAEjC,4DAA8F;AAC9F,wEAA8F;AAE9F,wDAA+D;AAC/D,kEAAoD;AAKpD,MAAM,uBAAuB;IAE5B,YACkB,MAAgC,EAChC,wBAAkD;QADlD,WAAM,GAAN,MAAM,CAA0B;QAChC,6BAAwB,GAAxB,wBAAwB,CAA0B;IAChE,CAAC;IAEE,KAAK,CAAC,YAAY,CACxB,QAA6B,EAC7B,QAAyB,EACzB,KAA+B;QAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACf,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE;YAC7D,MAAM,IAAI,CAAC,wBAAwB,CAAC,8BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAEpF,MAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACnF,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,KAAK,CACtB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,EACnE,cAAc,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACpD,CAAC;IAEO,WAAW,CAClB,QAAoB,EACpB,IAAiC,EACjC,MAA8B;QAE9B,MAAM,KAAK,GAA4B,EAAE,CAAC;QAE1C,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,YAAY,GAAa,EAAE,CAAC;YAElC,IAAI,MAAM,KAAK,8BAAU,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,QAAQ,EAAE,oCAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/G,YAAY,CAAC,IAAI,CAChB,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;oBACb,OAAO,EAAE,cAAc;oBACvB,OAAO,EAAE,CAAC,sEAAsE,CAAC;iBACjF,CAAC,CAAC,CAAC;YACN,CAAC;YAED,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;QAC/F,CAAC;QACD,MAAM,EAAE,GAAG,IAAA,uCAAuB,EAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QACzF,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,OAAO,KAAK,CAAC;IACd,CAAC;CACD;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,MAAgC,EAChC,wBAAkD;IAElD,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,6CAAqB,EAAC,MAAM,EAAE,oCAAgB,CAAC,cAAc,EAAE,oCAAgB,CAAC,QAAQ,CAAC;KACzF,EAAE,GAAG,EAAE;QACP,OAAO,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,QAAQ,CAAC,MAAM,EAC5D,IAAI,uBAAuB,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "hover.js", "sourceRoot": "../../src/"}