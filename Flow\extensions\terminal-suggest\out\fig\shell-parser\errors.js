"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConvertCommandError = exports.SubstituteAliasError = void 0;
const errors_1 = require("../shared/errors");
exports.SubstituteAliasError = (0, errors_1.createErrorInstance)('SubstituteAliasError');
exports.ConvertCommandError = (0, errors_1.createErrorInstance)('ConvertCommandError');
//# sourceMappingURL=errors.js.map