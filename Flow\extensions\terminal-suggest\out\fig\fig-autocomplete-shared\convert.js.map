{"version": 3, "sources": ["fig/fig-autocomplete-shared/convert.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAqDhG,8CAqBC;AAxED,mCAAoC;AAoBpC,MAAM,YAAY,GAAG,CAA+B,KAAsB,EAAqB,EAAE;IAChG,MAAM,WAAW,GAAsB,EAAE,CAAC;IAC1C,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,OAAO,WAAW,CAAC;IACpB,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1C,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC9B,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,WAAW,CAAC;AACpB,CAAC,CAAC;AAQF,SAAS,aAAa,CACrB,MAAkB,EAClB,UAAiE;IAEjE,OAAO;QACN,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;QAC5B,IAAI,EAAE,IAAA,iBAAS,EAAC,MAAM,CAAC,IAAI,CAAC;QAC5B,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,iBAAS,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;KACnE,CAAC;AACH,CAAC;AAED,SAAgB,iBAAiB,CAChC,UAA0B,EAC1B,UAAmD;IAEnD,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC;IAClD,OAAO;QACN,GAAG,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC;QACpC,IAAI,EAAE,IAAA,iBAAS,EAAC,UAAU,CAAC,IAAI,CAAC;QAChC,WAAW,EAAE,YAAY,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;QACpF,OAAO,EAAE,YAAY,CACpB,OAAO;YACN,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC;YAC1C,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CACrD;QACD,iBAAiB,EAAE,YAAY,CAC9B,OAAO;YACN,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC;YACzC,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CACrD;QACD,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAA,iBAAS,EAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;KACrD,CAAC;AACH,CAAC", "file": "convert.js", "sourceRoot": "../../../src/"}