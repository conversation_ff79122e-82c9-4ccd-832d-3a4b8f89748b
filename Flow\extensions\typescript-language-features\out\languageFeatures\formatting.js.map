{"version": 3, "sources": ["languageFeatures/formatting.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFhG,4BAeC;AAjGD,+CAAiC;AAIjC,kEAAoD;AAGpD,wEAAmG;AAEnG,MAAM,4BAA4B;IACjC,YACkB,MAAgC,EAChC,wBAAkD;QADlD,WAAM,GAAN,MAAM,CAA0B;QAChC,6BAAwB,GAAxB,wBAAwB,CAA0B;IAChE,CAAC;IAEE,KAAK,CAAC,mCAAmC,CAC/C,QAA6B,EAC7B,KAAmB,EACnB,OAAiC,EACjC,KAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAEzF,MAAM,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACvE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAClE,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAChE,CAAC;IAEM,KAAK,CAAC,4BAA4B,CACxC,QAA6B,EAC7B,QAAyB,EACzB,EAAU,EACV,OAAiC,EACjC,KAA+B;QAE/B,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,IAAI,CAAC,wBAAwB,CAAC,0BAA0B,CAAC,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAEzF,MAAM,IAAI,GAAiC;YAC1C,GAAG,cAAc,CAAC,QAAQ,CAAC,yBAAyB,CAAC,IAAI,EAAE,QAAQ,CAAC;YACpE,GAAG,EAAE,EAAE;SACP,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QACvE,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,MAAM,GAAsB,EAAE,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YAC7B,uEAAuE;YACvE,mGAAmG;YACnG,kCAAkC;YAClC,IAAI,KAAK,CAAC,KAAK,CAAC,SAAS,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,KAAK,EAAE,EAAE,CAAC;gBACnG,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;gBACrD,qGAAqG;gBACrG,4DAA4D;gBAC5D,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;oBACnE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACvB,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvB,CAAC;QACF,CAAC;QACD,OAAO,MAAM,CAAC;IACf,CAAC;CACD;AAED,SAAgB,QAAQ,CACvB,QAA0B,EAC1B,QAA6B,EAC7B,MAAgC,EAChC,wBAAkD;IAElD,OAAO,IAAA,+CAAuB,EAAC;QAC9B,IAAA,kDAA0B,EAAC,QAAQ,CAAC,EAAE,EAAE,eAAe,CAAC;KACxD,EAAE,GAAG,EAAE;QACP,MAAM,kBAAkB,GAAG,IAAI,4BAA4B,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;QAC9F,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAC5B,MAAM,CAAC,SAAS,CAAC,oCAAoC,CAAC,QAAQ,CAAC,MAAM,EAAE,kBAAkB,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,EAC1G,MAAM,CAAC,SAAS,CAAC,2CAA2C,CAAC,QAAQ,CAAC,MAAM,EAAE,kBAAkB,CAAC,CACjG,CAAC;IACH,CAAC,CAAC,CAAC;AACJ,CAAC", "file": "formatting.js", "sourceRoot": "../../src/"}