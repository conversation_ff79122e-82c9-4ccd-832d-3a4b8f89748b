"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.unusedIdentifier = exports.unreachableCode = exports.fixClassDoesntImplementInheritedAbstractMember = exports.fixMissingFunctionDeclaration = exports.addNameToNamelessParameter = exports.inferFromUsage = exports.spelling = exports.removeUnnecessaryAwait = exports.forgottenThisPropertyAccess = exports.fixImport = exports.extendsInterfaceBecomesImplements = exports.constructorForDerivedNeedSuperCall = exports.classIncorrectlyImplementsInterface = exports.classDoesntImplementInheritedAbstractMember = exports.awaitInSyncFunction = exports.annotateWithTypeFromJSDoc = exports.addMissingOverride = exports.addMissingNewOperator = exports.addMissingAwait = void 0;
exports.addMissingAwait = 'addMissingAwait';
exports.addMissingNewOperator = 'addMissingNewOperator';
exports.addMissingOverride = 'fixOverrideModifier';
exports.annotateWithTypeFromJSDoc = 'annotateWithTypeFromJSDoc';
exports.awaitInSyncFunction = 'fixAwaitInSyncFunction';
exports.classDoesntImplementInheritedAbstractMember = 'fixClassDoesntImplementInheritedAbstractMember';
exports.classIncorrectlyImplementsInterface = 'fixClassIncorrectlyImplementsInterface';
exports.constructorForDerivedNeedSuperCall = 'constructorForDerivedNeedSuperCall';
exports.extendsInterfaceBecomesImplements = 'extendsInterfaceBecomesImplements';
exports.fixImport = 'import';
exports.forgottenThisPropertyAccess = 'forgottenThisPropertyAccess';
exports.removeUnnecessaryAwait = 'removeUnnecessaryAwait';
exports.spelling = 'spelling';
exports.inferFromUsage = 'inferFromUsage';
exports.addNameToNamelessParameter = 'addNameToNamelessParameter';
exports.fixMissingFunctionDeclaration = 'fixMissingFunctionDeclaration';
exports.fixClassDoesntImplementInheritedAbstractMember = 'fixClassDoesntImplementInheritedAbstractMember';
exports.unreachableCode = 'fixUnreachableCode';
exports.unusedIdentifier = 'unusedIdentifier';
//# sourceMappingURL=fixNames.js.map