{"version": 3, "sources": ["test/unit/onEnter.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,iBAAe;AACf,+CAAiC;AACjC,4CAA6E;AAE7E,MAAM,gBAAgB,GAAG,CAAC,GAAwB,EAAgC,EAAE;IACnF,OAAO,IAAI,OAAO,CAAsB,OAAO,CAAC,EAAE;QACjD,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAAE;YACxD,IAAI,CAAC,CAAC,QAAQ,KAAK,GAAG,EAAE,CAAC;gBACxB,OAAO;YACR,CAAC;YACD,GAAG,CAAC,OAAO,EAAE,CAAC;YACd,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,IAAI,GAAG,KAAK,EAAE,QAA6B,EAAE,IAAY,EAAgC,EAAE;IAChG,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC5C,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;IACvD,MAAM,QAAQ,CAAC;IACf,OAAO,QAAQ,CAAC;AACjB,CAAC,CAAC;AAEF,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE;IAC1B,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,mEAAmE;QACnE,MAAM,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,qCAAqC,CAAE,CAAC,QAAQ,EAAE,CAAC;IACzF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,0CAA0C,EAAE,GAAG,EAAE;QACrD,OAAO,IAAA,gCAAoB,EAAC,cAAc,kBAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACrF,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,WAAW,CACjB,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAA,qBAAS,EACR,aAAa,EACb,OAAO,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2CAA2C,EAAE,GAAG,EAAE;QACtD,OAAO,IAAA,gCAAoB,EAAC,KAAK,kBAAM,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC9E,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5B,MAAM,IAAA,gBAAI,EAAC,GAAG,CAAC,CAAC;YAEhB,MAAM,CAAC,WAAW,CACjB,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAA,qBAAS,EAAC,IAAI,EACb,OAAO,EACP,IAAI,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,oDAAoD,EAAE,GAAG,EAAE;QAC/D,OAAO,IAAA,gCAAoB,EAAC,gCAAgC,kBAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACxG,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,WAAW,CACjB,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAA,qBAAS,EACR,+BAA+B,EAC/B,OAAO,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sDAAsD,EAAE,GAAG,EAAE;QACjE,OAAO,IAAA,gCAAoB,EAAC,6CAA6C,kBAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACpH,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,WAAW,CACjB,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAA,qBAAS,EACR,SAAS,EACT,WAAW,EACX,WAAW,EACX,WAAW,EACX,EAAE,EACF,GAAG,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,sDAAsD,EAAE,GAAG,EAAE;QACjE,OAAO,IAAA,gCAAoB,EAAC,uCAAuC,kBAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC9G,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,WAAW,CACjB,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAA,qBAAS,EACR,SAAS,EACT,WAAW,EACX,WAAW,EACX,KAAK,EACL,EAAE,EACF,GAAG,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iDAAiD,EAAE,GAAG,EAAE;QAC5D,OAAO,IAAA,gCAAoB,EAAC,gCAAgC,kBAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YACvG,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,WAAW,CACjB,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAA,qBAAS,EACR,SAAS,EACT,WAAW,EACX,WAAW,EACX,MAAM,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,6DAA6D,EAAE,GAAG,EAAE;QACxE,OAAO,IAAA,gCAAoB,EAAC,mBAAmB,kBAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE;YAC1F,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5B,MAAM,CAAC,WAAW,CACjB,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAA,qBAAS,EACR,kBAAkB,EAClB,OAAO,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "onEnter.test.js", "sourceRoot": "../../../src/"}