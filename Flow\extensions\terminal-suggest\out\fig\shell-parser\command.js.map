{"version": 3, "sources": ["fig/shell-parser/command.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;AAEhG,2CAAwE;AACxE,2CAAwE;AAExE,8CAA4B;AAiB5B,MAAM,iBAAiB,GAAG,CACzB,IAAc,EACd,KAAa,EACb,IAAe,EACG,EAAE;IACpB,IAAI,IAAI,CAAC,UAAU,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ;aAC9B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACrD,IAAI,CAAC,OAAO,CAAC,CAAC;QAChB,IAAI,UAAU,EAAE,CAAC;YAChB,OAAO,UAAU,CAAC;QACnB,CAAC;QACD,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAClD,CAAC;IACD,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEK,MAAM,eAAe,GAAG,CAC9B,OAAgB,EAChB,KAAa,EACb,IAAY,EACZ,YAAuB,EACf,EAAE;IACV,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAE/C,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAClC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,UAAU,CAChD,CAAC;IACF,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;IACjC,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;QACtB,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC;IACnE,CAAC;SAAM,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE,CAAC;QAC9B,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;IACzD,CAAC;SAAM,CAAC;QACP,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC;IAC/D,CAAC;IAED,OAAO;QACN,YAAY,EACX,YAAY,IAAI,IAAA,0BAAc,EAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;QAC/D,IAAI,EAAE,IAAA,0BAAc,EAAC,IAAI,EAAE,KAAK,GAAG,SAAS,EAAE,IAAI,CAAC;QACnD,IAAI;KACJ,CAAC;AACH,CAAC,CAAC;AA3BW,QAAA,eAAe,mBA2B1B;AAEF,MAAM,2BAA2B,GAAG,CAAC,IAAc,EAAW,EAAE;IAC/D,IAAI,IAAI,CAAC,IAAI,KAAK,oBAAQ,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,IAAI,+BAAmB,CAAC,yCAAyC,CAAC,CAAC;IAC1E,CAAC;IAED,MAAM,OAAO,GAAG;QACf,YAAY,EAAE,IAAI;QAClB,IAAI;QACJ,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;YACrC,YAAY,EAAE,KAAK;YACnB,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,KAAK,CAAC,SAAS;SACrB,CAAC,CAAC;KACH,CAAC;IAEF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IAC1C,IACC,CAAC,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;QAClE,QAAQ;QACR,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EACjB,CAAC;QACF,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,uBAAe,EAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC;IACD,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,IAAc,EAAE,KAAa,EAAY,EAAE,CAAC,CAAC;IACnE,GAAG,IAAI;IACP,UAAU,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK;IACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK;IAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;CACnE,CAAC,CAAC;AAEI,MAAM,eAAe,GAAG,CAC9B,OAAgB,EAChB,KAAY,EACZ,KAAa,EACH,EAAE;IACZ,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;QAC3D,MAAM,IAAI,gCAAoB,CAAC,sBAAsB,CAAC,CAAC;IACxD,CAAC;IACD,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IAEzB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;IAC9D,MAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;IAE3D,MAAM,YAAY,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC;IAC5D,MAAM,aAAa,GAAG,cAAc;QACnC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;QACtC,CAAC,CAAC,EAAE,CAAC;IAEN,MAAM,aAAa,GAAG,GAAG,YAAY,GAAG,KAAK,GAAG,aAAa,EAAE,CAAC;IAEhE,kEAAkE;IAClE,MAAM,SAAS,GAAG,aAAa,CAAC,IAAA,iBAAK,EAAC,aAAa,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvE,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,MAAM,IAAI,gCAAoB,CAAC,eAAe,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,UAAU,GAAG,2BAA2B,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAEtE,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,GAAG;QAC9B,KAAK,CAAC,IAAI,CAAC,UAAU;QACrB,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,MAAM;KACpC,CAAC;IAEF,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,6FAA6F;IAC7F,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;QACxD,MAAM,YAAY,GACjB,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ;YACnC,QAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;QACrC,cAAc,IAAI,YAAY,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,MAAM,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,cAAc,CAAC,CAAC;QAChE,gBAAgB,GAAG,YAAY,CAAC;QAChC,OAAO,EAAE,GAAG,QAAQ,EAAE,YAAY,EAAE,CAAC;IACtC,CAAC,CAAC,CAAC;IAEH,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;QACzE,MAAM,IAAI,gCAAoB,CAAC,0BAA0B,CAAC,CAAC;IAC5D,CAAC;IAED,OAAO;QACN,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,IAAI,EAAE,UAAU,CAAC,IAAI;QACrB,MAAM;KACN,CAAC;AACH,CAAC,CAAC;AAxDW,QAAA,eAAe,mBAwD1B;AAEK,MAAM,aAAa,GAAG,CAC5B,OAAgB,EAChB,YAAoB,EACpB,OAAiB,EACP,EAAE;IACZ,IAAI,QAAQ,GAAG,OAAO,CAAC;IACvB,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;IAE9B,oBAAoB;IACpB,IAAI,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC7B,OACC,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;QAC1B,IAAI;QACJ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;QAClB,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1B,CAAC;QACF,gBAAgB;QAChB,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACjE,IAAI,CAAC;YACJ,QAAQ,GAAG,IAAA,uBAAe,EAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACxD,CAAC;QAAC,OAAO,IAAI,EAAE,CAAC;YACf,sCAAsC;YACtC,6CAA6C;QAC9C,CAAC;QACD,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC1B,CAAC;IAED,OAAO,QAAQ,CAAC;AACjB,CAAC,CAAC;AA7BW,QAAA,aAAa,iBA6BxB;AAEK,MAAM,UAAU,GAAG,CACzB,MAAc,EACd,OAAiB,EACjB,WAAoB,EACH,EAAE;IACnB,MAAM,KAAK,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;IACtE,MAAM,SAAS,GAAG,IAAA,iBAAK,EAAC,MAAM,CAAC,CAAC;IAChC,MAAM,WAAW,GAAG,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,oBAAQ,CAAC,OAAO,CAAC,CAAC;IAC1E,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC;IACb,CAAC;IACD,MAAM,OAAO,GAAG,2BAA2B,CAAC,WAAW,CAAC,CAAC;IACzD,OAAO,IAAA,qBAAa,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC,CAAC;AAbW,QAAA,UAAU,cAarB;AAEF,MAAM,UAAU,GAAG;IAClB,oBAAQ,CAAC,OAAO;IAChB,oBAAQ,CAAC,iBAAiB;IAC1B,oBAAQ,CAAC,QAAQ;IACjB,oBAAQ,CAAC,QAAQ;IACjB,oBAAQ,CAAC,IAAI;IACb,oBAAQ,CAAC,OAAO;CAChB,CAAC;AAEK,MAAM,mBAAmB,GAAG,CAAC,SAAmB,EAAa,EAAE;IACrE,IAAI,SAAS,CAAC,IAAI,KAAK,oBAAQ,CAAC,OAAO,EAAE,CAAC;QACzC,OAAO,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC,CAAC;IACjD,CAAC;IACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1C,OAAO,EAAE,CAAC;IACX,CAAC;IACD,MAAM,QAAQ,GAAc,EAAE,CAAC;IAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QACvD,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAA,2BAAmB,EAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,OAAO,QAAQ,CAAC;AACjB,CAAC,CAAC;AAZW,QAAA,mBAAmB,uBAY9B;AAEK,MAAM,uBAAuB,GAAG,CACtC,MAAc,EACd,OAAiB,EACL,EAAE;IACd,MAAM,SAAS,GAAG,IAAA,iBAAK,EAAC,MAAM,CAAC,CAAC;IAChC,MAAM,QAAQ,GAAG,IAAA,2BAAmB,EAAC,SAAS,CAAC,CAAC;IAChD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAC/B,IAAA,qBAAa,EAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CACzD,CAAC;AACH,CAAC,CAAC;AATW,QAAA,uBAAuB,2BASlC", "file": "command.js", "sourceRoot": "../../../src/"}