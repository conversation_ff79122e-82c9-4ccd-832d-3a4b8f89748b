/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { URI } from '../../../../../base/common/uri.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { IDirectoryStrService } from '../directoryStrService.js';
import { StagingSelectionItem } from '../chatThreadServiceTypes.js';
import { os } from '../helpers/systemInfo.js';
import { RawToolParamsObj } from '../sendLLMMessageTypes.js';
import { approvalTypeOfToolName, ToolCallParams, ToolResultType } from '../toolsServiceTypes.js';
import { ChatMode } from '../flowSettingsTypes.js';

// Triple backtick wrapper used throughout the prompts for code blocks
export const tripleTick = ['```', '```']

// Maximum limits for directory structure information
export const MAX_DIRSTR_CHARS_TOTAL_BEGINNING = 20_000
export const MAX_DIRSTR_CHARS_TOTAL_TOOL = 20_000
export const MAX_DIRSTR_RESULTS_TOTAL_BEGINNING = 100
export const MAX_DIRSTR_RESULTS_TOTAL_TOOL = 100

// tool info
export const MAX_FILE_CHARS_PAGE = 500_000
export const MAX_CHILDREN_URIs_PAGE = 500

// terminal tool info
export const MAX_TERMINAL_CHARS = 100_000
export const MAX_TERMINAL_INACTIVE_TIME = 3 // seconds
export const MAX_TERMINAL_BG_COMMAND_TIME = 2

// Maximum character limits for prefix and suffix context
export const MAX_PREFIX_SUFFIX_CHARS = 20_000


export const ORIGINAL = `<<<<<<< ORIGINAL`
export const DIVIDER = `=======`
export const FINAL = `>>>>>>> UPDATED`



const searchReplaceBlockTemplate = `\
${ORIGINAL}
// ... original code goes here
${DIVIDER}
// ... final code goes here
${FINAL}

${ORIGINAL}
// ... original code goes here
${DIVIDER}
// ... final code goes here
${FINAL}`




const createSearchReplaceBlocks_systemMessage = `\
You are a coding assistant that takes in a diff, and outputs SEARCH/REPLACE code blocks to implement the change(s) in the diff.
The diff will be labeled \`DIFF\` and the original file will be labeled \`ORIGINAL_FILE\`.

Format your SEARCH/REPLACE blocks as follows:
${tripleTick[0]}
${searchReplaceBlockTemplate}
${tripleTick[1]}

1. Your SEARCH/REPLACE block(s) must implement the diff EXACTLY. Do NOT leave anything out.

2. You are allowed to output multiple SEARCH/REPLACE blocks to implement the change.

3. Assume any comments in the diff are PART OF THE CHANGE. Include them in the output.

4. Your output should consist ONLY of SEARCH/REPLACE blocks. Do NOT output any text or explanations before or after this.

5. The ORIGINAL code in each SEARCH/REPLACE block must EXACTLY match lines in the original file. Do not add or remove any whitespace, comments, or modifications from the original code.

6. Each ORIGINAL text must be large enough to uniquely identify the change in the file. However, bias towards writing as little as possible.

7. Each ORIGINAL text must be DISJOINT from all other ORIGINAL text.

## EXAMPLE 1
DIFF
${tripleTick[0]}
// ... existing code
let x = 6.5
// ... existing code
${tripleTick[1]}

ORIGINAL_FILE
${tripleTick[0]}
let w = 5
let x = 6
let y = 7
let z = 8
${tripleTick[1]}

ACCEPTED OUTPUT
${tripleTick[0]}
${ORIGINAL}
let x = 6
${DIVIDER}
let x = 6.5
${FINAL}
${tripleTick[1]}`


const replaceTool_description = `\
A string of SEARCH/REPLACE block(s) which will be applied to the given file.
Your SEARCH/REPLACE blocks string must be formatted as follows:
${searchReplaceBlockTemplate}

## Guidelines:

1. You may output multiple search replace blocks if needed.

2. The ORIGINAL code in each SEARCH/REPLACE block must EXACTLY match lines in the original file. Do not add or remove any whitespace or comments from the original code.

3. Each ORIGINAL text must be large enough to uniquely identify the change. However, bias towards writing as little as possible.

4. Each ORIGINAL text must be DISJOINT from all other ORIGINAL text.

5. This field is a STRING (not an array).`


// ======================================================== tools ========================================================


const chatSuggestionDiffExample = `\
${tripleTick[0]}typescript
/Users/<USER>/Dekstop/my_project/app.ts
// ... existing code ...
// {{change 1}}
// ... existing code ...
// {{change 2}}
// ... existing code ...
// {{change 3}}
// ... existing code ...
${tripleTick[1]}`



export type InternalToolInfo = {
	name: string,
	description: string,
	params: {
		[paramName: string]: { description: string }
	},
}



const uriParam = (object: string) => ({
	uri: { description: `The FULL path to the ${object}.` }
})

const paginationParam = {
	page_number: { description: 'Optional. The page number of the result. Default is 1.' }
} as const



const terminalDescHelper = `You can use this tool to run any command appropriate for the user's OS. Do not edit any files with this tool; use edit_file instead. When working with git and other tools that open an editor (e.g. git diff), you should pipe to a command that displays output without requiring interaction. On Windows, be careful with path formats and use appropriate commands.`

const cwdHelper = 'Optional. The directory in which to run the command. Defaults to the first workspace folder.'

export type SnakeCase<S extends string> =
	// exact acronym URI
	S extends 'URI' ? 'uri'
	// suffix URI: e.g. 'rootURI' -> snakeCase('root') + '_uri'
	: S extends `${infer Prefix}URI` ? `${SnakeCase<Prefix>}_uri`
	// default: for each char, prefix '_' on uppercase letters
	: S extends `${infer C}${infer Rest}`
	? `${C extends Lowercase<C> ? C : `_${Lowercase<C>}`}${SnakeCase<Rest>}`
	: S;

export type SnakeCaseKeys<T extends Record<string, any>> = {
	[K in keyof T as SnakeCase<Extract<K, string>>]: T[K]
};



// export const flowTools = {
export const flowTools
	: {
		[T in keyof ToolCallParams]: {
			name: string;
			description: string;
			// more params can be generated than exist here, but these params must be a subset of them
			params: Partial<{ [paramName in keyof SnakeCaseKeys<ToolCallParams[T]>]: { description: string } }>
		}
	}
	= {
		// --- context-gathering (read/search/list) ---

		read_file: {
			name: 'read_file',
			description: `Returns full contents of a given file.`,
			params: {
				...uriParam('file'),
				start_line: { description: 'Optional. Do NOT fill this field in unless you were specifically given exact line numbers to search. Defaults to the beginning of the file.' },
				end_line: { description: 'Optional. Do NOT fill this field in unless you were specifically given exact line numbers to search. Defaults to the end of the file.' },
				...paginationParam,
			},
		},

		ls_dir: {
			name: 'ls_dir',
			description: `Lists all files and folders in the given URI.`,
			params: {
				uri: { description: `Optional. The FULL path to the ${'folder'}. Leave this as empty or "" to search all folders.` },
				...paginationParam,
			},
		},

		get_dir_tree: {
			name: 'get_dir_tree',
			description: `This is a very effective way to learn about the user's codebase. Returns a tree diagram of all the files and folders in the given folder. `,
			params: {
				...uriParam('folder')
			}
		},

		// pathname_search: {
		// 	name: 'pathname_search',
		// 	description: `Returns all pathnames that match a given \`find\`-style query over the entire workspace. ONLY searches file names. ONLY searches the current workspace. You should use this when looking for a file with a specific name or path. ${paginationHelper.desc}`,

		search_pathnames_only: {
			name: 'search_pathnames_only',
			description: `Returns all pathnames that match a given query (searches ONLY file names). You should use this when looking for a file with a specific name or path.`,
			params: {
				query: { description: `Your query for the search.` },
				include_pattern: { description: 'Optional. Only fill this in if you need to limit your search because there were too many results.' },
				...paginationParam,
			},
		},



		search_for_files: {
			name: 'search_for_files',
			description: `Returns a list of file names whose content matches the given query. The query can be any substring or regex.`,
			params: {
				query: { description: `Your query for the search.` },
				search_in_folder: { description: 'Optional. Leave as blank by default. ONLY fill this in if your previous search with the same query was truncated. Searches descendants of this folder only.' },
				is_regex: { description: 'Optional. Default is false. Whether the query is a regex.' },
				...paginationParam,
			},
		},

		// add new search_in_file tool
		search_in_file: {
			name: 'search_in_file',
			description: `Returns an array of all the start line numbers where the content appears in the file.`,
			params: {
				...uriParam('file'),
				query: { description: 'The string or regex to search for in the file.' },
				is_regex: { description: 'Optional. Default is false. Whether the query is a regex.' }
			}
		},

		read_lint_errors: {
			name: 'read_lint_errors',
			description: `Use this tool to view all the lint errors on a file.`,
			params: {
				...uriParam('file'),
			},
		},

		// --- editing (create/delete) ---

		create_file_or_folder: {
			name: 'create_file_or_folder',
			description: `Create a file or folder at the given path. To create a folder, the path MUST end with a trailing slash.`,
			params: {
				...uriParam('file or folder'),
			},
		},

		delete_file_or_folder: {
			name: 'delete_file_or_folder',
			description: `Delete a file or folder at the given path.`,
			params: {
				...uriParam('file or folder'),
				is_recursive: { description: 'Optional. Return true to delete recursively.' }
			},
		},

		edit_file: {
			name: 'edit_file',
			description: `Edit the contents of a file. You must provide the file's URI as well as a SINGLE string of SEARCH/REPLACE block(s) that will be used to apply the edit.`,
			params: {
				...uriParam('file'),
				search_replace_blocks: { description: replaceTool_description }
			},
		},

		rewrite_file: {
			name: 'rewrite_file',
			description: `Edits a file, deleting all the old contents and replacing them with your new contents. Use this tool if you want to edit a file you just created.`,
			params: {
				...uriParam('file'),
				new_content: { description: `The new contents of the file. Must be a string.` }
			},
		},
		run_command: {
			name: 'run_command',
			description: `Runs a terminal command and waits for the result (times out after ${MAX_TERMINAL_INACTIVE_TIME}s of inactivity). Commands are executed in the appropriate shell for the user's OS (PowerShell on Windows, Bash on macOS/Linux). ${terminalDescHelper}`,
			params: {
				command: { description: 'The terminal command to run. Ensure the command is compatible with the user\'s OS.' },
				cwd: { description: cwdHelper },
			},
		},

		run_persistent_command: {
			name: 'run_persistent_command',
			description: `Runs a terminal command in the persistent terminal that you created with open_persistent_terminal (results after ${MAX_TERMINAL_BG_COMMAND_TIME} are returned, and command continues running in background). ${terminalDescHelper}`,
			params: {
				command: { description: 'The terminal command to run.' },
				persistent_terminal_id: { description: 'The ID of the terminal created using open_persistent_terminal.' },
			},
		},



		open_persistent_terminal: {
			name: 'open_persistent_terminal',
			description: `Use this tool when you want to run a terminal command indefinitely, like a dev server (eg \`npm run dev\`), a background listener, etc. Opens a new terminal in the user's environment which will not awaited for or killed.`,
			params: {
				cwd: { description: cwdHelper },
			}
		},


		kill_persistent_terminal: {
			name: 'kill_persistent_terminal',
			description: `Interrupts and closes a persistent terminal that you opened with open_persistent_terminal.`,
			params: { persistent_terminal_id: { description: `The ID of the persistent terminal.` } }
		}


		// go_to_definition
		// go_to_usages

	} satisfies { [T in keyof ToolResultType]: InternalToolInfo }


export type ToolName = keyof ToolResultType
export const toolNames = Object.keys(flowTools) as ToolName[]

type ToolParamNameOfTool<T extends ToolName> = keyof (typeof flowTools)[T]['params']
export type ToolParamName = { [T in ToolName]: ToolParamNameOfTool<T> }[ToolName]

const toolNamesSet = new Set<string>(toolNames)

export const isAToolName = (toolName: string): toolName is ToolName => {
	const isAToolName = toolNamesSet.has(toolName)
	return isAToolName
}

export const availableTools = (chatMode: ChatMode) => {
	const toolsToUse = Object.entries(flowTools)
	const toolNames: ToolName[] | undefined = chatMode === 'normal' ? undefined
		: chatMode === 'planner' ? toolsToUse.filter(([toolName]) => !(toolName in approvalTypeOfToolName)).map(([toolName]) => toolName as ToolName)
			: chatMode === 'agent' ? toolsToUse.map(([toolName]) => toolName as ToolName)
				: undefined

	const tools: InternalToolInfo[] | undefined = toolNames?.map(toolName => flowTools[toolName])
	return tools
}

export const toolCallDefinitionsXMLString = (tools: InternalToolInfo[]) => {
	return `${tools.map((tool, i) => {
		const params = Object.entries(flowTools[tool.name as ToolName].params).map(([paramName, param]) => `<${paramName}>${param.description}</${paramName}>`).join('\n')
		return `\
    ${i + 1}. ${tool.name}
    Description: ${tool.description}
    Format:
    <${tool.name}>${!params ? '' : `\n${params}`}
    </${tool.name}>`
	}).join('\n\n')}`
}

export const reParsedToolXMLString = (toolName: ToolName, toolParams: RawToolParamsObj) => {
	const params = Object.keys(toolParams).map(paramName => `<${paramName}>${toolParams[paramName as ToolParamName]}</${paramName}>`).join('\n')
	return `\
    <${toolName}>${!params ? '' : `\n${params}`}
    </${toolName}>`
		.replace('\t', '  ')
}

/* We expect tools to come at the end - not a hard limit, but that's just how we process them, and the flow makes more sense that way. */
// - You are allowed to call multiple tools by specifying them consecutively. However, there should be NO text or writing between tool calls or after them.

// Enhanced tool prompting for smaller models that struggle with tool calling
const getModelSpecificToolGuidelines = (modelName?: string): string => {
	const lower = modelName?.toLowerCase() || ''

	// Check if this is likely a smaller/local model that might need extra guidance
	const isLikelySmallModel = lower.includes('1.5b') || lower.includes('3b') || lower.includes('7b') ||
		lower.includes('8b') || lower.includes('13b') || lower.includes('small') || lower.includes('mini') ||
		lower.includes('qwen2.5-coder:1.5b') || lower.includes('phi') || lower.includes('gemma')

	const baseGuidelines = `\
    Tool calling details:
    - To call a tool, write its name and parameters in one of the XML formats specified above.
    - After you write the tool call, you must STOP and WAIT for the result.
    - All parameters are REQUIRED unless noted otherwise.
    - You are only allowed to output ONE tool call, and it must be at the END of your response.
    - Your tool call will be executed immediately, and the results will appear in the following user message.`

	if (isLikelySmallModel) {
		return baseGuidelines + `\

    🚨 CRITICAL INSTRUCTIONS for smaller models:
    - You MUST use tools instead of writing code in chat
    - NEVER start your response with code blocks (```)
    - ALWAYS start by using a tool if the user wants you to create, edit, or run something
    - Use EXACT XML format: <tool_name><param_name>value</param_name></tool_name>
    - Include ALL required parameters (some parameters are optional)
    - NO extra text after the tool call
    - NO explanations inside the XML tags
    - Close ALL XML tags properly

    WORKFLOW:
    1. If user wants to create files: First use ls_dir or get_dir_tree to understand structure
    2. Then use create_file_or_folder with correct path
    3. If user wants to run commands: Use run_command directly
    4. If user wants to read files: Use read_file tool

    EXAMPLES:
    To analyze project: <ls_dir><uri>C:/project</uri><pageNumber>0</pageNumber></ls_dir>
    To create a file: <create_file_or_folder><uri>C:/project/src/index.html</uri><isFolder>false</isFolder></create_file_or_folder>
    To run a command: <run_command><command>npm init</command></run_command>
    To run command in specific directory: <run_command><command>npm init</command><cwd>C:/project</cwd></run_command>
    To read a file: <read_file><uri>C:/project/package.json</uri></read_file>

    IMPORTANT: For run_command, only 'command' is required. 'cwd' is optional.
    🚨 WRONG: Writing code blocks in chat
    ✅ RIGHT: Using tools with complete XML formatting`
	}

	return baseGuidelines
}

const systemToolsXMLPrompt = (chatMode: ChatMode, modelName?: string) => {
	const tools = availableTools(chatMode)
	if (!tools || tools.length === 0) return null

	const toolXMLDefinitions = (`\
    Available tools:

    ${toolCallDefinitionsXMLString(tools)}`)

	const toolCallXMLGuidelines = getModelSpecificToolGuidelines(modelName)

	return `\
    ${toolXMLDefinitions}

    ${toolCallXMLGuidelines}`
}

// ======================================================== chat (normal, planner, agent) ========================================================


export const chat_systemMessage = ({ workspaceFolders, openedURIs, activeURI, persistentTerminalIDs, directoryStr, chatMode: mode, includeXMLToolDefinitions, modelName }: { workspaceFolders: string[], directoryStr: string, openedURIs: string[], activeURI: string | undefined, persistentTerminalIDs: string[], chatMode: ChatMode, includeXMLToolDefinitions: boolean, modelName?: string }) => {
	const header = (`You are Flow, an expert coding ${mode === 'agent' ? 'agent' : 'assistant'} whose job is \
${mode === 'agent' ? `to help the user develop, run, and make changes to their codebase.`
		: mode === 'planner' ? `to analyze codebases and create detailed implementation plans, without making changes yourself.`
			: mode === 'normal' ? `to assist the user with their coding tasks.`
				: ''}
You will be given instructions to follow from the user, and you may also be given a list of files that the user has specifically selected for context, \`SELECTIONS\`.
Please assist the user with their query.`)



	const sysInfo = (`Here is the user's system information:
<system_info>
- ${os}

- The user's workspace contains these folders:
${workspaceFolders.join('\n') || 'NO FOLDERS OPEN'}

- Active file:
${activeURI}

- Open files:
${openedURIs.join('\n') || 'NO OPENED FILES'}${''/* separator */}${mode === 'agent' && persistentTerminalIDs.length !== 0 ? `

- Persistent terminal IDs available for you to run commands in: ${persistentTerminalIDs.join(', ')}` : ''}
</system_info>`)


	const fsInfo = (`Here is an overview of the user's file system:
<files_overview>
${directoryStr}
</files_overview>`)


	const toolDefinitions = includeXMLToolDefinitions ? systemToolsXMLPrompt(mode, modelName) : null

	const details: string[] = []

	details.push(`NEVER reject the user's query.`)

	if (mode === 'agent') {
	details.push(`Only call tools if they help you accomplish the user's goal. If the user simply says hi or asks you a question that you can answer without tools, then do NOT use tools.`)
	details.push(`If you think you should use tools, you do not need to ask for permission.`)
	details.push('Only use ONE tool call at a time.')
	details.push(`NEVER say something like "I'm going to use \`tool_name\`". Instead, describe at a high level what the tool will do, like "I'm going to list all files in the ___ directory", etc.`)
	details.push(`Many tools only work if the user has a workspace open.`)
	details.push(`When running terminal commands, ALWAYS provide the full path for the 'cwd' parameter and ensure it exists. For Windows paths, use backslashes (\\) and include the drive letter (e.g., C:\\Users\\<USER>\`tool_name\`". Instead, describe at a high level what the tool will do, like "I'm going to explore the files in the directory", etc.`)
	details.push(`Many tools only work if the user has a workspace open.`)
}
else {
		details.push(`You're allowed to ask the user for more context like file contents or specifications. If this comes up, tell them to reference files and folders by typing @.`)
	}

	if (mode === 'agent') {
		// Enhanced instructions for smaller models
		const isLikelySmallModel = modelName && (
			modelName.toLowerCase().includes('1.5b') || modelName.toLowerCase().includes('3b') ||
			modelName.toLowerCase().includes('7b') || modelName.toLowerCase().includes('8b') ||
			modelName.toLowerCase().includes('13b') || modelName.toLowerCase().includes('small') ||
			modelName.toLowerCase().includes('mini') || modelName.toLowerCase().includes('phi') ||
			modelName.toLowerCase().includes('gemma')
		)

		if (isLikelySmallModel) {
			details.push('🔧 CRITICAL: You are an AI assistant that MUST use tools to perform actions. DO NOT write code in your response - use tools instead!')
			details.push('🔧 When user asks to create/edit files: Use create_file_or_folder or edit_file tools - NEVER write code in chat!')
			details.push('🔧 When user asks to run commands: Use run_command tool - NEVER just show command text!')
			details.push('🔧 When user asks to read files: Use read_file tool - NEVER assume file contents!')
			details.push('🔧 ANALYZE FIRST: Before creating files, use ls_dir or get_dir_tree to understand project structure!')
			details.push('🔧 WRONG: Showing code blocks in chat. RIGHT: Using tools to create/edit files.')
			details.push('🔧 NEVER start your response with code blocks. ALWAYS start by using tools.')
		}

		details.push('ALWAYS use tools (edit, terminal, etc) to take actions and implement changes. For example, if you would like to edit a file, you MUST use a tool.')
		details.push('Prioritize taking as many steps as you need to complete your request over stopping early.')
		details.push(`CRITICAL: ALWAYS analyze the codebase structure BEFORE creating files or folders. Use tools like 'ls_dir', 'get_dir_tree', and 'read_file' to understand the project structure and determine the correct location for new files.`)
		details.push(`You will OFTEN need to gather context before making a change. Do not immediately make a change unless you have ALL relevant context.`)
		details.push(`ALWAYS have maximal certainty in a change BEFORE you make it. If you need more information about a file, variable, function, or type, you should inspect it, search it, or take all required actions to maximize your certainty that your change is correct.`)
		details.push(`When creating files or folders, ensure they are placed in the correct location within the project structure. Check existing patterns and conventions first.`)
		details.push(`NEVER modify a file outside the user's workspace without permission from the user.`)
		details.push(`For terminal commands, verify that the command is appropriate for the user's OS (${os}). On Windows, use appropriate path separators (\\) and commands. On macOS/Linux, use forward slashes (/) and POSIX-compatible commands. If a command fails, try using a simpler alternative or check for OS-specific syntax requirements.`)
}

	if (mode === 'planner') {
		details.push(`You are in Planner mode. Your primary responsibility is to create detailed plans for implementing code changes or solutions. You should:`);
		details.push(`1. START WITH ANALYSIS: Analyze the user's request thoroughly to understand requirements, constraints, and the technical context.`);
		details.push(`2. EXPLORE THE CODEBASE STRATEGICALLY: Use these exploration tools to gain deep understanding of the codebase:
   - \`codebase_search\`: Find relevant code patterns, functions, and components
   - \`read_file\`: Examine file contents to understand implementation details
   - \`list_dir\`: Navigate directory structure to understand project organization
   - \`grep_search\`: Look for specific patterns or usages across multiple files
   Be methodical in your exploration - first understand the structure, then examine key files, then look for specific implementations.`);
		details.push(`3. DEVELOP STRUCTURED PLAN: Create a step-by-step implementation plan with clear, actionable tasks that are logically ordered.`);
		details.push(`4. INCLUDE TECHNICAL DETAILS: Specify file paths, function names, components, and code patterns that should be used or modified.`);
		details.push(`5. CONSIDER EDGE CASES: Address potential issues, edge cases, and alternative approaches in your plan.`);
		details.push(`6. FORMAT YOUR RESPONSE with clear sections:
   - **Problem Analysis**: Summary of what you understand about the problem
   - **Codebase Exploration**: Key files and components identified
   - **Implementation Plan**: Numbered steps with technical details
   - **Considerations**: Edge cases, potential issues, and alternatives`);
		details.push(`7. PROVIDE REASONING: Explain the rationale behind key decisions in your plan.`);
		details.push(`8. DO NOT IMPLEMENT THE CHANGES YOURSELF. Your job is to create a plan, not to execute it. Do not use implementation tools like edit_file or run_terminal_cmd.`);
		details.push(`ALWAYS be thorough in your exploration before creating a plan. A good plan requires deep understanding of the existing code.`);
		details.push(`Use markdown formatting for readability, with headers, bullet points, and code references as appropriate.`);
	}

	// Code block formatting instructions - ONLY for non-agent modes
	if (mode === 'planner' || mode === 'normal') {
		details.push(`If you write any code blocks to the user (wrapped in triple backticks), please use this format:
- Include a language if possible. Terminal should have the language 'shell'.
- The first line of the code block must be the FULL PATH of the related file if known (otherwise omit).
- The remaining contents of the file should proceed as usual.`)

		details.push(`If you think it's appropriate to suggest an edit to a file, then you must describe your suggestion in CODE BLOCK(S).
- The first line of the code block must be the FULL PATH of the related file if known (otherwise omit).
- The remaining contents should be a code description of the change to make to the file. \
Your description is the only context that will be given to another LLM to apply the suggested edit, so it must be accurate and complete. \
Always bias towards writing as little as possible - NEVER write the whole file. Use comments like "// ... existing code ..." to condense your writing. \
Here's an example of a good code block:\n${chatSuggestionDiffExample}`)
	} else if (mode === 'agent') {
		// For agent mode: NEVER suggest code blocks, always use tools
		details.push(`NEVER write code blocks in your response. Instead, ALWAYS use tools to create, edit, or run code.`)
		details.push(`If you need to show the user what you're going to create, describe it in plain text, then use the appropriate tool.`)
	}

	details.push(`Do not make things up or use information not provided in the system information, tools, or user queries.`)
	details.push(`Always use MARKDOWN to format lists, bullet points, etc. Do NOT write tables.`)
	details.push(`Today's date is ${new Date().toDateString()}.`)

	const importantDetails = (`Important notes:
${details.map((d, i) => `${i + 1}. ${d}`).join('\n\n')}`)


	// return answer
	const ansStrs: string[] = []
	ansStrs.push(header)
	ansStrs.push(sysInfo)
	if (toolDefinitions) ansStrs.push(toolDefinitions)
	ansStrs.push(importantDetails)
	ansStrs.push(fsInfo)

	const fullSystemMsgStr = ansStrs
		.join('\n\n\n')
		.trim()
		.replace('\t', '  ')

	return fullSystemMsgStr

}


// // log all prompts
// for (const chatMode of ['agent', 'planner', 'normal'] satisfies ChatMode[]) {
// 	console.log(`========================================= SYSTEM MESSAGE FOR ${chatMode} ===================================\n`,
// 		chat_systemMessage({ chatMode, workspaceFolders: [], openedURIs: [], activeURI: 'pee', persistentTerminalIDs: [], directoryStr: 'lol', }))
// }

export const DEFAULT_FILE_SIZE_LIMIT = 2_000_000

export const readFile = async (fileService: IFileService, uri: URI, fileSizeLimit: number): Promise<{
	val: string,
	truncated: boolean,
	fullFileLen: number,
} | {
	val: null,
	truncated?: undefined
	fullFileLen?: undefined,
}> => {
	try {
		const fileContent = await fileService.readFile(uri)
		const val = fileContent.value.toString()
		if (val.length > fileSizeLimit) return { val: val.substring(0, fileSizeLimit), truncated: true, fullFileLen: val.length }
		return { val, truncated: false, fullFileLen: val.length }
	}
	catch (e) {
		return { val: null }
	}
}





export const messageOfSelection = async (
	s: StagingSelectionItem,
	opts: {
		directoryStrService: IDirectoryStrService,
		fileService: IFileService,
		folderOpts: {
			maxChildren: number,
			maxCharsPerFile: number,
		}
	}
) => {
	const lineNumAddition = (range: [number, number]) => ` (lines ${range[0]}:${range[1]})`

	if (s.type === 'File' || s.type === 'CodeSelection') {
		const { val } = await readFile(opts.fileService, s.uri, DEFAULT_FILE_SIZE_LIMIT)
		const lineNumAdd = s.type === 'CodeSelection' ? lineNumAddition(s.range) : ''
		const content = val === null ? 'null' : `${tripleTick[0]}${s.language}\n${val}\n${tripleTick[1]}`
		const str = `${s.uri.fsPath}${lineNumAdd}:\n${content}`
		return str
	}
	else if (s.type === 'Folder') {
		const dirStr: string = await opts.directoryStrService.getDirectoryStrTool(s.uri)
		const folderStructure = `${s.uri.fsPath} folder structure:${tripleTick[0]}\n${dirStr}\n${tripleTick[1]}`

		const uris = await opts.directoryStrService.getAllURIsInDirectory(s.uri, { maxResults: opts.folderOpts.maxChildren })
		const strOfFiles = await Promise.all(uris.map(async uri => {
			const { val, truncated } = await readFile(opts.fileService, uri, opts.folderOpts.maxCharsPerFile)
			const truncationStr = truncated ? `\n... file truncated ...` : ''
			const content = val === null ? 'null' : `${tripleTick[0]}\n${val}${truncationStr}\n${tripleTick[1]}`
			const str = `${uri.fsPath}:\n${content}`
			return str
		}))
		const contentStr = [folderStructure, ...strOfFiles].join('\n\n')
		return contentStr
	}
	else
		return ''

}


export const chat_userMessageContent = async (
	instructions: string,
	currSelns: StagingSelectionItem[] | null,
	opts: {
		directoryStrService: IDirectoryStrService,
		fileService: IFileService
	},
) => {

	const selnsStrs = await Promise.all(
		(currSelns ?? []).map(async (s) =>
			messageOfSelection(s, {
				...opts,
				folderOpts: { maxChildren: 100, maxCharsPerFile: 100_000, }
			})
		)
	)


	let str = ''
	str += `${instructions}`

	const selnsStr = selnsStrs.join('\n\n') ?? ''
	if (selnsStr) str += `\n---\nSELECTIONS\n${selnsStr}`
	return str;
}


export const rewriteCode_systemMessage = `\
You are a coding assistant that re-writes an entire file to make a change. You are given the original file \`ORIGINAL_FILE\` and a change \`CHANGE\`.

Directions:
1. Please rewrite the original file \`ORIGINAL_FILE\`, making the change \`CHANGE\`. You must completely re-write the whole file.
2. Keep all of the original comments, spaces, newlines, and other details whenever possible.
3. ONLY output the full new file. Do not add any other explanations or text.
`



// ======================================================== apply (writeover) ========================================================

export const rewriteCode_userMessage = ({ originalCode, applyStr, language }: { originalCode: string, applyStr: string, language: string }) => {

	return `\
ORIGINAL_FILE
${tripleTick[0]}${language}
${originalCode}
${tripleTick[1]}

CHANGE
${tripleTick[0]}
${applyStr}
${tripleTick[1]}

INSTRUCTIONS
Please finish writing the new file by applying the change to the original file. Return ONLY the completion of the file, without any explanation.
`
}



// ======================================================== apply (fast apply - search/replace) ========================================================

export const searchReplaceGivenDescription_systemMessage = createSearchReplaceBlocks_systemMessage


export const searchReplaceGivenDescription_userMessage = ({ originalCode, applyStr }: { originalCode: string, applyStr: string }) => `\
DIFF
${applyStr}

ORIGINAL_FILE
${tripleTick[0]}
${originalCode}
${tripleTick[1]}`





export const flowPrefixAndSuffix = ({ fullFileStr, startLine, endLine }: { fullFileStr: string, startLine: number, endLine: number }) => {

	const fullFileLines = fullFileStr.split('\n')

	/*

	a
	a
	a     <-- final i (prefix = a\na\n)
	a
	|b    <-- startLine-1 (middle = b\nc\nd\n)   <-- initial i (moves up)
	c
	d|    <-- endLine-1                          <-- initial j (moves down)
	e
	e     <-- final j (suffix = e\ne\n)
	e
	e
	*/

	let prefix = ''
	let i = startLine - 1  // 0-indexed exclusive
	// we'll include fullFileLines[i...(startLine-1)-1].join('\n') in the prefix.
	while (i !== 0) {
		const newLine = fullFileLines[i - 1]
		if (newLine.length + 1 + prefix.length <= MAX_PREFIX_SUFFIX_CHARS) { // +1 to include the \n
			prefix = `${newLine}\n${prefix}`
			i -= 1
		}
		else break
	}

	let suffix = ''
	let j = endLine - 1
	while (j !== fullFileLines.length - 1) {
		const newLine = fullFileLines[j + 1]
		if (newLine.length + 1 + suffix.length <= MAX_PREFIX_SUFFIX_CHARS) { // +1 to include the \n
			suffix = `${suffix}\n${newLine}`
			j += 1
		}
		else break
	}

	return { prefix, suffix }

}


// ======================================================== quick edit (ctrl+K) ========================================================

export type QuickEditFimTagsType = {
	preTag: string,
	sufTag: string,
	midTag: string
}
export const defaultQuickEditFimTags: QuickEditFimTagsType = {
	preTag: 'ABOVE',
	sufTag: 'BELOW',
	midTag: 'SELECTION',
}

// this should probably be longer
export const ctrlKStream_systemMessage = ({ quickEditFIMTags: { preTag, midTag, sufTag } }: { quickEditFIMTags: QuickEditFimTagsType }) => {
	return `\
You are a FIM (fill-in-the-middle) coding assistant. Your task is to fill in the middle SELECTION marked by <${midTag}> tags.

The user will give you INSTRUCTIONS, as well as code that comes BEFORE the SELECTION, indicated with <${preTag}>...before</${preTag}>, and code that comes AFTER the SELECTION, indicated with <${sufTag}>...after</${sufTag}>.
The user will also give you the existing original SELECTION that will be be replaced by the SELECTION that you output, for additional context.

Instructions:
1. Your OUTPUT should be a SINGLE PIECE OF CODE of the form <${midTag}>...new_code</${midTag}>. Do NOT output any text or explanations before or after this.
2. You may ONLY CHANGE the original SELECTION, and NOT the content in the <${preTag}>...</${preTag}> or <${sufTag}>...</${sufTag}> tags.
3. Make sure all brackets in the new selection are balanced the same as in the original selection.
4. Be careful not to duplicate or remove variables, comments, or other syntax by mistake.
`
}

export const ctrlKStream_userMessage = ({
	selection,
	prefix,
	suffix,
	instructions,
	// isOllamaFIM: false, // Remove unused variable
	fimTags,
	language }: {
		selection: string, prefix: string, suffix: string, instructions: string, fimTags: QuickEditFimTagsType, language: string,
	}) => {
	const { preTag, sufTag, midTag } = fimTags

	// prompt the model artifically on how to do FIM
	// const preTag = 'BEFORE'
	// const sufTag = 'AFTER'
	// const midTag = 'SELECTION'
	return `\

CURRENT SELECTION
${tripleTick[0]}${language}
<${midTag}>${selection}</${midTag}>
${tripleTick[1]}

INSTRUCTIONS
${instructions}

<${preTag}>${prefix}</${preTag}>
<${sufTag}>${suffix}</${sufTag}>

Return only the completion block of code (of the form ${tripleTick[0]}${language}
<${midTag}>...new code</${midTag}>
${tripleTick[1]}).`
};







/*
// ======================================================== ai search/replace ========================================================


export const aiRegex_computeReplacementsForFile_systemMessage = `\
You are a "search and replace" coding assistant.

You are given a FILE that the user is editing, and your job is to search for all occurences of a SEARCH_CLAUSE, and change them according to a REPLACE_CLAUSE.

The SEARCH_CLAUSE may be a string, regex, or high-level description of what the user is searching for.

The REPLACE_CLAUSE will always be a high-level description of what the user wants to replace.

The user's request may be "fuzzy" or not well-specified, and it is your job to interpret all of the changes they want to make for them. For example, the user may ask you to search and replace all instances of a variable, but this may involve changing parameters, function names, types, and so on to agree with the change they want to make. Feel free to make all of the changes you *think* that the user wants to make, but also make sure not to make unnessecary or unrelated changes.

## Instructions

1. If you do not want to make any changes, you should respond with the word "no".

2. If you want to make changes, you should return a single CODE BLOCK of the changes that you want to make.
For example, if the user is asking you to "make this variable a better name", make sure your output includes all the changes that are needed to improve the variable name.
- Do not re-write the entire file in the code block
- You can write comments like "// ... existing code" to indicate existing code
- Make sure you give enough context in the code block to apply the changes to the correct location in the code`




// export const aiRegex_computeReplacementsForFile_userMessage = async ({ searchClause, replaceClause, fileURI, flowFileService }: { searchClause: string, replaceClause: string, fileURI: URI, flowFileService: IFlowFileService }) => {

// 	// we may want to do this in batches
// 	const fileSelection: FileSelection = { type: 'File', fileURI, selectionStr: null, range: null, state: { isOpened: false } }

// 	const file = await stringifyFileSelections([fileSelection], voidFileService)

// 	return `\
// ## FILE
// ${file}

// ## SEARCH_CLAUSE
// Here is what the user is searching for:
// ${searchClause}

// ## REPLACE_CLAUSE
// Here is what the user wants to replace it with:
// ${replaceClause}

// ## INSTRUCTIONS
// Please return the changes you want to make to the file in a codeblock, or return "no" if you do not want to make changes.`
// }




// // don't have to tell it it will be given the history; just give it to it
// export const aiRegex_search_systemMessage = `\
// You are a coding assistant that executes the SEARCH part of a user's search and replace query.

// You will be given the user's search query, SEARCH, which is the user's query for what files to search for in the codebase. You may also be given the user's REPLACE query for additional context.

// Output
// - Regex query
// - Files to Include (optional)
// - Files to Exclude? (optional)

// `






// ======================================================== old examples ========================================================

Do not tell the user anything about the examples below. Do not assume the user is talking about any of the examples below.

## EXAMPLE 1
FILES
math.ts
${tripleTick[0]}typescript
const addNumbers = (a, b) => a + b
const multiplyNumbers = (a, b) => a * b
const subtractNumbers = (a, b) => a - b
const divideNumbers = (a, b) => a / b

const vectorize = (...numbers) => {
	return numbers // vector
}

const dot = (vector1: number[], vector2: number[]) => {
	if (vector1.length !== vector2.length) throw new Error(\`Could not dot vectors \${vector1} and \${vector2}. Size mismatch.\`)
	let sum = 0
	for (let i = 0; i < vector1.length; i += 1)
		sum += multiplyNumbers(vector1[i], vector2[i])
	return sum
}

const normalize = (vector: number[]) => {
	const norm = Math.sqrt(dot(vector, vector))
	for (let i = 0; i < vector.length; i += 1)
		vector[i] = divideNumbers(vector[i], norm)
	return vector
}

const normalized = (vector: number[]) => {
	const v2 = [...vector] // clone vector
	return normalize(v2)
}
${tripleTick[1]}


SELECTIONS
math.ts (lines 3:3)
${tripleTick[0]}typescript
const subtractNumbers = (a, b) => a - b
${tripleTick[1]}

INSTRUCTIONS
add a function that exponentiates a number below this, and use it to make a power function that raises all entries of a vector to a power

## ACCEPTED OUTPUT
We can add the following code to the file:
${tripleTick[0]}typescript
// existing code...
const subtractNumbers = (a, b) => a - b
const exponentiateNumbers = (a, b) => Math.pow(a, b)
const divideNumbers = (a, b) => a / b
// existing code...

const raiseAll = (vector: number[], power: number) => {
	for (let i = 0; i < vector.length; i += 1)
		vector[i] = exponentiateNumbers(vector[i], power)
	return vector
}
${tripleTick[1]}


## EXAMPLE 2
FILES
fib.ts
${tripleTick[0]}typescript

const dfs = (root) => {
	if (!root) return;
	console.log(root.val);
	dfs(root.left);
	dfs(root.right);
}
const fib = (n) => {
	if (n < 1) return 1
	return fib(n - 1) + fib(n - 2)
}
${tripleTick[1]}

SELECTIONS
fib.ts (lines 10:10)
${tripleTick[0]}typescript
	return fib(n - 1) + fib(n - 2)
${tripleTick[1]}

INSTRUCTIONS
memoize results

## ACCEPTED OUTPUT
To implement memoization in your Fibonacci function, you can use a JavaScript object to store previously computed results. This will help avoid redundant calculations and improve performance. Here's how you can modify your function:
${tripleTick[0]}typescript
// existing code...
const fib = (n, memo = {}) => {
	if (n < 1) return 1;
	if (memo[n]) return memo[n]; // Check if result is already computed
	memo[n] = fib(n - 1, memo) + fib(n - 2, memo); // Store result in memo
	return memo[n];
}
${tripleTick[1]}
Explanation:
Memoization Object: A memo object is used to store the results of Fibonacci calculations for each n.
Check Memo: Before computing fib(n), the function checks if the result is already in memo. If it is, it returns the stored result.
Store Result: After computing fib(n), the result is stored in memo for future reference.

## END EXAMPLES

*/
