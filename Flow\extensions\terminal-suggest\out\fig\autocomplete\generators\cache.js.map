{"version": 3, "sources": ["fig/autocomplete/generators/cache.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAEhG,MAAM,UAAU;IAAhB;QACS,cAAS,GAAG,CAAC,CAAC;QAEd,YAAO,GAA2B,SAAS,CAAC;QAEpD,UAAK,GAAkB,SAAS,CAAC;QAEzB,kBAAa,GAAG,KAAK,CAAC;IAqD/B,CAAC;IAnDA,IAAY,UAAU;QACrB,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,GAAqB;QAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC3B,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,GAAqB;QAC7C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,OAAO,CAAC;YACnB,OAAO,IAAI,CAAC,KAAK,CAAC;QACnB,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,GAAqB,EAAE,MAAc;QAC9D,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,GAAqB,EAAE,MAAM,GAAG,CAAC;QACvD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;YAC9D,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;QACD,OAAO,IAAI,CAAC,KAAU,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,GAAqB,EAAE,KAAgB;QAClD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;QACD,QAAQ,KAAK,CAAC,QAAQ,IAAI,wBAAwB,EAAE,CAAC;YACpD,KAAK,SAAS;gBACb,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,GAAI,CAAC,CAAC;YAC1C,KAAK,wBAAwB;gBAC5B,0DAA0D;gBAC1D,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,GAAI,CAAC,CAAC;YACvC;gBACC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAC9B,CAAC;IACF,CAAC;CACD;AAED,MAAa,KAAK;IAAlB;QACS,UAAK,GAAG,IAAI,GAAG,EAA+B,CAAC;IAuBxD,CAAC;IArBA,KAAK,CAAC,KAAK,CACV,GAAW,EACX,GAAqB,EACrB,KAAgB;QAEhB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,UAAU,EAAE,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAe,CAAC;IAC7D,CAAC;IAED,YAAY,CAAI,GAAW,EAAE,MAAiB;QAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,UAAU,EAAE,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,KAAsB,CAAC;IACpD,CAAC;IAED,KAAK;QACJ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;CACD;AAxBD,sBAwBC", "file": "cache.js", "sourceRoot": "../../../../src/"}