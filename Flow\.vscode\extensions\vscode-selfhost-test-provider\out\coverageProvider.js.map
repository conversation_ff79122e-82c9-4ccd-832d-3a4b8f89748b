{"version": 3, "sources": ["coverageProvider.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,2DAA6D;AAC7D,+CAAiC;AAEjC,+DAAgG;AAEnF,QAAA,uBAAuB,GAAG,IAAI,4CAAuB,EAAE,CAAC;AAErE;;;;GAIG;AACH,MAAa,sBAAsB;IAGlC,YAA6B,IAAoB;QAApB,SAAI,GAAJ,IAAI,CAAgB;QAFhC,YAAO,GAAG,IAAI,GAAG,EAAkC,CAAC;IAEhB,CAAC;IAE/C,GAAG,CAAC,QAAyB,EAAE,IAAsB;QAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnD,IAAI,MAAM,EAAE,CAAC;YACZ,OAAO,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;QACD,oCAAoC;QACpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAClF,OAAO;QACR,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QACzC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACzB,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,GAAmB;QACtC,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;CACD;AA1BD,wDA0BC;AAED,MAAM,MAAM;IAQX,YACiB,GAAe,EAC/B,MAAc,EACG,IAAoB;QAFrB,QAAG,GAAH,GAAG,CAAY;QAEd,SAAI,GAAJ,IAAI,CAAgB;QARtC,iDAAiD;QACzC,YAAO,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAC9C,mCAAmC;QAClB,YAAO,GAAG,IAAI,GAAG,EAA0C,CAAC;QAO5E,IAAI,CAAC,SAAS,GAAG,IAAI,sCAAgB,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAEM,GAAG,CAAC,QAAyB,EAAE,IAAsB;QAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC3B,IAAI,IAAI,EAAE,CAAC;YACV,MAAM,CAAC,GAAG,IAAI,qBAAqB,EAAE,CAAC;YACtC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC;IACF,CAAC;IAEM,KAAK,CAAC,MAAM,CAAC,GAAmB;QACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5E,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC;QACrF,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACzF,CAAC;CACD;AAED,MAAM,qBAAqB;IAA3B;QACS,aAAQ,GAAG,IAAI,0CAAoB,EAAE,CAAC;IAgE/C,CAAC;IA9DO,GAAG,CAAC,QAAyB;QACnC,KAAK,MAAM,KAAK,IAAI,0CAAoB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACjE,CAAC;IACF,CAAC;IAEM,CAAC,SAAS,CAChB,GAAe,EACf,OAAyB,EACzB,MAAwC;QAExC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnC,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,EAAE,CAAC;gBAC/B,SAAS;YACV,CAAC;YAED,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACnD,IAAI,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEhE,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/C,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1D,IAAI,MAAM,EAAE,CAAC;gBACZ,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,oCAA4B,CAAC;gBAChF,MAAM,MAAM,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,SAAS,sCAA6B,CAAC;gBACvF,IAAI,CAAC,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC;oBACrF,SAAS;gBACV,CAAC;gBACD,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC7B,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;YACxB,CAAC;YAED,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7C,MAAM,IAAI,MAAM,CAAC,iBAAiB,CACjC,KAAK,CAAC,OAAO,EACb,IAAI,MAAM,CAAC,KAAK,CACf,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAC9D,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAChF,CACD,CAAC;YACH,CAAC;QACF,CAAC;IACF,CAAC;IAED;;;;;OAKG;IACI,MAAM,CACZ,GAAe,EACf,OAAyB,EACzB,MAAwC,EACxC,KAAkD;QAElD,MAAM,IAAI,GAAG,IAAI,cAAc,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC7D,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;CACD;AAED,MAAa,cAAe,SAAQ,MAAM,CAAC,YAAY;IAGtD,YACC,GAAe,EACE,OAAoD,EACpD,OAAyB,EACzB,MAAwC;QAEzD,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAJ/D,YAAO,GAAP,OAAO,CAA6C;QACpD,YAAO,GAAP,OAAO,CAAkB;QACzB,WAAM,GAAN,MAAM,CAAkC;QANnD,YAAO,GAA+B,EAAE,CAAC;IAShD,CAAC;IAEM,GAAG,CAAC,MAAgC;QAC1C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACrB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QAClC,CAAC;IACF,CAAC;IAEM,WAAW,CAAC,IAAqB;QACvC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACjC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACvE,CAAC;CACD;AAxBD,wCAwBC", "file": "coverageProvider.js", "sourceRoot": "../src/"}