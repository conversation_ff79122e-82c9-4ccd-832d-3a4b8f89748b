{"version": 3, "sources": ["test/selectionRanges.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,iBAAe;AACf,+CAAiC;AACjC,0DAA4G;AAC5G,8DAA8D;AAC9D,2CAA+C;AAE/C,KAAK,UAAU,YAAY,CAAC,OAAe,EAAE,QAA+B;IAC3E,IAAI,OAAO,GAAG,GAAG,OAAO,2BAA2B,CAAC;IAEpD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEjE,MAAM,SAAS,GAAG;QACjB,QAAQ,EAAE,EAAE;QACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;KAC7C,CAAC;IACF,MAAM,aAAa,GAAG,IAAA,gCAAgB,EAAC,EAAE,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,kCAAkB,CAAC,MAAM,EAAE,IAAA,sBAAa,GAAE,CAAC,CAAC;IAE/H,MAAM,QAAQ,GAAG,4BAAY,CAAC,MAAM,CAAC,iBAAiB,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;IAC5E,MAAM,YAAY,GAAG,MAAM,IAAA,oCAAkB,EAAC,aAAa,EAAE,QAAQ,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtG,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC3C,MAAM,WAAW,GAAuB,EAAE,CAAC;IAC3C,IAAI,IAAI,GAA+B,YAAY,CAAC,CAAC,CAAC,CAAC;IACvD,OAAO,IAAI,EAAE,CAAC;QACb,WAAW,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACtF,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;IACpB,CAAC;IAED,OAAO,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9F,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;AACxD,CAAC;AAED,KAAK,CAAC,qBAAqB,EAAE,GAAG,EAAE;IACjC,IAAI,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;QACtC,MAAM,YAAY,CAAC,mFAAmF,EAAE;YACvG,CAAC,EAAE,EAAE,GAAG,CAAC;YACT,CAAC,EAAE,EAAE,KAAK,CAAC;YACX,CAAC,EAAE,EAAE,OAAO,CAAC;YACb,CAAC,EAAE,EAAE,SAAS,CAAC;YACf,CAAC,EAAE,EAAE,WAAW,CAAC;YACjB,CAAC,EAAE,EAAE,kBAAkB,CAAC;YACxB,CAAC,EAAE,EAAE,qCAAqC,CAAC;YAC3C,CAAC,EAAE,EAAE,uCAAuC,CAAC;YAC7C,CAAC,EAAE,EAAE,wDAAwD,CAAC;YAC9D,CAAC,CAAC,EAAE,qEAAqE,CAAC;YAC1E,CAAC,CAAC,EAAE,kFAAkF,CAAC;SACvF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QAC/B,MAAM,YAAY,CAAC,mEAAmE,EAAE;YACvF,CAAC,EAAE,EAAE,MAAM,CAAC;YACZ,CAAC,EAAE,EAAE,eAAe,CAAC;YACrB,CAAC,EAAE,EAAE,kBAAkB,CAAC;YACxB,CAAC,EAAE,EAAE,oBAAoB,CAAC;YAC1B,CAAC,EAAE,EAAE,wBAAwB,CAAC;YAC9B,CAAC,EAAE,EAAE,yBAAyB,CAAC;YAC/B,CAAC,EAAE,EAAE,wCAAwC,CAAC;YAC9C,CAAC,CAAC,EAAE,qDAAqD,CAAC;YAC1D,CAAC,CAAC,EAAE,kEAAkE,CAAC;SACvE,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,gBAAgB,EAAE,KAAK,IAAI,EAAE;QACjC,MAAM,YAAY,CAAC,iCAAiC,EAAE;YACrD,CAAC,EAAE,EAAE,KAAK,CAAC;YACX,CAAC,EAAE,EAAE,YAAY,CAAC;YAClB,CAAC,EAAE,EAAE,cAAc,CAAC;YACpB,CAAC,CAAC,EAAE,oBAAoB,CAAC;YACzB,CAAC,CAAC,EAAE,wBAAwB,CAAC;YAC7B,CAAC,CAAC,EAAE,gCAAgC,CAAC;SACrC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AAGJ,CAAC,CAAC,CAAC", "file": "selectionRanges.test.js", "sourceRoot": "../../src/"}