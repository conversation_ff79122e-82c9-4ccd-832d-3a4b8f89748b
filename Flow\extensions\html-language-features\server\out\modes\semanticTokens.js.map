{"version": 3, "sources": ["modes/semanticTokens.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAgBhG,4DA+BC;AA7CD,mDAAkG;AAClG,kDAAkD;AAalD,SAAgB,wBAAwB,CAAC,aAA4B;IAEpE,+BAA+B;IAC/B,MAAM,MAAM,GAA6C,EAAE,KAAK,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC;IACtF,MAAM,cAAc,GAAwC,EAAE,CAAC;IAE/D,KAAK,MAAM,IAAI,IAAI,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC;QAChD,IAAI,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACjD,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,aAAa,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;QAC3J,CAAC;IACF,CAAC;IAED,OAAO;QACN,MAAM;QACN,KAAK,CAAC,iBAAiB,CAAC,QAAsB,EAAE,MAAgB;YAC/D,MAAM,SAAS,GAAwB,EAAE,CAAC;YAC1C,KAAK,MAAM,IAAI,IAAI,aAAa,CAAC,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBAC5B,MAAM,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBACtD,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;oBACzC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBACjD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;wBAC5B,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACvB,CAAC;gBACF,CAAC;YACF,CAAC;YACD,OAAO,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;KACD,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,UAAoB,EAAE,SAAmB;IAC/D,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC;QACpE,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;QACpC,IAAI,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC;YACrB,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC;YAC5B,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACvB,YAAY,GAAG,YAAY,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC;IACzD,CAAC;IACD,OAAO,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3C,CAAC;AAED,SAAS,iBAAiB,CAAC,MAA2B,EAAE,YAAkC;IACzF,IAAI,YAAY,EAAE,CAAC;QAClB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC5B,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC;IACF,CAAC;AACF,CAAC;AAED,SAAS,qBAAqB,CAAC,MAA2B,EAAE,gBAAsC;IACjG,IAAI,gBAAgB,EAAE,CAAC;QACtB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC5B,IAAI,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YACpC,IAAI,WAAW,EAAE,CAAC;gBACjB,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,IAAI,MAAM,GAAG,CAAC,CAAC;gBACf,OAAO,WAAW,GAAG,CAAC,EAAE,CAAC;oBACxB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC7B,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;oBAClD,CAAC;oBACD,KAAK,EAAE,CAAC;oBACR,WAAW,GAAG,WAAW,IAAI,CAAC,CAAC;gBAChC,CAAC;gBACD,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;YAC5B,CAAC;QACF,CAAC;IACF,CAAC;AACF,CAAC;AAED,SAAS,YAAY,CAAC,MAA2B,EAAE,MAA2B,EAAE,QAAsB;IAErG,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IACvH,IAAI,MAAM,EAAE,CAAC;QACZ,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC5G,CAAC;SAAM,CAAC;QACP,MAAM,GAAG,CAAC,qBAAK,CAAC,MAAM,CAAC,wBAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,wBAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;IAED,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,SAAS,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;IAErC,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;IAEjB,MAAM,aAAa,GAAa,EAAE,CAAC;IAEnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3D,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,OAAO,SAAS,IAAI,IAAA,wBAAY,EAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;YACxD,SAAS,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QAClC,CAAC;QACD,IAAI,SAAS,IAAI,IAAA,wBAAY,EAAC,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,IAAA,wBAAY,EAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;YACtJ,uBAAuB;YAEvB,IAAI,QAAQ,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;gBAC7B,QAAQ,GAAG,CAAC,CAAC;YACd,CAAC;YACD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,aAAa;YACxD,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,aAAa;YAC7D,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC1C,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY;YAC9C,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB;YAEtD,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YACtB,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC;QAC5B,CAAC;IACF,CAAC;IACD,OAAO,aAAa,CAAC;AACtB,CAAC", "file": "semanticTokens.js", "sourceRoot": "../../src/"}