"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
Object.defineProperty(exports, "__esModule", { value: true });
exports.noopLogDirectoryProvider = void 0;
exports.noopLogDirectoryProvider = new class {
    getNewLogDirectory() {
        return undefined;
    }
};
//# sourceMappingURL=logDirectoryProvider.js.map