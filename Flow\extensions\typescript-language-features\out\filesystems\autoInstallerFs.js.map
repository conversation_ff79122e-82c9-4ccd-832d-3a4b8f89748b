{"version": 3, "sources": ["filesystems/autoInstallerFs.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,mEAA6E;AAC7E,+BAAsC;AACtC,+CAAiC;AACjC,2CAAiC;AACjC,8CAA8C;AAC9C,mCAAgC;AAGhC,MAAM,YAAY,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9C,MAAM,YAAY,GAAG,IAAI,WAAW,EAAE,CAAC;AAEvC,MAAa,eAAgB,SAAQ,oBAAU;IAS9C,YACkB,MAAc;QAE/B,KAAK,EAAE,CAAC;QAFS,WAAM,GAAN,MAAM,CAAQ;QANf,kBAAa,GAAG,IAAI,GAAG,EAAgD,CAAC;QAExE,aAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,MAAM,CAAC,YAAY,EAA4B,CAAC,CAAC;QACvF,oBAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QAO9C,MAAM,KAAK,GAAG,IAAI,aAAK,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBAC/B,IAAI,EAAE,EAAE,CAAC,IAAI;gBACb,8CAA8C;gBAC9C,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;aACrC,CAAC,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,IAAI,mCAAc,CAAC;YACxC,aAAa,CAAC,IAAY,EAAE,WAA+B,EAAE,QAA4B,EAAE,QAA4B,EAAE,MAAe;gBACvI,OAAO,KAAK,CAAC,aAAa,CAAC,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YACrE,CAAC;YAED,UAAU,CAAC,IAAY;gBACtB,KAAK,CAAC,MAAM,CAAC,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9B,CAAC;YAED,eAAe,CAAC,IAAY;gBAC3B,KAAK,CAAC,eAAe,CAAC,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;YACvC,CAAC;YAED,SAAS,CAAC,IAAY,EAAE,IAAY,EAAE,mBAA6B;gBAClE,KAAK,CAAC,SAAS,CAAC,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/F,CAAC;YAED,eAAe,CAAC,IAAY;gBAC3B,IAAI,CAAC;oBACJ,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACxC,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAChD,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACZ,OAAO,KAAK,CAAC;gBACd,CAAC;YACF,CAAC;YAED,QAAQ,CAAC,IAAY,EAAE,SAAkB;gBACxC,IAAI,CAAC;oBACJ,OAAO,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC5D,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACZ,OAAO,SAAS,CAAC;gBAClB,CAAC;YACF,CAAC;SACD,CAAC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAoB;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC9E,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,GAAe;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAC;QAElD,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;QAElC,uCAAuC;QAEvC,yEAAyE;QACzE,UAAU;QACV,IAAI,IAAA,eAAQ,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,cAAc,IAAI,IAAA,eAAQ,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,CAAC;YACpF,OAAO;gBACN,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS;gBAC/B,IAAI,EAAE,CAAC;aACP,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,GAAe;QAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,GAAG,EAAE,CAAC,CAAC;QAE3D,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,gBAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAe;QAC7B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,GAAG,EAAE,CAAC,CAAC;QAEtD,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;IAED,SAAS,CAAC,IAAgB,EAAE,QAAoB,EAAE,QAAiD;QAClG,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,OAAmB,EAAE,OAAmB,EAAE,QAAgC;QAChF,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,IAAgB;QACtB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAED,eAAe,CAAC,IAAgB;QAC/B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,WAAsB;QACzD,sEAAsE;QACtE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAChD,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAC7C,CAAC;QAED,iDAAiD;QACjD,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,kCAAkC,CAAC,EAAE,CAAC;YAC5H,MAAM,MAAM,CAAC,eAAe,CAAC,YAAY,EAAE,CAAC;QAC7C,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC7D,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO;QACR,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,WAAW,CAAC,IAAI,WAAW,IAAI,EAAE,CAAC,CAAC;QAErG,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qEAAqE,IAAI,eAAe,CAAC,CAAC;YAC5G,OAAO,eAAe,CAAC;QACxB,CAAC;QAED,MAAM,UAAU,GAAG,CAAC,KAAK,IAAI,EAAE;YAC9B,IAAI,IAAqB,CAAC;YAC1B,IAAI,CAAC;gBACJ,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;YAC9G,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;gBACvE,OAAO;YACR,CAAC;YAED,IAAI,CAAC;gBACJ,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YACtB,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,WAAW,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;YACxE,CAAC;QACF,CAAC,CAAC,EAAE,CAAC;QACL,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACzC,MAAM,UAAU,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,WAAgB,EAAE,IAAY;QAC1D,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;QAEjC,iDAAiD;QACjD,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,IAAI,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEjH,IAAI,OAAO,CAAC;QACZ,IAAI,CAAC;YACJ,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,IAAI,EAAE,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAEf,IAAI,OAAO,CAAC;QACZ,IAAI,CAAC;YACJ,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,IAAI,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QAEf,OAAO;YACN,OAAO;YACP,OAAO;YACP,OAAO;SACP,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,WAAgB;QAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;QACjC,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC/D,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,CAAC,GAAG,EAAE,CAAC;YACV,OAAO;QACR,CAAC;QACD,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAA,WAAI,EAAC,GAAG,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;YACvE,OAAO,GAAG,CAAC;QACZ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACZ,OAAO;QACR,CAAC;IACF,CAAC;CACD;AA1MD,0CA0MC;AAED,MAAM,SAAS;IAId,YAAY,GAAe;QAC1B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAClE,IAAI,CAAC,KAAK,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACxB,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAClE,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,gBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;IAC9F,CAAC;IAED,IAAI,IAAI;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACzB,CAAC;IACD,IAAI,MAAM;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC3B,CAAC;IACD,IAAI,SAAS;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;IAC9B,CAAC;IACD,IAAI,QAAQ;QACX,OAAO,IAAA,WAAI,EAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;CACD", "file": "autoInstallerFs.js", "sourceRoot": "../../src/"}