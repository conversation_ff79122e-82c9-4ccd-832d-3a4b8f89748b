{"version": 3, "sources": ["tsServer/versionProvider.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AAajC,MAAa,iBAAiB;IAE7B,YACiB,MAA+B,EAC/B,IAAY,EACZ,UAA2B,EAC1B,UAAmB;QAHpB,WAAM,GAAN,MAAM,CAAyB;QAC/B,SAAI,GAAJ,IAAI,CAAQ;QACZ,eAAU,GAAV,UAAU,CAAiB;QAC1B,eAAU,GAAV,UAAU,CAAS;IACjC,CAAC;IAEL,IAAW,YAAY;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC;IAClB,CAAC;IAED,IAAW,SAAS;QACnB,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC;IACrC,CAAC;IAED,IAAW,OAAO;QACjB,OAAO,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC;IACtC,CAAC;IAEM,EAAE,CAAC,KAAwB;QACjC,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC;QACd,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,KAAK,KAAK,CAAC,UAAU,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACb,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;IAED,IAAW,WAAW;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC;QAChC,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,oDAAoD,CAAC,CAAC;IAC5G,CAAC;CACD;AAvCD,8CAuCC", "file": "versionProvider.js", "sourceRoot": "../../src/"}