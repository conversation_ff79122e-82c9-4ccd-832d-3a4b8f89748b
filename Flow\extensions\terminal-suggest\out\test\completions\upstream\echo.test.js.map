{"version": 3, "sources": ["test/completions/upstream/echo.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;AAEhG,iBAAe;AACf,2CAA2D;AAC3D,8EAA0D;AAE1D,MAAM,UAAU,GAAG;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;CACJ,CAAC;AACF,MAAM,uBAAuB,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAG,cAAgB,CAAC,WAAW,EAAE,CAAC,CAAC;AACnF,QAAA,iBAAiB,GAAe;IAC5C,IAAI,EAAE,MAAM;IACZ,eAAe,EAAE,cAAQ;IACzB,iBAAiB,EAAE,MAAM;IACzB,SAAS,EAAE;QACV,cAAc;QACd,EAAE,KAAK,EAAE,GAAG,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAE5H,qBAAqB;QACrB,EAAE,KAAK,EAAE,IAAI,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAC7H,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAC9H,EAAE,KAAK,EAAE,MAAM,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAC/H,EAAE,KAAK,EAAE,OAAO,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,mBAAS,CAAC,GAAG,EAAE,EAAE;QAEhI,gBAAgB;QAChB,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,EAAE,UAAU,EAAE;QAEpD,mBAAmB;QACnB,oGAAoG;QACpG,sFAAsF;QACtF,+FAA+F;KAC/F;CACD,CAAC", "file": "echo.test.js", "sourceRoot": "../../../../src/"}