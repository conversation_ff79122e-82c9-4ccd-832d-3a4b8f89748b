{"version": 3, "sources": ["test/unit/jsdocSnippet.test.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhG,+CAAiC;AACjC,iBAAe;AACf,+CAAiC;AACjC,8EAA4E;AAC5E,4CAAyC;AAEzC,KAAK,CAAC,yBAAyB,EAAE,GAAG,EAAE;IAErC,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,2DAA2D;QAC3D,MAAM,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,qCAAqC,CAAE,CAAC,QAAQ,EAAE,CAAC;IACzF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;QAC1D,MAAM,KAAK,GAAG,QAAQ,CAAC;QACvB,MAAM,CAAC,WAAW,CAAC,IAAA,oCAAiB,EAAC,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;QAChE,MAAM,CAAC,WAAW,CACjB,IAAA,oCAAiB,EAAC,IAAA,qBAAS,EAC1B,KAAK,EACL,KAAK,EACL,KAAK,CACL,CAAC,CAAC,KAAK,EACR,IAAA,qBAAS,EACR,KAAK,EACL,OAAO,EACP,KAAK,CACL,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;QAC/D,MAAM,CAAC,WAAW,CACjB,IAAA,oCAAiB,EAAC,IAAA,qBAAS,EAC1B,KAAK,EACL,aAAa,EACb,aAAa,EACb,KAAK,CACL,CAAC,CAAC,KAAK,EACR,IAAA,qBAAS,EACR,KAAK,EACL,kBAAkB,EAClB,kBAAkB,EAClB,KAAK,CACL,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,CAAC,WAAW,CACjB,IAAA,oCAAiB,EAAC,IAAA,qBAAS,EAC1B,KAAK,EACL,iBAAiB,EACjB,iBAAiB,EACjB,KAAK,CACL,CAAC,CAAC,KAAK,EACR,IAAA,qBAAS,EACR,KAAK,EACL,2BAA2B,EAC3B,2BAA2B,EAC3B,KAAK,CACL,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,CAAC,WAAW,CACjB,IAAA,oCAAiB,EAAC,IAAA,qBAAS,EAC1B,KAAK,EACL,KAAK,EACL,gBAAgB,EAChB,KAAK,CACL,CAAC,CAAC,KAAK,EACR,IAAA,qBAAS,EACR,KAAK,EACL,OAAO,EACP,uBAAuB,EACvB,KAAK,CACL,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC", "file": "jsdocSnippet.test.js", "sourceRoot": "../../../src/"}