{"version": 3, "sources": ["task/taskProvider.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqShG,4BAIC;AAvSD,oDAAsC;AACtC,2CAA6B;AAC7B,+CAAiC;AACjC,iDAAyC;AACzC,4DAAgF;AAChF,4CAA2C;AAC3C,8CAA8C;AAC9C,oCAAqC;AACrC,8EAA0E;AAE1E,0CAA0D;AAC1D,yDAAgE;AAGhE,IAAK,UAKJ;AALD,WAAK,UAAU;IACd,uBAAS,CAAA;IACT,yBAAW,CAAA;IACX,6BAAe,CAAA;IACf,6BAAe,CAAA;AAChB,CAAC,EALI,UAAU,KAAV,UAAU,QAKd;AAQD;;GAEG;AACH,MAAM,eAAgB,SAAQ,oBAAU;IAQvC,YACkB,MAAsC;QAEvD,KAAK,EAAE,CAAC;QAFS,WAAM,GAAN,MAAM,CAAgC;QAPvC,8BAAyB,GAAG,IAAI,CAAC;QACjC,2BAAsB,GAAG,IAAI,CAAC;QAEvC,eAAU,GAAG,UAAU,CAAC,EAAE,CAAC;QAOlC,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAgB,EAAE,CAAC;QAE/C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC,CAAC;QAC7F,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAC/B,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,KAA+B;QACxD,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;YAC9D,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,WAAW,GAAgB,IAAI,GAAG,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAkB,EAAE,CAAC;QAChC,KAAK,MAAM,OAAO,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACtC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAChC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACzD,CAAC;QACF,CAAC;QACD,OAAO,KAAK,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,IAAiB;QACzC,MAAM,UAAU,GAA6B,IAAI,CAAC,UAAU,CAAC;QAC7D,IAAI,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpD,8CAA8C;YAC9C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,2FAA2F,CAAC,CAAC,CAAC;YAC7I,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,YAAY,GAAG,UAAU,CAAC,QAAQ,CAAC;QACzC,IAAI,CAAC,YAAY,EAAE,CAAC;YACnB,OAAO,SAAS,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;YACrH,4DAA4D;YAC5D,OAAO,SAAS,CAAC;QAClB,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,YAAY,EAAE,CAAC,CAAC;QAC5F,MAAM,QAAQ,GAAa;YAC1B,GAAG,EAAE,WAAW;YAChB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,SAAS,EAAE,WAAW,CAAC,IAAI;YAC3B,eAAe,EAAE,IAAI,CAAC,KAAK;SAC3B,CAAC;QACF,OAAO,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IACnE,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,KAA+B;QAC5D,MAAM,OAAO,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC;YAClC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;YACpC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;SACnC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAEX,OAAO,OAAO,CAAC,GAAG,CACjB,OAAO,CAAC,GAAG,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE,CAAC,MAAM,IAAA,WAAM,EAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAC1E,CAAC,IAAI,CAAC,iBAAQ,CAAC,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,KAA+B;QACrE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,MAAM,EAAE,CAAC;YACZ,IAAI,IAAA,wCAAkB,EAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClD,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC;gBAChC,OAAO,CAAC;wBACP,GAAG;wBACH,MAAM,EAAE,GAAG,CAAC,MAAM;wBAClB,SAAS,EAAE,GAAG,CAAC,IAAI;wBACnB,eAAe,EAAE,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC;qBACzD,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC5C,IAAI,CAAC,IAAI,EAAE,CAAC;YACX,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CACxB,aAAa,EACb,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,EACjC,KAAK,CAAC;YACP,IAAI,OAAO,CAAkC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,kCAAc,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC;SAC5I,CAAC,CAAC;QACH,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,EAAE,CAAC;QACX,CAAC;QAED,MAAM,EAAE,cAAc,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC;QACzC,IAAI,cAAc,IAAI,CAAC,IAAA,sCAA2B,EAAC,cAAc,CAAC,EAAE,CAAC;YACpE,MAAM,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;YAC5D,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAClD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACxD,OAAO,CAAC;oBACP,GAAG;oBACH,MAAM,EAAE,oBAAoB;oBAC5B,SAAS,EAAE,GAAG,CAAC,IAAI;oBACnB,eAAe,EAAE,MAAM;iBACvB,CAAC,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,CAAC;IACX,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAA+B;QACpE,MAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QAC/D,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;QAEhE,OAAO,OAAO,CAAC,IAAI,CAAC;YACnB,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC9F,IAAA,gBAAI,EAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC3C,iBAAiB,CAAC,MAAM,EAAE,CAAC;gBAC3B,OAAO,EAAE,CAAC;YACX,CAAC,CAAC;SACF,CAAC,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,OAAiB;QAChD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACvF,IAAI,QAAQ,EAAE,CAAC;gBACd,OAAO,QAAQ,CAAC;YACjB,CAAC;YAED,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjG,IAAI,YAAY,EAAE,CAAC;gBAClB,OAAO,YAAY,CAAC;YACrB,CAAC;QACF,CAAC;QAED,yBAAyB;QACzB,OAAO,KAAK,CAAC;IACd,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QACxD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAClC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;QAC1D,IAAI,QAAQ,KAAK,OAAO,IAAI,MAAM,IAAA,WAAM,EAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;YACtF,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAClC,CAAC;aAAM,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,QAAQ,CAAC,IAAI,MAAM,IAAA,WAAM,EAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACpH,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9B,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,uBAAuB;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,MAAM,EAAE,CAAC;YACZ,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,YAAY,IAAI,QAAQ,CAAC,UAAU,KAAK,iBAAiB,CAAC,EAAE,CAAC;gBACrG,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACrD,CAAC;QACF,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,YAAY,CAAC,eAAmD,EAAE,KAAa,EAAE,OAAe,EAAE,IAAc,EAAE,mBAA6C;QACtK,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,IAAI,CAChC,mBAAmB,EACnB,eAAe,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,KAAK,CAAC,EACnC,KAAK,EACL,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,EACxC,MAAM,CAAC,CAAC;QACT,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;QACzC,SAAS,CAAC,YAAY,GAAG,KAAK,CAAC;QAC/B,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,YAAY,CAAC,eAAmD,EAAE,KAAa,EAAE,OAAe,EAAE,IAAc,EAAE,mBAA6C;QACtK,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,IAAI,CAChC,mBAAmB,EACnB,eAAe,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAC7C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE,KAAK,CAAC,EACnC,KAAK,EACL,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,SAAS,CAAC,CAAC,EACxD,YAAY,CAAC,CAAC;QACf,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;QACzC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;QAC9B,OAAO,SAAS,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,OAAiB;QACjD,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE7C,MAAM,KAAK,GAAkB,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,EAAE,EAAE,CAAC;YAC/E,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;QACvH,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,UAAU,KAAK,UAAU,CAAC,EAAE,EAAE,CAAC;YAC/E,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;QACxI,CAAC;QAED,OAAO,KAAK,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,+BAA+B,CAAC,OAAiB,EAAE,UAAoC;QACpG,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE7C,IAAI,IAA6B,CAAC;QAElC,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACrC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACrF,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;YAC1C,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QACrF,CAAC;QAED,OAAO,IAAI,CAAC;IACb,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,OAAiB;QAChD,MAAM,WAAW,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,CAAC;YACJ,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,QAAQ,EAAE,UAAU,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;QACF,CAAC;QAAC,MAAM,CAAC;YACR,QAAQ;QACT,CAAC;QACD,OAAO,WAAW,CAAC;IACpB,CAAC;IAEO,gBAAgB,CAAC,OAAiB;QACzC,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC7B,MAAM,sBAAsB,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0CAA0C;YAC9I,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAC5E,CAAC;QAED,OAAO,OAAO,CAAC,SAAS,CAAC;IAC1B,CAAC;IAEO,sBAAsB;QAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAa,YAAY,CAAC,CAAC;QAC/F,IAAI,CAAC,UAAU,GAAG,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IACtE,CAAC;CACD;AAED,SAAgB,QAAQ,CACvB,UAA0C;IAE1C,OAAO,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,YAAY,EAAE,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;AACzF,CAAC", "file": "taskProvider.js", "sourceRoot": "../../src/"}