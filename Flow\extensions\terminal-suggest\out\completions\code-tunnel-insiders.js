"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
const code_1 = require("./code");
const code_tunnel_1 = __importStar(require("./code-tunnel"));
const codeTunnelInsidersCompletionSpec = {
    ...code_tunnel_1.default,
    name: 'code-tunnel-insiders',
    description: 'Create a tunnel that\'s accessible on vscode.dev from anywhere, with insider features.',
    subcommands: [...code_1.codeTunnelSubcommands, code_1.extTunnelSubcommand],
    options: [
        ...code_1.commonOptions,
        ...(0, code_1.extensionManagementOptions)('code-tunnel-insiders'),
        ...(0, code_1.troubleshootingOptions)('code-tunnel-insiders'),
        ...code_1.globalTunnelOptions,
        ...code_1.codeTunnelOptions,
        ...code_tunnel_1.codeTunnelSpecOptions
    ]
};
exports.default = codeTunnelInsidersCompletionSpec;
//# sourceMappingURL=code-tunnel-insiders.js.map