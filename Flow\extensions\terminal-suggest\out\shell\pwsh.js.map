{"version": 3, "sources": ["shell/pwsh.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhG,wCAKC;AAVD,+CAAiC;AAGjC,qCAAsC;AAE/B,KAAK,UAAU,cAAc,CAAC,OAAsC,EAAE,gBAA8B;IAC1G,OAAO;QACN,GAAG,MAAM,UAAU,CAAC,OAAO,EAAE,gBAAgB,CAAC;QAC9C,GAAG,MAAM,WAAW,CAAC,OAAO,EAAE,gBAAgB,CAAC;KAC/C,CAAC;AACH,CAAC;AA8BD,MAAM,+BAA+B,GAA4D,IAAI,GAAG,CAAC;IACxG,gCAAwB,MAAM,CAAC,0BAA0B,CAAC,KAAK,CAAC;IAChE,mCAA2B,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;IACpE,iCAAyB,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;IAClE,iCAAyB,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;IAClE,0CAAiC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;IAC1E,uCAA8B,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;IACvE,kCAAyB,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;IAClE,0CAAgC,MAAM,CAAC,0BAA0B,CAAC,QAAQ,CAAC;CAC3E,CAAC,CAAC;AAEH,KAAK,UAAU,UAAU,CAAC,OAAsC,EAAE,gBAA8B;IAC/F,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAU,EAAC,8KAA8K,EAAE;QAC/M,GAAG,OAAO;QACV,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,iDAAiD;KAC9E,CAAC,CAAC;IACH,IAAI,IAAS,CAAC;IACd,IAAI,CAAC;QACJ,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACZ,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAC;QAC1C,OAAO,EAAE,CAAC;IACX,CAAC;IACD,OAAQ,IAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QAC9B,0FAA0F;QAC1F,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,WAAW,CAAC;QACzC,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;YAClB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,iBAAiB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,eAAe,GAAG,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAChD,IAAI,eAAe,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC5B,eAAe,GAAG,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;gBACtC,iBAAiB,GAAG,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;YAChE,CAAC;QACF,CAAC;QACD,OAAO;YACN,KAAK,EAAE,CAAC,CAAC,IAAI;YACb,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;YAChC,IAAI,EAAE,CAAC,OAAO;gBACb,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC,KAAK;gBACzC,CAAC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC;YAC5C,iBAAiB;SACjB,CAAC;IACH,CAAC,CAAC,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,OAAsC,EAAE,gBAA8B;IAChG,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAU,EAAC,mJAAmJ,EAAE;QACpL,GAAG,OAAO;QACV,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,iDAAiD;KAC9E,CAAC,CAAC;IACH,IAAI,IAAS,CAAC;IACd,IAAI,CAAC;QACJ,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACZ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC;QAC/C,OAAO,EAAE,CAAC;IACX,CAAC;IACD,OAAO,CACL,IAAc;SACb,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,kCAA0B,CAAC;SACpD,GAAG,CAAC,CAAC,CAAC,EAAE;QACR,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC;YAClB,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACnD,CAAC;QACD,OAAO;YACN,KAAK,EAAE,CAAC,CAAC,IAAI;YACb,MAAM,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;YAChC,IAAI,EAAE,+BAA+B,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC;SACxD,CAAC;IACH,CAAC,CAAC,CACH,CAAC;AACH,CAAC", "file": "pwsh.js", "sourceRoot": "../../src/"}