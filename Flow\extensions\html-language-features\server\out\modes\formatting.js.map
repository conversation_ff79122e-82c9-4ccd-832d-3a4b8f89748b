{"version": 3, "sources": ["modes/formatting.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;AAMhG,wBAsFC;AA1FD,mDAAyI;AACzI,4CAA0C;AAC1C,8CAAyC;AAElC,KAAK,UAAU,MAAM,CAAC,aAA4B,EAAE,QAAsB,EAAE,WAAkB,EAAE,iBAAoC,EAAE,QAA8B,EAAE,YAAyC;IACrN,MAAM,MAAM,GAAe,EAAE,CAAC;IAE9B,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC;IAC/B,IAAI,SAAS,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;IACnC,IAAI,MAAM,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,IAAI,SAAS,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;QAC/E,4DAA4D;QAC5D,MAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,wBAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7E,OAAO,IAAA,eAAK,EAAC,OAAO,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,SAAS,GAAG,aAAa,EAAE,CAAC;YACnE,SAAS,EAAE,CAAC;QACb,CAAC;QACD,WAAW,GAAG,qBAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;IAC/E,CAAC;IAGD,mGAAmG;IACnG,8CAA8C;IAC9C,kCAAkC;IAClC,0CAA0C;IAC1C,oDAAoD;IACpD,sCAAsC;IAEtC,6BAA6B;IAC7B,MAAM,SAAS,GAAG,aAAa,CAAC,eAAe,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACvE,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC;IACjC,MAAM,MAAM,GAAG,CAAC,KAAwB,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,MAAM,CAAC;IAEzF,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtD,MAAM,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9D,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,qBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;YAChH,IAAA,gBAAO,EAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACxB,CAAC;QACD,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC;QACrB,CAAC,EAAE,CAAC;IACL,CAAC;IACD,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC;IACf,CAAC;IACD,mBAAmB;IACnB,WAAW,GAAG,qBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC;IAEtD,4DAA4D;IAC5D,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAE,CAAC;IAChD,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,MAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;IAC7F,IAAI,oBAAoB,GAAG,4BAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACxE,IAAI,iBAAiB,CAAC,kBAAkB,IAAI,SAAS,KAAK,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QAClH,oBAAoB,GAAG,oBAAoB,GAAG,IAAI,CAAC;QACnD,SAAS,CAAC,IAAI,CAAC,wBAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC/C,CAAC;IACD,MAAM,WAAW,GAAG,4BAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,UAAU,EAAE,QAAQ,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;IAC5H,IAAI,CAAC;QACJ,6FAA6F;QAC7F,MAAM,sBAAsB,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,kDAAkD;QACjJ,MAAM,cAAc,GAAG,qBAAK,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,UAAU,CAAC,oBAAoB,CAAC,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC;QACrI,MAAM,cAAc,GAAG,aAAa,CAAC,eAAe,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAElF,MAAM,aAAa,GAAe,EAAE,CAAC;QAErC,KAAK,MAAM,CAAC,IAAI,cAAc,EAAE,CAAC;YAChC,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;YACpB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;gBAC5E,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;gBAC7E,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;oBAC1B,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC;YACF,CAAC;QACF,CAAC;QAED,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAChC,IAAA,gBAAO,EAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAC3B,OAAO,MAAM,CAAC;QACf,CAAC;QAED,2EAA2E;QAC3E,MAAM,aAAa,GAAG,4BAAY,CAAC,UAAU,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;QAC1E,MAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,aAAa,CAAC,MAAM,GAAG,sBAAsB,CAAC,CAAC;QAEvI,MAAM,CAAC,IAAI,CAAC,wBAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC;QAC9D,OAAO,MAAM,CAAC;IACf,CAAC;YAAS,CAAC;QACV,aAAa,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;IAC9C,CAAC;AAEF,CAAC", "file": "formatting.js", "sourceRoot": "../../src/"}