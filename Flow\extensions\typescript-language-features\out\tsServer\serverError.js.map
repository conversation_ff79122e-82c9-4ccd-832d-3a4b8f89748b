{"version": 3, "sources": ["tsServer/serverError.ts"], "names": [], "mappings": ";AAAA;;;gGAGgG;;;AAMhG,MAAa,qBAAsB,SAAQ,KAAK;IACxC,MAAM,CAAC,MAAM,CACnB,QAAgB,EAChB,OAA0B,EAC1B,QAAwB;QAExB,MAAM,YAAY,GAAG,qBAAqB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACpE,OAAO,IAAI,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;IACzI,CAAC;IAED,YACiB,QAAgB,EAChB,OAA0B,EACzB,QAAwB,EACzB,aAAiC,EACjC,WAA+B,EAC9B,cAAkC;QAEnD,KAAK,CAAC;YACL,IAAI,QAAQ,8BAA8B,OAAO,CAAC,WAAW,GAAG;YAChE,aAAa;YACb,WAAW;SACX,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAXd,aAAQ,GAAR,QAAQ,CAAQ;QAChB,YAAO,GAAP,OAAO,CAAmB;QACzB,aAAQ,GAAR,QAAQ,CAAgB;QACzB,kBAAa,GAAb,aAAa,CAAoB;QACjC,gBAAW,GAAX,WAAW,CAAoB;QAC9B,mBAAc,GAAd,cAAc,CAAoB;IAOpD,CAAC;IAED,IAAW,eAAe,KAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAE9D,IAAW,aAAa,KAAK,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;IAE5D,IAAW,SAAS;QACnB,sGAAsG;QACtG,oFAAoF;QACpF;;;;;;;UAOE;QACF,OAAO;YACN,OAAO,EAAE,IAAI,CAAC,aAAa;YAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc,IAAI,EAAE;YACzC,SAAS,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;SACxC,CAAC;IACZ,CAAC;IAED;;;OAGG;IACK,MAAM,CAAC,cAAc,CAAC,QAAwB;QACrD,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC;QACnC,IAAI,SAAS,EAAE,CAAC;YACf,MAAM,WAAW,GAAG,4BAA4B,CAAC;YACjD,IAAI,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;gBACvC,MAAM,mBAAmB,GAAG,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBACjE,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACvD,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;oBACvB,8CAA8C;oBAC9C,MAAM,KAAK,GAAG,mBAAmB,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;oBAC9D,OAAO;wBACN,OAAO,EAAE,mBAAmB,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,CAAC;wBACvD,KAAK;wBACL,cAAc,EAAE,qBAAqB,CAAC,aAAa,CAAC,KAAK,CAAC;qBAC1D,CAAC;gBACH,CAAC;YACF,CAAC;QACF,CAAC;QACD,OAAO,SAAS,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,aAAa,CAAC,OAA2B;QACvD,IAAI,CAAC,OAAO,EAAE,CAAC;YACd,OAAO,EAAE,CAAC;QACX,CAAC;QACD,MAAM,KAAK,GAAG,6DAA6D,CAAC;QAC5E,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,OAAO,IAAI,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACZ,MAAM;YACP,CAAC;YACD,iCAAiC;YACjC,6CAA6C;YAC7C,WAAW,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAC3D,CAAC;QACD,OAAO,WAAW,CAAC;IACpB,CAAC;CACD;AA7FD,sDA6FC", "file": "serverError.js", "sourceRoot": "../../src/"}